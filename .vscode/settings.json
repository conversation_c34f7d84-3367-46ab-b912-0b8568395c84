{"files.associations": {"*.tcc": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "span": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "variant": "cpp", "text_encoding": "cpp"}}