#pragma once
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef DCLIENT_SEQUENCE_NUM_SIZE
#define DCLIENT_SEQUENCE_NUM_SIZE		(16)
#endif

#ifndef DCLIENT_IP_SIZE
#define DCLIENT_IP_SIZE		(16)
#endif

#ifndef DCLIENT_VALUE_SIZE
#define DCLIENT_VALUE_SIZE	(64)
#endif

#ifndef DCLIENT_MAC_SIZE
#define DCLIENT_MAC_SIZE	(20)
#endif

#ifndef DCLIENT_MD5_SIZE
#define DCLIENT_MD5_SIZE	(36)
#endif

#ifndef DCLIENT_URL_SIZE
#define DCLIENT_URL_SIZE	256
#endif

#ifndef DCLIENT_TIME_SIZE
#define DCLIENT_TIME_SIZE	24
#endif

#ifndef DCLIENT_USER_SIZE
#define DCLIENT_USER_SIZE	32
#endif
	typedef enum
	{
		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - NULL
		*/	
		DCLIENT_IPC_NOTIFY_CONFIG_CHANGED = 24,//24

		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - DCLIENT_NOTIFY_HANDLE_FILE_INFO*
		*/	
		DCLIENT_IPC_NOTIFY_CONTACT_CHANGED,//25

		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - 0:未上线; 1:已上线 
		*note	 - 通知PHONE，Dclient刚启动，设备未连接任何服务器
		*/	
		DCLIENT_IPC_NOTIFY_CONNECT_STATUS,//26

		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - NULL
		*/	
		DCLIENT_IPC_NOTIFY_BOOTUP,//27

		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - const DCLIENT_SIP_INFO* pSipInfo	新的SIP信息
		*/	
		DCLIENT_IPC_NOTIFY_SIP_INFO_CHANGED,//28

		/**
		*@param1 - NULL
		*@param2 - NULL
		*@dat	 - const char* pstrNewDeviceName 新的设备名
		*/	
		DCLIENT_IPC_NOTIFY_DEVICE_LOCATION_CHANGED,//29

		/**
        *@param1 - NULL
        *@param2 - NULL
        *@dat	 - const DCLIENT_OWNER_MSG *pTextMsg 接收到新的文本信息
        */
        DCLIENT_IPC_NOTIFY_RECEIVE_OWNER_MESSAGE,//30
	}DCLIENT_IPC_MSG_TYPE;

	struct DCLIENT_DEVICE_INFO
	{
		DCLIENT_DEVICE_INFO()
		{
			memset(szModelName, 0, sizeof(szModelName));
			memset(szIPAddr, 0, sizeof(szIPAddr));
			memset(szMask, 0, sizeof(szMask));
			memset(szGateWay, 0, sizeof(szGateWay));
			memset(szPrimaryDNS, 0, sizeof(szPrimaryDNS));
			memset(szSecondaryDNS, 0, sizeof(szSecondaryDNS));
			memset(szMAC, 0, sizeof(szMAC));
		}

		char szModelName[DCLIENT_VALUE_SIZE];		//设备型号名称，如：C313
		char szIPAddr[DCLIENT_VALUE_SIZE];			//IP地址
		char szMask[DCLIENT_VALUE_SIZE];			//子网掩码
		char szGateWay[DCLIENT_VALUE_SIZE];			//网关
		char szPrimaryDNS[DCLIENT_VALUE_SIZE];		//第一个DNS
		char szSecondaryDNS[DCLIENT_VALUE_SIZE];	//第二个DNS
		char szMAC[DCLIENT_MAC_SIZE];				//MAC地址
	};

	typedef struct DCLIENT_NOTIFY_HANDLE_FILE_INFO_T
	{
		DCLIENT_NOTIFY_HANDLE_FILE_INFO_T()
		{
			memset(szFilePath, 0, sizeof(szFilePath));
			memset(szMD5, 0, sizeof(szMD5));
		}
		char szFilePath[DCLIENT_URL_SIZE];
		char szMD5[DCLIENT_MD5_SIZE];
	}DCLIENT_NOTIFY_HANDLE_FILE_INFO;

	struct DCLIENT_SIP_INFO
	{
		DCLIENT_SIP_INFO()
			: nServerPort(0)
		{
			memset(szServer, 0, sizeof(szServer));
			memset(szUserName, 0, sizeof(szUserName));
			memset(szPassword, 0, sizeof(szPassword));
		}

		int nServerPort;							//服务器端口
		char szServer[DCLIENT_URL_SIZE];			//服务器地址
		char szUserName[DCLIENT_VALUE_SIZE];		//SIP 账户
		char szPassword[DCLIENT_VALUE_SIZE];		//SIP 密码
	};

	typedef struct DCLIENT_OWNER_MSG_T
    {
    #define DCLIENT_OWNER_MSG_TITLE_SIZE			128
    #define DCLIENT_OWNER_MSG_CONTENT_SIZE		1024
        DCLIENT_OWNER_MSG_T()
        {
        	memset(title, 0, sizeof(title));
        	memset(content, 0, sizeof(content));
        	memset(time, 0, sizeof(time));
        }

    	char title[DCLIENT_OWNER_MSG_TITLE_SIZE];       //物业消息标题
    	char content[DCLIENT_OWNER_MSG_CONTENT_SIZE];   //物业消息主体
    	char time[DCLIENT_TIME_SIZE];/*in system time format*/
    }DCLIENT_OWNER_MSG;

	typedef struct DCLIENT_ALARM_MSG_T
	{
	#define DCLIENT_ALARM_MSG_TYPE_SIZE			256
		DCLIENT_ALARM_MSG_T()
			: nSequenceNum(0)
			, id(0)
			, alarm_code(0)
			, unAlarmLocation(0)
			, unAlarmZone(0)
			, unAlarmCustomize(0)
		{
			memset(type, 0, sizeof(type));
			memset(time, 0, sizeof(time));
			memset(from, 0, sizeof(from));
			memset(to, 0, sizeof(to));
			memset(from_name, 0, sizeof(from_name));
			memset(to_name, 0, sizeof(to_name));
		}

		unsigned int nSequenceNum;
		unsigned int id;
		unsigned int alarm_code;//9: Emergency Alarm
		unsigned int unAlarmLocation;
		unsigned int unAlarmZone;
		unsigned int unAlarmCustomize;
		char type[DCLIENT_ALARM_MSG_TYPE_SIZE];
		char time[DCLIENT_TIME_SIZE];/*in system time format*/
		char from[DCLIENT_USER_SIZE];
		char to[DCLIENT_USER_SIZE];
		char from_name[DCLIENT_USER_SIZE];
		char to_name[DCLIENT_USER_SIZE];
	}DCLIENT_ALARM_MSG;
	
	typedef struct DCLIENT_ALARM_DEAL_INFO_T
	{
	#define DCLIENT_ALARM_DEAL_USER_SIZE	24
	#define DCLIENT_ALARM_DEAL_RESULT_SIZE	512
	#define DCLIENT_ALARM_TYPE_SIZE			64
		DCLIENT_ALARM_DEAL_INFO_T()
			: id(0)
			, nAlarmCode(0)
			, unAlarmLocation(0)
			, unAlarmZone(0)
			, unAlarmCustomize(0)
		{
			memset(szUser, 0, sizeof(szUser));
			memset(szResult, 0, sizeof(szResult));
			memset(szType, 0, sizeof(szType));
			memset(szTime, 0, sizeof(szTime));
			memset(to_name, 0, sizeof(to_name));
		}
		unsigned int id;
		unsigned int nAlarmCode;
		unsigned int unAlarmLocation;
		unsigned int unAlarmZone;
		unsigned int unAlarmCustomize;
		char szUser[DCLIENT_ALARM_DEAL_USER_SIZE];
		char szResult[DCLIENT_ALARM_DEAL_RESULT_SIZE];
		char szType[DCLIENT_ALARM_TYPE_SIZE];
		char szTime[DCLIENT_TIME_SIZE];
		char to_name[DCLIENT_USER_SIZE];
	}DCLIENT_ALARM_DEAL_INFO;
#ifdef __cplusplus
}
#endif