#pragma once

class IDClientDelegate;
struct DCLIENT_DEVICE_INFO;

class IDClient
{
public:
	/*@function
	*******************************************************************
	功  能:  析构.

	参  数:

	返回值:  <无>.
	******************************************************************/
	virtual ~IDClient(){};
	
	/*@function
	*******************************************************************
	功  能:  初始化.

	参  数:	 pstrDownloadFolderPath: 下载目录路径；DClient会从服务器上下载一些文件，如地址簿等
			 pstrConfigFilePath: 配置文件路径；DClient会往该文件读写一些配置信息，设备那边需要提供一个有读写权限的空文件

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int Init(const char* pstrDownloadFolderPath, const char* pstrConfigFilePath, IDClientDelegate* pDelegate) = 0;

	/*@function
	*******************************************************************
	功  能:  反初始化.

	参  数:	

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int UnInit() = 0;


	/*@function
	*******************************************************************
	功  能:  设置设备信息.

	参  数:	 infoDevice: 设备信息，包括网络IP，MAC等

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int SetDeviceInfo(const DCLIENT_DEVICE_INFO& infoDevice) = 0;

	/*@function
	*******************************************************************
	功  能:  运行.

	参  数:	 

	返回值:  成功返回0，失败返回-1.

	备  注:  在运行前需确保已经初始化，并设置了设备信息.
	******************************************************************/
	virtual int Run() = 0;

	/*@function
	*******************************************************************
	功  能:  请求开门.

	参  数:	 pstrMAC : 请求哪台设备开门
			 pstrRelayID : 开哪个门

	返回值:  请求发送成功返回0，失败返回-1.
	******************************************************************/
	virtual int RequestOpenDoor(const char* pstrMAC, const char* pstrRelayID) = 0;

	/*@function
	*******************************************************************
	功  能:  发送告警

	参  数:	 alarmMsg : 发送的alarm消息
			

	返回值:  请求发送成功返回0，失败返回-1.
	******************************************************************/
	virtual int SendAlarm(DCLIENT_ALARM_MSG& alarmMsg, int nSendType) = 0;

	/*@function
	*******************************************************************
	功  能:  发送解除告警
	参  数:	 alarmDeal : 发送的告警解除消息
			

	返回值:  请求发送成功返回0，失败返回-1.
	------------------------------------------------------------------
	作  者:  kaijia, 2025.04.07
	******************************************************************/
	virtual int SendAlarmDeal(DCLIENT_ALARM_DEAL_INFO& alarmDeal, int nSendType) = 0;

};


#ifdef __cplusplus
extern "C" {
#endif

IDClient* GetIDClientInstance();


#ifdef __cplusplus
}
#endif
