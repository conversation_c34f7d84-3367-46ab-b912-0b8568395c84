#pragma once
#include "Def.h"

class IDClientDelegate
{
public:
	virtual ~IDClientDelegate(){};
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_NOTIFY_HANDLE_FILE_INFO* pData, int nDataSize) = 0;	
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_SIP_INFO* pSipInfo, int nDataSize) = 0;
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_OWNER_MSG* pTextMsg, int nDataSize) = 0;
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, const char* pData) = 0;
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, int nData) = 0;
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2) = 0;
	virtual void OuputLog(const char* pstrLog) = 0;	//打日志
};
