##########################################################################################
## (C)Copyright 2012-2020 Akuvox .Ltd
##
##########################################################################################

##MOD_BIN_NAME = dclient
MOD_LIB_NAME = libdclientapi.so
MOD_NAME = dclient

PWD := $(shell pwd)
export MOD_DIR := $(PWD)/../

PJ_DIR ?=

include $(PJ_DIR)/build/PROJECT.mak
include $(PJ_DIR)/build/TOOL.mak
include $(PJ_DIR)/build/MOD.mak

export CPPFLAGS := -I$(PJ_INC_DIR) -I$(MOD_INC_DIR) -I$(MOD_SRC_DIR)include -I$(PJ_INC_DIR)pjlib 
export CPPFLAGS += -I$(PJ_INC_DIR)android/controller/
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Utility 
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Event 
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Lock
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/third/curl 
export CPPFLAGS += -I$(MOD_SRC_DIR)/Logic
export CPPFLAGS += -I$(MOD_SRC_DIR)/Model
export CPPFLAGS += -I$(MOD_SRC_DIR)/Main
export CPPFLAGS += -I$(MOD_SRC_DIR)/SimpleIni
export CPPFLAGS += -I$(MOD_SRC_DIR)/../
export CPPFLAGS  +=$(INCFLAGS_RLLOG)
export CPPFLAGS += $(INCFLAGS_IPC)
#export CPPFLAGS += $(INCFLAGS_CFG)
#export CPPFLAGS += $(INCFLAGS_UI)
#export CPPFLAGS += $(INCFLAGS_DEVCTRL)
export CPPFLAGS += $(INCFLAGS_RLLIB)
export CPPFLAGS += $(INCFLAGS_TINYXML)
export CPPFLAGS += -Wall
ifneq ($(_LINUX), 1)
    export CPPFLAGS += -Os
endif

export CPPFLAGS += -DPJ_AUTOCONF=1 -DPJ_IS_BIG_ENDIAN=0 -DPJ_IS_LITTLE_ENDIAN=1

export LIBFLAGS := -shared -fPIC -Wl,-Bsymbolic
export LIBFLAGS = -L$(PJ_LIB_DIR) -L$(PJ_LIB_THIRD_DIR)
export LIBFLAGS += -lrllog -lrllib -ltinyxml 
#export LIBFLAGS += -lssl -lcrypto -lcurl
#export LIBFLAGS += -lcrypto -lcurl
export LIBFLAGS += -lcurl
#export LIBFLAGS += $(PJ_LIB_DIR)/libssl.a $(PJ_LIB_DIR)/libcrypto.a $(PJ_LIB_DIR)/libcurl.a
#export LIBFLAGS += $(PJ_LIB_DIR)/libssl.a
export JNI_PACKAGE_DIR := /atalk/com/akuvox/mobile/libcommon/wrapper/dclient/jni
export JNI_PACKAGE_NAME := com.akuvox.mobile.libcommon.wrapper.dclient.jni
export WRAP_OUTPUT_PATH := $(MOD_SRC_DIR)
export JNI_OUTPUT_PATH := $(PJ_JNI_DIR)/$(JNI_PACKAGE_DIR)

ifneq ($(PLATFORMID), $(PLATFORMID_ANDROID))
export LIBFLAGS += -lpthread
endif

ifeq ($(_LINUX), 1)
    export LIBFLAGS += -lrt
else
ifeq ($(PLATFORMID), $(PLATFORMID_GM8126))
	export LIBFLAGS += -lrt
endif
ifeq ($(PLATFORMID), $(PLATFORMID_DVFD8185))
	export LIBFLAGS += -lrt
endif
endif
export LIBFLAGS += -Wl,-rpath=/app/lib -Wl,-rpath=$(PJ_LIB_DIR) -Wl,-rpath=$(PJ_LIB_THIRD_DIR)

MOD_SRC_DIRS := $(MOD_SRC_DIR)/Logic $(MOD_SRC_DIR)/Model $(MOD_SRC_DIR)/Common/Event \
			$(MOD_SRC_DIR)/Common/Lock  $(MOD_SRC_DIR)/Common/Utility $(MOD_SRC_DIR)/Main $(MOD_SRC_DIR)/


.PHONY: all $(MOD_SRC_DIRS) clean

all: check jni $(MOD_SRC_DIRS) main 

check:
ifeq ($(PJ_DIR),)
	@echo "Build failed: PJ_DIR is NULL"
	@exit 1
endif

main: $(OBJS)
	@echo "\n"
	$(CXX) -o $(MOD_LIB_DIR)$(MOD_LIB_NAME) $(LDFLAGS) $(OBJS) $(MOD_OBJ_DIR)*.o $(LIBFLAGS)
ifneq ($(GFLAG), -g)
	$(STRIP) $(MOD_LIB_DIR)$(MOD_LIB_NAME)
endif
	cp $(MOD_LIB_DIR)$(MOD_LIB_NAME) $(PJ_LIB_DIR)$(MOD_LIB_NAME)
	mkdir -p $(PJ_MODS_REVISION_H_DIR)/$(MOD_NAME)/
	cp -f $(MOD_SRC_INC_DIR)/revision.h $(PJ_MODS_REVISION_H_DIR)/$(MOD_NAME)/

$(MOD_SRC_DIRS):
	@echo "\n"
	@$(MAKE) --directory=$@

clean: check jni_clean
	for d in $(MOD_SRC_DIRS); \
	do \
		cd $${d}; \
		$(MAKE) clean; \
		cd ..;	\
	done \

	-rm $(PJ_LIB_DIR)$(MOD_LIB_NAME)
	-rm $(MOD_LIB_DIR)$(MOD_LIB_NAME)

jni:
	@echo "----------Use Swig to create JNI------------"
	mkdir -p $(JNI_OUTPUT_PATH)
	$(MOD_DIR)/build/swig.sh

jni_clean:
	@echo "----------clean JNI------------"
	-rm -rf $(JNI_OUTPUT_PATH)/*
