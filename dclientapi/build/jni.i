%module (directors="1") dclient

%include "enumtypeunsafe.swg"

%{
	#include "../Def.h"
	#include "../IDClient.h"
	#include "../IDClientDelegate.h"
%}

/* turn on director wranning Callback */
/*%feature("director") ICbDclient;*/
%feature("director") IDClientDelegate;

//================== Mapping void * (Java to C) as jobject
%typemap(jni) void * "jobject"
%typemap(jtype) void * "Object"
%typemap(jstype) void * "Object"
%typemap(javain) void * "$javainput"
%typemap(javaout) void * { return $jnicall; }


%typemap(in) void * %{
        $1=(void *)$input;
%}
//=====================Mapping void *===============

//================== Mapping char ** (Java to C) as jobject
//for xx_wrap.cxx
%typemap(jni) char ** "jobjectArray"
//For xxJNI.java
%typemap(jtype) char ** "String []"
//For xx.java
%typemap(jstype) char ** "String []"
%typemap(javain) char ** "$javainput"
%typemap(javaout) char ** { return $jnicall; }


%typemap(in) void * %{
        $1=(void *)$input;
%}
//=====================Mapping char **===============


//================== Mapping int* (Java to C) as jintArray
%typemap(jni) int * "jintArray"
%typemap(jtype) int * "int []"
%typemap(jstype) int * "int []"
%typemap(javain) int * "$javainput"
%typemap(javaout) int * { return $jnicall; }



// (From Java to C)
%typemap(in) int * %{
        $1 = jenv->GetIntArrayElements($input,NULL);
%}

%typemap(freearg) int* %{
  jenv->ReleaseIntArrayElements($input, (jint*)$1, 0);
%}

%typemap(javadirectorin) int * "$jniinput"
//==================

%include "../Def.h"
%include "../IDClient.h"
%include "../IDClientDelegate.h"
