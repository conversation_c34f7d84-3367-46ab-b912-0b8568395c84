调用说明：

1. 准备回调类
1.1 在准备回调类前需将Def.h、IDClient.h、IDClientDelegate.h放置到项目能识别的目录下；
1.2 在项目中包含头文件 IDClient.h 和 IDClientDelegate.h；
1.3 创建 DClient 的回调类（该类的对象会在DClient初始化时使用）,该类必须继承自IDClientDelegate（接口申明在IDClientDelegate.h中）并实现IDClientDelegate中的方法；
1.3.1 回调类申明示例：
class CDClientDelegate : public IDClientDelegate
{
	virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, void* pData, int nDataSize);//响应DClient的消息通知
	virtual void OuputLog(const char* pstrLog);//打日志
}
1.3.2 DClient会向回调类通知四个消息，该四个消息的定义和具体描述在Def.h中
int CDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, void* pData, int nDataSize)
{
    switch (nMsgID)
	{
	case DCLIENT_IPC_NOTIFY_CONFIG_CHANGED:	//通知配置信息发生改变，即配置文件内容发生改变
		break;

	case DCLIENT_IPC_NOTIFY_CONTACT_CHANGED:	//通知联系人信息发生改变
		break;

	case DCLIENT_IPC_NOTIFY_CONNECT_STATUS:	//通知PHONE，Dclient刚启动，设备未连接任何服务器
		break;

	case DCLIENT_IPC_NOTIFY_BOOTUP:		//通知DClient已完成启动，整个初始化完成
		break;

	case DCLIENT_IPC_NOTIFY_SIP_USER_CHANGED:	//通知SIP账号已改变
		break;

	case DCLIENT_IPC_NOTIFY_DEVICE_LOCATION_CHANGED:	//通知设备名称已改变
		break;

	default:
		break;
	}

	return 0;
}

2. 调用DClient，必须等到设备环境完全准备好（已获取到IP地址等）才可以调用DClient的流程
2.1 定义一个回调类对象
2.2 从DClient库中获取DClient单例对象，GetDClientInstance定义在IDClient.h中
3.3 对DClient单例对象初始化
3.4 对DClient单例对象设置设备信息
3.5 运行
3.6 整个调用流程示例如下：
DCLIENT_DEVICE_INFO infoDevice;
CDClientDelegate delegateDClient;					//定义一个回调类对象
IDClient* pDClientInst = GetDClientInstance();				//从DClient库中获取DClient单例对象
pDClientInst->Init("/temp/download/", "/temp/cfg.ini", &delegateDClient);	//初始化单例对象，需要传入下载目录路径、配置文件路径及回调类对象指针
pDClientInst->SetDeviceInfo(infoDevice);					//设置设备信息
pDClientInst->Run();							//运行


3. 备注：
3.1 DClient接口类声明及说明如下：
class IDClient
{
public:
	/*@function
	*******************************************************************
	功  能:  析构.

	参  数:

	返回值:  <无>.
	******************************************************************/
	virtual ~IDClient(){};

	/*@function
	*******************************************************************
	功  能:  初始化.

	参  数:	 pstrDownloadFolderPath: 下载目录路径；DClient会从服务器上下载一些文件，如地址簿等
			 pstrConfigFilePath: 配置文件路径；DClient会往该文件读写一些配置信息，设备那边需要提供一个有读写权限的空文件

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int Init(const char* pstrDownloadFolderPath, const char* pstrConfigFilePath, IDClientDelegate* pDelegate) = 0;

	/*@function
	*******************************************************************
	功  能:  反初始化.

	参  数:	

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int UnInit() = 0;

	/*@function
	*******************************************************************
	功  能:  设置设备信息.

	参  数:	 infoDevice: 设备信息，包括网络IP，MAC等

	返回值:  成功返回0，失败返回-1.
	******************************************************************/
	virtual int SetDeviceInfo(const DCLIENT_DEVICE_INFO& infoDevice) = 0;

	/*@function
	*******************************************************************
	功  能:  运行.

	参  数:	 

	返回值:  成功返回0，失败返回-1.

	备  注:  在运行前需确保已经初始化，并设置了设备信息.
	******************************************************************/
	virtual int Run() = 0;

	/*@function
	*******************************************************************
	功  能:  请求开门.

	参  数:	 pstrMAC : 请求哪台设备开门
			 pstrRelayID : 开哪个门

	返回值:  请求发送成功返回0，失败返回-1.
	******************************************************************/
	virtual int RequestOpenDoor(const char* pstrMAC, const char* pstrRelayID) = 0;

	/*@function
	*******************************************************************
	功  能:  发送告警

	参  数:	 alarmMsg : 发送的alarm消息
			

	返回值:  请求发送成功返回0，失败返回-1.
	******************************************************************/
	virtual int SendAlarm(DCLIENT_ALARM_MSG& alarmMsg) = 0;
};

