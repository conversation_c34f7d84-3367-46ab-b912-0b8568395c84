#ifndef __DCLIENT_IPC_H__
#define __DCLIENT_IPC_H__

#include "rl_revision.h"

#define ipc_send(param1, param2, param3, param4, param5, param6)  (0)
#define RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT (1)

/*dclient to phone*/
#define MSG_D2P		0xF00000

/*phone to dclient*/
#define MSG_P2D		0xF10000

/*dclient to autop*/
#define MSG_D2A		0xF20000

/*rfid type*/
#define TYPE_RFID   0x01

/*private type*/
#define TYPE_PKEY   0x02

/*common macros*/
#define DCLIENT_TIME_SIZE				24
#define DCLIENT_BIND_CODE_SIZE					20
#define BIND_CODE_LIST_COUNT_MAX		10
#define BINDCODE_SEQUENCE_NUM_SIZE		10
#define DEVICE_ID_SIZE					24
#define DCLIENT_SIP_SIZE				20

#ifndef DCLIENT_IP_SIZE
#define DCLIENT_IP_SIZE					48
#endif

#ifndef DCLIENT_MAC_SIZE
#define DCLIENT_MAC_SIZE				20
#endif

#ifndef DCLIENT_VALUE_SIZE
#define DCLIENT_VALUE_SIZE				64
#endif

#ifndef DCLIENT_URL_SIZE						
#define DCLIENT_URL_SIZE				256
#endif

#ifndef DCLIENT_INT_SIZE
#define DCLIENT_INT_SIZE				12
#endif

#ifndef DEVICE_FWVER_SIZE
#define DEVICE_FWVER_SIZE				64
#endif
#define DCLIENT_COMMON_SIZE_32			32
#define DCLIENT_COMMON_SIZE_64			64
#define DCLIENT_COMMON_SIZE_256			256

#ifndef DCLIENT_MD5_SIZE
#define DCLIENT_MD5_SIZE				36
#endif

typedef enum
{
	DISCOVER_MATCH_FLAG_ALL = 0,
	DISCOVER_FLAG_PHONE = 0,
	DISCOVER_FLAG_DCLIENT,
	DISCOVER_MATCH_FLAG_MODEL =2,
	DISCOVER_MATCH_FLAG_TYPE = 3,
	DISCOVER_MATCH_FLAG_NONE = 4,
	DISCOVER_FLAG_AUTO,
}DISCOVER_MATCH_FLAG;

typedef enum
{
	DISCOVER_METHOD_NORMAL = 0,
	DISCOVER_METHOD_REQUEST_IP,
	DISCOVER_METHOD_REQUEST_DEVICECODE,
	DISCOVER_METHOD_REQUEST_RTSP,
	DISCOVER_METHOD_REPORT_IP,
}DISCOVER_METHOD;

typedef enum
{
	REPORT_ARMING_SYNC_MODE_NONE = 0,
	REPORT_ARMING_SYNC_MODE_NORESPONSE,
}REPORT_ARMING_SYNC_MODE;

typedef enum
{
	REPORT_ARMING_ACTION_TYPE_REBOOT = 0,
	REPORT_ARMING_ACTION_TYPE_GET,
	REPORT_ARMING_ACTION_TYPE_SETTED,
	REPORT_ARMING_ACTION_TYPE_SELF_SET,
	REPORT_ARMING_ACTION_TYPE_REPORT_SET,
	REPORT_ARMING_ACTION_TYPE_FORBID,
}REPORT_ARMING_ACTION_TYPE;

typedef enum
{
	DCLIENT_KEEP_RELAY_OPEN = 0,
	DCLIENT_KEEP_RELAY_CLOSE,
}DCLIENT_KEEP_RELAY_STATUS;

/*struct for text msg*/
typedef struct DCLIENT_TEXT_MSG_T
{
#define DCLIENT_TEXT_MSG_TITLE_SIZE			128
#define DCLIENT_TEXT_MSG_CONTENT_SIZE		1024
#define DCLIENT_USER_SIZE					32
	unsigned int nSequenceNum;//序列号，用于标识消息
	unsigned int nMsgID;
	unsigned int nMultiFlag;
	char title[DCLIENT_TEXT_MSG_TITLE_SIZE];
	char content[DCLIENT_TEXT_MSG_CONTENT_SIZE];
	char time[DCLIENT_TIME_SIZE];/*in system time format*/
	char from[DCLIENT_USER_SIZE];/*format like:*******.2-1*/
	char to[DCLIENT_USER_SIZE];/*format like:0.0.0.0.0*/
	char from_name[DCLIENT_USER_SIZE];
	char to_name[DCLIENT_USER_SIZE];
}DCLIENT_TEXT_MSG;

#if 0
/*struct for alarm msg*/
typedef struct DCLIENT_ALARM_MSG_T
{
#define DCLIENT_ALARM_MSG_TYPE_SIZE			64
	unsigned int nSequenceNum;
	unsigned int id;
	unsigned int alarm_code;
	unsigned int unAlarmLocation;
	unsigned int unAlarmZone;
	unsigned int unAlarmCustomize;
	char type[DCLIENT_ALARM_MSG_TYPE_SIZE];
	char time[DCLIENT_TIME_SIZE];/*in system time format*/
	char from[DCLIENT_USER_SIZE];
	char to[DCLIENT_USER_SIZE];
	char from_name[DCLIENT_USER_SIZE];
	char to_name[DCLIENT_USER_SIZE];
}DCLIENT_ALARM_MSG;
#endif
typedef struct DLCIENT_RECV_ALARM_DATAt_T
{
#define DCLIENT_RECV_ALARM_MSG_TYPE_SIZE			256
#define DCLIENT_RECV_ALARM_MSG_ADDRESS_SIZE			64	
	unsigned int nSequenceNum;
	unsigned int id;
	unsigned int nExtension;
	unsigned int nDeviceType;
	unsigned int nDuration;
	unsigned int nAlarmCode;
	unsigned int unAlarmLocation;
	unsigned int unAlarmZone;
	unsigned int unAlarmCustomize;
	char szAddress[DCLIENT_RECV_ALARM_MSG_ADDRESS_SIZE];
	char szType[DCLIENT_RECV_ALARM_MSG_TYPE_SIZE];
	char szTime[DCLIENT_TIME_SIZE];/*in system time format*/ 
	char szMAC[DCLIENT_MAC_SIZE];
}DLCIENT_RECV_ALARM_DATA;

#if 0
typedef struct DCLIENT_ALARM_DEAL_INFO_T
{
#define DCLIENT_ALARM_DEAL_USER_SIZE	24
#define DCLIENT_ALARM_DEAL_RESULT_SIZE	512
#define DCLIENT_ALARM_TYPE_SIZE			64
	unsigned int id;
	unsigned int nAlarmCode;
	unsigned int unAlarmLocation;
	unsigned int unAlarmZone;
	unsigned int unAlarmCustomize;
	char szUser[DCLIENT_ALARM_DEAL_USER_SIZE];
	char szResult[DCLIENT_ALARM_DEAL_RESULT_SIZE];
	char szType[DCLIENT_ALARM_TYPE_SIZE];
	char szTime[DCLIENT_TIME_SIZE];
}DCLIENT_ALARM_DEAL_INFO;
#endif

/*struct for access control msg*/
typedef struct DCLIENT_ACCESS_INFO_T
{
#define ACCESS_INFO_CODE_SIZE		24
	char szCode[ACCESS_INFO_CODE_SIZE];
}DCLIENT_ACCESS_INFO;

typedef struct DLCIENT_BINDCODE_INFO_T
{
	int nStatus;
	char szBindCode[DCLIENT_BIND_CODE_SIZE];
	char szTime[DCLIENT_TIME_SIZE];
	char szDeviceCode[DEVICE_ID_SIZE];
}DLCIENT_BINDCODE_INFO;

typedef struct DLCIENT_BIND_CODE_LIST_T
{
	unsigned int nResult;
	unsigned int nBindCodeCount;
	unsigned int nSequenceNum;
	DLCIENT_BINDCODE_INFO bindCodeInfo[BIND_CODE_LIST_COUNT_MAX];
}DLCIENT_BIND_CODE_LIST;

typedef struct DLCIENT_BIND_CODE_CREATE_T
{
	int nResult;
	unsigned int nSequenceNum;
	char szBindCode[DCLIENT_BIND_CODE_SIZE];
}DLCIENT_BIND_CODE_CREATE;

typedef struct DCLIENT_SEND_DISCOVER_SEND_T
{
	int nSequenceNum;
	int nType;	
	int nExtension;	
	int nFlag;
	int nDiscoverMethod;
	char szDeviceID[DEVICE_ID_SIZE];
}DCLIENT_DISCOVER_SEND;

typedef struct DCLIENT_DISCOVER_ACK_T
{
#define DCLIENT_RTSP_URL_SIZE 		256
	int nSequenceNum;
	int nType;	
	int nExtension;
	int nDeviceMethod;
	char szDeviceID[DEVICE_ID_SIZE];
	char szIP[DCLIENT_IP_SIZE];
	char szFW[DEVICE_FWVER_SIZE];
	char szDeviceName[DCLIENT_VALUE_SIZE];
	char szRTSP[DCLIENT_RTSP_URL_SIZE];
	char szDeviceCode[DCLIENT_INT_SIZE];
	char szLocation[DCLIENT_VALUE_SIZE];
	char szMAC[DCLIENT_MAC_SIZE];
}DCLIENT_DISCOVER_ACK;

typedef struct DCLIENT_MSG_ACK_T
{
#define DCLIENT_MSG_ID_SIZE                     8
#define DCLIENT_MSG_CRC_SIZE                    8
#define DCLIENT_ACK_RESULT_SIZE                 8
#define DCLIENT_ACK_INFO_SIZE                   64
	unsigned int nSquenceNum;
	char szMsgID[DCLIENT_MSG_ID_SIZE];
	char szMsgCRC[DCLIENT_MSG_CRC_SIZE];
	char szResult[DCLIENT_ACK_RESULT_SIZE];
	char szInfo[DCLIENT_ACK_INFO_SIZE];
}DCLIENT_MSG_ACK;
/*struct for check tmp key msg*/
typedef struct DCLIENT_CHECK_TMP_KEY_T
{
#define CHECK_TMP_KEY_CODE_SIZE		24
#define MSG_SEQ_CODE_SIZE			24
#define CHECK_TMP_KEY_RELAY_SIZE	24
#define DCLIENT_TMPKEY_COMMON_SIZE	24
	int nResult;
	char szRelay[CHECK_TMP_KEY_RELAY_SIZE];
	char szSecurityRelay[CHECK_TMP_KEY_RELAY_SIZE];
	char szCheckTmpKeyCode[CHECK_TMP_KEY_CODE_SIZE];
	char szMsgSeqCode[MSG_SEQ_CODE_SIZE];
	char szUnitAPT[DCLIENT_TMPKEY_COMMON_SIZE];
	char szPerID[DCLIENT_COMMON_SIZE_32];
}DCLIENT_CHECK_TMP_KEY;

/*struct for request arming msg*/
typedef struct DCLIENT_REQUEST_ARMING_T
{
#define DCLIENT_ARMING_ACTION_SIZE		12
	unsigned int nSequenceNum;//序列号，用于标识消息
	int mode;// 0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor 
	char action[DCLIENT_ARMING_ACTION_SIZE]; /*Set=设置 Get=获取*/
	char from[DCLIENT_USER_SIZE];/*AppUser*/
	char to[DCLIENT_USER_SIZE];/*INDOOR_1.*******-1*/
	char from_ip[DCLIENT_USER_SIZE];
	char to_ip[DCLIENT_USER_SIZE];
}DCLIENT_REQUEST_ARMING;

typedef struct DCLIENT_REPORT_ARMING_T
{
	unsigned int nSequenceNum;//序列号，用于标识消息
	int mode;//  0=Disarm, 1=Indoor, 2=Sleeping, 3=Outdoor   
	unsigned int nSync;//0=ingore,1=just sync not need response
	unsigned int nActionType;//0=reboot report;1=get report;2=setted report;3=self set report
	char from[DCLIENT_USER_SIZE];/*INDOOR_1.*******-1*/
	char to[DCLIENT_USER_SIZE];/*AppUser 如果为空，则为当前节点的所有app*/
	char from_ip[DCLIENT_USER_SIZE];
	char to_ip[DCLIENT_USER_SIZE];
}DCLIENT_REPORT_ARMING;

typedef struct DCLIENT_PUSH_NOANSWER_FWD_NUMBER_T
{
#define DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE			128
#define DCLIENT_PUSH_NOANSWER_FWD_NUMBER_ACTION_SIZE			12
	unsigned int nSequenceNum;//序列号，用于标识消息
	char action[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_ACTION_SIZE];
	char group0[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char group1[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char group2[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char from[DCLIENT_USER_SIZE];/*INDOOR_1.*******-1*/
	char to[DCLIENT_USER_SIZE];/*DOOR_1.*******-1 如果没有分机号，则为所有同类型的所有分机*/
}DCLIENT_PUSH_NOANSWER_FWD_NUMBER;

typedef struct DCLIENT_REPORT_NOANSWER_FWD_NUMBER_T
{
	unsigned int nSequenceNum;//序列号，用于标识消息
	char group0[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char group1[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char group2[DCLIENT_PUSH_NOANSWER_FWD_NUMBER_GOURP_SIZE];
	char from[DCLIENT_USER_SIZE];/*DOOR_1.*******-1*/
	char to[DCLIENT_USER_SIZE];/*INDOOR_1.*******-1*/
}DCLIENT_REPORT_NOANSWER_FWD_NUMBER;

typedef struct DCLIENT_MOTION_ALERT_T
{
	char szPicName[DCLIENT_URL_SIZE];
}DCLIENT_MOTION_ALERT;

typedef struct DCLIENT_DOOR_MOTION_ALERT_T
{
	char szMAC[DCLIENT_MAC_SIZE];
}DCLIENT_DOOR_MOTION_ALERT;

typedef struct DCLIENT_REPORT_ACTIVITY_T
{
	unsigned int nSeq; //唯一的ID
	unsigned int nType;
	unsigned int nResponse;
	unsigned int nTimestamp; //本条记录的生成时间时间戳
	char szPicName[DCLIENT_URL_SIZE];
	char szInitiator[DCLIENT_VALUE_SIZE];
	char szPerID[DCLIENT_COMMON_SIZE_32];
}DCLIENT_REPORT_ACTIVITY;

typedef struct DCLIENT_REPORT_ACTIVITY_ACK_T
{
	int nResponse; //成功返回0，失败返回-1
	unsigned int nSeq; //唯一的ID
}DCLIENT_REPORT_ACTIVITY_ACK;

typedef struct DCLIENT_SYNC_ACTIVITY_ACK_T  
{  
    unsigned int nSeq; //唯一的ID  
    int nResponse; //0正常，1 MD5出错，2 没收到文件
}DCLIENT_SYNC_ACTIVITY_ACK;  

typedef struct DCLIENT_SYNC_ACTIVITY_T
{
	unsigned int nSeq; //唯一的ID
	char szMD5[DCLIENT_MD5_SIZE];
	char szFileName[DCLIENT_COMMON_SIZE_256];	
}DCLIENT_SYNC_ACTIVITY;

typedef struct DCLIENT_CHECK_DTMF_T
{
	unsigned int nSequenceNum;
	char szRemoteSip[DCLIENT_SIP_SIZE];	
}DCLIENT_CHECK_DTMF;

typedef struct DCLIENT_CHECK_DTMF_ACK_T
{
	unsigned int nSequenceNum;
	unsigned int nResult;
	char szRemoteSip[DCLIENT_SIP_SIZE];	
}DCLIENT_CHECK_DTMF_ACK;

typedef struct DCLIENT_DEVICE_CODE_T
{
#define DCLIENT_DEVICE_CODE_SIZE			20
	char szDeviceCode[DCLIENT_DEVICE_CODE_SIZE];
}DCLIENT_DEVICE_CODE;


//#define DCLIENT_DEVICE_CODE_SIZE     16
#define DCLIENT_RESPONSE_MSG_SIZE    128
#define DCLIENT_EMAIL_SIZE           64
#define DCLIENT_PASSWD_SIZE          64
#define DCLIENT_ENCRY_PASSWD_SIZE    128
#define DCLIENT_NAME_SIZE            48
#define DCLIENT_SLAVE_ACCOUNT_MAX_COUNT 20 //从账号列表个数
#define DCLIENT_TOKEN_SIZE				20

typedef struct DCLIENT_REPORT_DEVICE_CODE_T
{
	char szDeviceCode[DCLIENT_DEVICE_CODE_SIZE];
	char szFromIP[DCLIENT_USER_SIZE];//由dclient自行填充
	char szToIP[DCLIENT_USER_SIZE];//由应用端负责，应为网关地址
}DCLIENT_REPORT_DEVICE_CODE;

#if RL_SUPPORT_DEVICE_HTTP_REGISTER
typedef struct DCLIENT_PERSONAL_REGISTER_T
{
	char szName[DCLIENT_NAME_SIZE];
	char szEmail[DCLIENT_EMAIL_SIZE];
	char szPasswd[DCLIENT_PASSWD_SIZE];
	char szDeviceCode[DCLIENT_DEVICE_CODE_SIZE * 5];
}DCLIENT_PERSONAL_REGISTER;

typedef struct DCLIENT_PERSONAL_LOGIN_T
{
	char szAccount[DCLIENT_MAC_SIZE];//填写MAC
	char szPasswd[DCLIENT_ENCRY_PASSWD_SIZE];
}DCLIENT_PERSONAL_LOGIN;

typedef struct DCLIENT_ADD_SLAVE_ACCOUNT_T
{
	char szEmail[DCLIENT_EMAIL_SIZE];
	char szName[DCLIENT_NAME_SIZE];
}DCLIENT_ADD_SLAVE_ACCOUNT;

typedef struct DCLIENT_GET_SLAVE_ACCOUNT_LIST_T
{
    //目前不用分页，按如下的默认值
    int nPage;//设备默认传0
    int nRow;//设备默认传100
}DCLIENT_GET_SLAVE_ACCOUNT_LIST;

typedef struct DLCIENT_SLAVE_ACCOUNT_INFO_T
{
	int nID;//从账号数据库id
	int nRole;//账号角色
	char szName[DCLIENT_NAME_SIZE];
	char szEmail[DCLIENT_EMAIL_SIZE];
	char szSip[DCLIENT_SIP_SIZE];
}DLCIENT_SLAVE_ACCOUNT_INFO;

typedef struct DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK_T
{
	int nCount;//实际从账号个数	
	int nResult;
	char szMsg[DCLIENT_RESPONSE_MSG_SIZE];
	DLCIENT_SLAVE_ACCOUNT_INFO stSlaveAccountList[DCLIENT_SLAVE_ACCOUNT_MAX_COUNT];
}DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK;

typedef struct DCLIENT_DEL_SLAVE_ACCOUNT_T
{
    int nID;//从账号的id
}DCLIENT_DEL_SLAVE_ACCOUNT;

typedef struct DCLIENT_BIND_DEVICE_T
{
    char szDeviceCode[DCLIENT_DEVICE_CODE_SIZE];
    char szLocation[DCLIENT_NAME_SIZE];
}DCLIENT_BIND_DEVICE;

typedef struct DCLIENT_EMAIL_EXIST_T
{
    char szEmail[DCLIENT_EMAIL_SIZE];
}DCLIENT_EMAIL_EXIST;

typedef struct DCLIENT_WEB_ACK_MSG_T
{
	int nResult;
	char szMsg[DCLIENT_RESPONSE_MSG_SIZE];
}DCLIENT_WEB_ACK_MSG;

typedef struct DCLIENT_WEB_LOGIN_ACK_MSG_T
{
	int nResult;
	char szMsg[DCLIENT_RESPONSE_MSG_SIZE];
	char szToken[DCLIENT_TOKEN_SIZE];
}DCLIENT_WEB_LOGIN_ACK_MSG;

#endif

typedef struct DCLIENT_NETWORK_INFO_T
{
	char szIP[DCLIENT_IP_SIZE];
	char szMAC[DCLIENT_MAC_SIZE];
}DCLIENT_NETWORK_INFO;

typedef struct DCLIENT_REPORT_NETWORK_INFO_T
{
	char szFromIP[DCLIENT_IP_SIZE];
	char szToIP[DCLIENT_IP_SIZE];
	DCLIENT_NETWORK_INFO networkInfo;
}DCLIENT_REPORT_NETWORK_INFO;

typedef struct DCLIENT_MAINTENANCE_GETLOG_T
{
	char szServerUrl[DCLIENT_URL_SIZE];
	char szUserName[DCLIENT_NAME_SIZE];
	char szPasswd[DCLIENT_PASSWD_SIZE];
	char szFileName[DCLIENT_NAME_SIZE];
}DCLIENT_MAINTENANCE_GETLOG;


#define DCLIENT_ROBINCALL_SIZE     10
#define DCLIENT_ROBINCALL_KEY_SIZE     16
#define DCLIENT_APP_INDOOR_LABEL_SIZE   32


typedef struct DCLINET_ACK_APP_AND_INDOOR_LABEL_DETAIL_T
{
    char szLabel[DCLIENT_APP_INDOOR_LABEL_SIZE];
    char szKey[DCLIENT_ROBINCALL_KEY_SIZE];
}DCLINET_ACK_APP_AND_INDOOR_LABEL_DETAIL;    
    
typedef struct DCLIENT_ACK_APP_AND_INDOOR_LABEL_T
{
    DCLINET_ACK_APP_AND_INDOOR_LABEL_DETAIL stLabelDetail[20];
	int nResult;//返回码
	char szMsg[DCLIENT_RESPONSE_MSG_SIZE];//错误消息
}DCLIENT_ACK_APP_AND_INDOOR_LABEL;

typedef struct DCLIENT_ROBINCALL_KEY_VALUE_T
{
	char szRobinCallKey[DCLIENT_ROBINCALL_KEY_SIZE];
}DCLIENT_ROBINCALL_KEY_VALUE;

typedef struct DCLIENT_MOTION_AND_ROBINCALL_T
{
	int nResult;//返回码
	char szMsg[DCLIENT_RESPONSE_MSG_SIZE];//错误消息
	short nEnableMotion;
	short nMotionTime;
    short nEnableRobinCall;
    short nRobinCallTime;
	DCLINET_ACK_APP_AND_INDOOR_LABEL_DETAIL stLabelDetail[20];
	DCLIENT_ROBINCALL_KEY_VALUE szRobinCall[10];
}DCLIENT_MOTION_AND_ROBINCALL;

typedef struct DCLIENT_SET_PERSONAL_ACCOUNT_MOTION_T
{
    short nEnableMotion;
    short nMotionTime;
}DCLIENT_SET_PERSONAL_ACCOUNT_MOTION;

typedef struct DCLIENT_SET_DTMF_T
{
#define DCLIENT_DTMF_SIZE	10
	unsigned int nSequenceNum;
	char szDTMF[DCLIENT_DTMF_SIZE];
	char szFrom[DCLIENT_USER_SIZE];/*INDOOR_1.*******-1*/
	char szTo[DCLIENT_USER_SIZE];/*DOOR_1.*******-1 如果没有分机号，则为所有同类型的所有分机*/
}DCLIENT_SET_DTMF;

typedef struct DCLIENT_CONFIG_MODULE_T
{
#define DCLIENT_CONFIG_MODULE_ITEM_NUM			10
#define DCLIENT_CONFIG_MODULE_ITEM_SIZE			256
	char szItem[DCLIENT_CONFIG_MODULE_ITEM_NUM][DCLIENT_CONFIG_MODULE_ITEM_SIZE];
}DCLIENT_CONFIG_MODULE;

typedef struct DCLIENT_REQUEST_CONFIG_T
{
	char szFromIP[DCLIENT_IP_SIZE];
	char szToIP[DCLIENT_IP_SIZE];
	DCLIENT_CONFIG_MODULE module;	
}DCLIENT_REQUEST_CONFIG;

typedef struct DCLIENT_MAKE_CALL_T
{
#define DCLIENT_MAX_MAKE_CALL_COUNT	10
	char szCallData[DCLIENT_MAX_MAKE_CALL_COUNT][DCLIENT_VALUE_SIZE];
}DCLIENT_MAKE_CALL;

typedef struct DCLIENT_CONTROL4_RESPONSE_COMMON_T
{
#define DCLIENT_CONTROL4_RESPONSE_COMMON_SIZE	512
	char szResponse[DCLIENT_CONTROL4_RESPONSE_COMMON_SIZE];
}DCLIENT_CONTROL4_RESPONSE_COMMON;

typedef struct DCLIENT_CONTROL4_MESSAGE_T
{
#define    DCLIENT_CONTROL4_MESSAGE_HEADER_SIZE 32
	char szHeader[DCLIENT_CONTROL4_MESSAGE_HEADER_SIZE];
	char szMsg[DCLIENT_TEXT_MSG_CONTENT_SIZE];
}DCLIENT_CONTROL4_MESSAGE;

typedef struct DCLIENT_REMOTE_CONTROL_COMMON_T
{
#define DCLIENT_REMOTE_CONTROL_COMMON_ACTION_SIZE	64
#define DCLIENT_REMOTE_CONTROL_VALUE_SIZE		32
#define DCLIENT_REMOTE_CONTROL_VALUE_NUM			8
	char szValue[DCLIENT_REMOTE_CONTROL_VALUE_NUM][DCLIENT_REMOTE_CONTROL_VALUE_SIZE];
	char szAction[DCLIENT_REMOTE_CONTROL_COMMON_ACTION_SIZE];	
}DCLIENT_REMOTE_CONTROL_COMMON;

typedef struct DCLIENT_REMOTE_CONTROL_OPENDOOR_T
{
#define DCLIENT_REMOTE_CONTROL_OPENDOOR_VALUE_SIZE	32
	int nStatus;
	char szUserID[DCLIENT_REMOTE_CONTROL_OPENDOOR_VALUE_SIZE];
	char szRelay[DCLIENT_REMOTE_CONTROL_OPENDOOR_VALUE_SIZE];
	char szTraceID[DCLIENT_REMOTE_CONTROL_OPENDOOR_VALUE_SIZE];
}DCLIENT_REMOTE_CONTROL_OPENDOOR;


typedef struct DCLIENT_REPORT_CALL_CAPTURE_T
{
	char szPicName[DCLIENT_URL_SIZE];
	char szCaller[DCLIENT_SIP_SIZE];
	char szCallee[DCLIENT_SIP_SIZE];
	int nCallType;	
}DCLIENT_REPORT_CALL_CAPTURE;

typedef struct DCLIENT_REPORT_TRIGGER_T
{
#define DCLIENT_TRIGGER_TYPE_SIZE	64
	char szType[DCLIENT_TRIGGER_TYPE_SIZE];
}DCLIENT_REPORT_TRIGGER;

/*
	1 Trigger, 0 normal;
	1*ARMING_ZOOM_NUM_MAX=Indoor
	2*ARMING_ZOOM_NUM_MAX=Sleeping
	3*ARMING_ZOOM_NUM_MAX=Outdoor
*/
typedef struct DCLIENT_ALL_TRIGGER_STATUS_T
{
#define ARMING_ZOOM_NUM_MAX 8
    bool bAlarmChException[ARMING_ZOOM_NUM_MAX*3];
    char szFromIP[DCLIENT_IP_SIZE];
    char szToIP[DCLIENT_IP_SIZE];
}DCLIENT_ALL_TRIGGER_STATUS;


#define DCLIENT_NODES_SIZE_MAX	512
#define DCLIENT_MANAGE_ALARM_APT_SIZE	64
typedef struct DCLIENT_MANAGE_BROADCAST_MSG_T
{
	char szTitle[DCLIENT_TEXT_MSG_TITLE_SIZE];
	char szContent[DCLIENT_TEXT_MSG_CONTENT_SIZE];
	char szTime[DCLIENT_TIME_SIZE];
	char szNodes[DCLIENT_NODES_SIZE_MAX];
}DCLIENT_MANAGE_BROADCAST_MSG;

typedef struct DCLIENT_MANAGE_ALARM_MSG_T
{
	int nID;
	unsigned int nAlarmCode;
	unsigned int unAlarmLocation;
	unsigned int unAlarmZone;
	unsigned int unAlarmCustomize;
	char szType[256];
	char szAlarmMsg[DCLIENT_TEXT_MSG_CONTENT_SIZE];
	char szFromName[DCLIENT_USER_SIZE];
	char szApt[DCLIENT_MANAGE_ALARM_APT_SIZE];
	char szTime[DCLIENT_TIME_SIZE];
	char szMAC[DCLIENT_MAC_SIZE];
}DCLIENT_MANAGE_ALARM_MSG;

typedef struct DCLIENT_REPORT_HEALTH_T
{
	int nStatus;
}DCLIENT_REPORT_HEALTH;

typedef struct DCLIENT_SENSOR_TRIGGER_T
{
	unsigned int nMode;
	unsigned int nHomeTrigger;
	unsigned int nSleepTrigger;
	unsigned int nAwayTrigger;
}DCLIENT_SENSOR_TRIGGER;

typedef struct DCLIENT_UPLOAD_VIDEO_NOTIFY_T
{
#define DCLIENT_UPLOAD_VIDEO_NAME_SIZE 64
	char szName[DCLIENT_UPLOAD_VIDEO_NAME_SIZE];//视频名字
	char szCreateTime[DCLIENT_TIME_SIZE];//视频的创建时间
	char szVideoDuration[DCLIENT_TIME_SIZE];//视频时长
	char szMD5[DCLIENT_MD5_SIZE];//视频文件的MD5
	char szRemoteMAC[DCLIENT_MAC_SIZE]; //对方设备的MAC，无法获取就放空
	char szRemoteIP[DCLIENT_IP_SIZE]; //对方设备的IP
}DCLIENT_UPLOAD_VIDEO_NOTIFY;

typedef struct DCLIENT_UPLOAD_CAPTURE_NOTIFY_T
{
 char szName[DCLIENT_URL_SIZE];//图片名称
 char szCreateTime[DCLIENT_TIME_SIZE];//图片的创建时间
 char szMD5[DCLIENT_MD5_SIZE];//图片文件的MD5
 char szRemoteMAC[DCLIENT_MAC_SIZE]; //对方设备的MAC，无法获取就放空
 char szRemoteIP[DCLIENT_IP_SIZE]; //对方设备的IP
}DCLIENT_UPLOAD_CAPTURE_NOTIFY;


typedef struct DCLIENT_REPORT_GAS_T
{
	unsigned int nGasCount;							/*The total gas be used in the month.*/
	char szMonth[DCLIENT_TIME_SIZE];		/*the format should be 2019-07.*/
}DCLIENT_REPORT_GAS;

typedef struct DCLIENT_REPORT_DOORSTATUS_T
{
	int nDoorState;							/*The door status of door phone. 0=closed, 1=opened.*/
	char szIPAddr[DCLIENT_IP_SIZE];				        /*The IP address of door phone.*/
}DCLIENT_REPORT_DOORSTATUS;

typedef struct DCLIENT_REPORT_VISITOR_INFO_T
{
#define DCLIENT_VISITOR_VALUE_SIZE	128
	char szVisitor[DCLIENT_VISITOR_VALUE_SIZE];
	char szAccount[DCLIENT_VISITOR_VALUE_SIZE];
    char szEmail[DCLIENT_VISITOR_VALUE_SIZE];
    char szPhone[DCLIENT_VISITOR_VALUE_SIZE];
	char szID[DCLIENT_VISITOR_VALUE_SIZE];
    char szFrom[DCLIENT_VISITOR_VALUE_SIZE];
	char szModelName[DCLIENT_VISITOR_VALUE_SIZE];
    int  nCount;
}DCLIENT_REPORT_VISITOR_INFO;

typedef struct DCLIENT_REPORT_VISITOR_AUTH_INFO_T
{
	int nCount;
	char szSipAccout[DCLIENT_SIP_SIZE];
}DCLIENT_REPORT_VISITOR_AUTH_INFO;

typedef struct DCLIENT_FACE_DATA_FORWARD_T
{
	char szUrl[DCLIENT_URL_SIZE];
}DCLIENT_FACE_DATA_FORWARD;

typedef struct DCLIENT_SEND_TEMP_KEY_T
{
#define DCLIENT_TMP_KEY_CODE_SIZE	24
	char szTempKey[DCLIENT_TMP_KEY_CODE_SIZE];
}DCLIENT_SEND_TEMP_KEY;

typedef struct DCLIENT_SEND_OSS_STS_T
{
#define DCLIENT_OSS_STS_TOKEN_SIZE	1024
#define DCLIENT_OSS_STS_KEY_SIZE	128
#define DCLIENT_OSS_STS_ENDPOINT_SIZE	256
	char szAccessKeySecret[DCLIENT_OSS_STS_KEY_SIZE];
	char szAccessKeyId[DCLIENT_OSS_STS_KEY_SIZE];
	char szSecurityToken[DCLIENT_OSS_STS_TOKEN_SIZE];
	char szBucket[DCLIENT_OSS_STS_KEY_SIZE];
	char szEndpoint[DCLIENT_OSS_STS_ENDPOINT_SIZE];
}DCLIENT_SEND_OSS_STS;

typedef struct DCLIENT_REQUEST_OSS_STS_T
{
	char szName[DCLIENT_VALUE_SIZE];
}DCLIENT_REQUEST_OSS_STS;

typedef struct DCLIENT_REMOTE_ACCESS_WEB_T
{
	int nPort;
	int nSSHPort;
	char szPassword[DCLIENT_PASSWD_SIZE];
	char szUserName[DCLIENT_NAME_SIZE];
}DCLIENT_REMOTE_ACCESS_WEB;

typedef struct DCLIENT_REQUEST_OPENDOOR_T
{
#define DCLIENT_RELAY_SIZE	16
	char szRelay[DCLIENT_RELAY_SIZE];
	char szMAC[DCLIENT_MAC_SIZE];
}DCLIENT_REQUEST_OPENDOOR;

typedef struct DCLIENT_OPENDOOR_ACK_T
{
	int nResult;
}DCLIENT_OPENDOOR_ACK;

typedef struct DCLIENT_FACE_INFO_T
{
	int nID;  //唯一的识别ID
	char szURL[DCLIENT_URL_SIZE];  //图片的下载地址
	char szPICMD5[DCLIENT_MD5_SIZE]; //图片的MD5
	char szName[DCLIENT_VALUE_SIZE]; //图片对应人的名字
	char szDoorNum[DCLIENT_VALUE_SIZE]; //能开的RELAY
	char szWeek[DCLIENT_VALUE_SIZE];   //"1111011"表示除了周五是false，其他都是true
	char szTimeStart[DCLIENT_VALUE_SIZE]; //如果是时间戳，则表示生效范围
	char szTimeEnd[DCLIENT_VALUE_SIZE];	
}DCLIENT_FACE_INFO;

typedef struct DCLIENT_MAINTENANCE_ALARM_REPORT_T
{
#define DCLIENT_MAINTENANCE_ALARM_INFO_SIZE	512
	int nCode; //告警码
	int nLevel;//告警等级 0:FAULT、1:ERROR、2:WARN、3:INFO
	char szTimeStamp[DCLIENT_VALUE_SIZE];//告警时间戳,精确到秒
	char szTraceID[DCLIENT_INT_SIZE];//10位的随机数，用来方便定位log
	char szModule[DCLIENT_VALUE_SIZE];//告警模块 phone;network;autop;
	char szInfo[DCLIENT_MAINTENANCE_ALARM_INFO_SIZE];//告警说明
}DCLIENT_MAINTENANCE_ALARM_REPORT;

typedef struct DCLIENT_REQUEST_ACINFO_T
{
#define DCLIENT_ACID_SIZE	2000
	int nCount;
	char szACID[DCLIENT_ACID_SIZE];
}DCLIENT_REQUEST_ACINFO;

typedef struct DCLIENT_DELIVERY_MSG_T
{
	int nCountNum; //快递总件数
	char szAccount[DCLIENT_COMMON_SIZE_32];//主账号或者家庭成员的账号
}DCLIENT_DELIVERY_MSG;

typedef struct DCLIENT_SEND_DELIVERY_T
{
#define DELIVERY_SEND_COUNT_MAX	10
	int nCount; //本次发送给几个人
	DCLIENT_DELIVERY_MSG deliveryMsg[DELIVERY_SEND_COUNT_MAX];
}DCLIENT_SEND_DELIVERY;

typedef struct DCLIENT_SEND_WAKE_WORD_T
{
#define DCLIENT_WAKE_WORD_BUFF_MAX	2048
	int nSeq; //本次消息的唯一标识
	char szWakeWord[DCLIENT_WAKE_WORD_BUFF_MAX];
}DCLIENT_SEND_WAKE_WORD;

typedef struct DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA_T
{	
	char szUrl[DCLIENT_COMMON_SIZE_32*4]; //上传或下载的url
} DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA;

typedef struct DCLIENT_NOTIFY_ATTENDANCE_SERVICE_T
{
	char szIP[DCLIENT_IP_SIZE];
}DCLIENT_NOTIFY_ATTENDANCE_SERVICE;

typedef struct DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT_T
{	
	char szUrl[DCLIENT_COMMON_SIZE_32*4]; //上传或下载的url
} DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT;

#if 0
typedef struct DCLIENT_NOTIFY_HANDLE_FILE_INFO_T
{
	char szFilePath[DCLIENT_URL_SIZE];
	char szMD5[DCLIENT_MD5_SIZE];
}DCLIENT_NOTIFY_HANDLE_FILE_INFO;
#endif

typedef struct DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE_T
{
	char szRelay[DCLIENT_COMMON_SIZE_32];
}DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE;

typedef struct DCLIENT_REPORT_RELAY_STATUS_T
{
	char szRelay[DCLIENT_COMMON_SIZE_32];
}DCLIENT_REPORT_RELAY_STATUS;

typedef struct DCLIENT_UPDATE_MD5_T
{
	char szMD5[DCLIENT_MD5_SIZE];
}DCLIENT_UPDATE_MD5;

typedef struct DCLIENT_FLOW_OUT_OF_LIMIT_T
{
	float fPercent;
	float fLimit;
}DCLIENT_FLOW_OUT_OF_LIMIT;

typedef struct DCLIENT_REPORT_CALLLOG_T
{
	int nCallType; //呼叫类别：1-SIP 2-IP 0-无效类别
	int nDuration;	//通话时长，以秒为单位,0为没应答
	char szCaller[DCLIENT_COMMON_SIZE_32]; //主叫号码
	char szCallee[DCLIENT_COMMON_SIZE_32]; //被叫号码
	char szCallerName[DCLIENT_COMMON_SIZE_64]; //主叫的名称，暂时填写主叫的MAC
	char szCalleeName[DCLIENT_COMMON_SIZE_64]; //被叫的名称，如果是通过联系人列表里呼叫，需要填被叫的MAC，否则放空,如果呼叫SDMC则填写SDMC
	char szTime[DCLIENT_COMMON_SIZE_32];
}DCLIENT_REPORT_CALLLOG;

typedef struct DCLIENT_BACKUP_CONFIG_T
{
	char szFileName[DCLIENT_COMMON_SIZE_64];//文件名称，按照时间戳+6位随机数命名
	char szUploadPath[DCLIENT_URL_SIZE];//上传的路径
}DCLIENT_BACKUP_CONFIG;

typedef struct DCLIENT_BACKUP_CONFIG_ACK_T
{
	char szFileName[DCLIENT_COMMON_SIZE_64];
	char szMD5[DCLIENT_MD5_SIZE];
}DCLIENT_BACKUP_CONFIG_ACK;

typedef struct DCLIENT_BACKUP_CONFIG_RECOVERY_T
{
	char szFileName[DCLIENT_COMMON_SIZE_64];
}DCLIENT_BACKUP_CONFIG_RECOVERY;

typedef struct DCLIENT_REQUEST_RTSP_MONITOR_T
{
	char szSeq[DCLIENT_INT_SIZE]; //一个10位的随机数，用来区别唯一的监控
	char szFromIP[DCLIENT_IP_SIZE]; //本地设备的IP地址
	char szToIP[DCLIENT_IP_SIZE]; //目的设备的IP地址
	char szRTSP[DCLIENT_URL_SIZE]; //RTSP地址
	char szUser[DCLIENT_COMMON_SIZE_32]; //RTSP用户名
	char szPassword[DCLIENT_COMMON_SIZE_32]; //RTSP密码
}DCLIENT_REQUEST_RTSP_MONITOR;

typedef struct DCLIENT_RTSP_MONITOR_STOP_T
{
	char szSeq[DCLIENT_INT_SIZE]; //一个10位的随机数，用来区别唯一的监控
	char szFromIP[DCLIENT_IP_SIZE]; //本地设备的IP地址
	char szToIP[DCLIENT_IP_SIZE]; //目的设备的IP地址
}DCLIENT_RTSP_MONITOR_STOP;

typedef struct DCLIENT_REG_END_USER_T
{
	int nStatus;//0未注册,1已注册
	char szAccount[DCLIENT_VALUE_SIZE+1];
	char szRegUrl[1024];
	char szAccountName[129];
	char szEmail[65];
	char szMobileNum[25];
}DCLIENT_REG_END_USER;

typedef struct KIT_DEVICE_BASE_INFO_T
{
	int nType;//0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机;  5:SDMC;  50: 门禁  自动扫描的时候，该字段可以放空
	char szMAC[DCLIENT_MAC_SIZE];
	char szSWVer[DCLIENT_COMMON_SIZE_32]; //手动添加的时候，该字段可以放空
	char szLocation[DCLIENT_COMMON_SIZE_64+1]; //自动扫描的时候，该字段可以放空
}KIT_DEVICE_BASE_INFO;

typedef struct DCLIENT_REPORT_KIT_DEVICE_LIST_T
{
#define KIT_MAX_SEND_COUNT	20
	KIT_DEVICE_BASE_INFO deviceInfo[KIT_MAX_SEND_COUNT];
}DCLIENT_REPORT_KIT_DEVICE_LIST;

enum
{

/** post when dclient received new privitekey list
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of local privatekey xml file
  *
 */
	MSG_D2P_RECV_PRIVATEKEY = MSG_D2P + 1,

/** post when dclient received new rfkey list---2
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of local rfkey xml file
  *
 */
	MSG_D2P_RECV_RFKEY,

/** post when dclient received AD settings---3
  *
  *@param param1 -- 0默认值；1表示清除广告
  *@param param2 -- NULL
  *@param lpData -- full path of local AD xml file
  *
 */
	MSG_D2P_RECV_AD,

/** post when dclient received address list---4
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of local address xml file
  *
 */
	MSG_D2P_RECV_ADDRESS,

/** post when dclient received text message---5
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_TEXT_MSG
  *
 */
	MSG_D2P_RECV_MSG,

/** post when dclient received text alarm---6
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DLCIENT_RECV_ALARM_DATA
  *
 */
	MSG_D2P_RECV_ALARM,

/** post when dclient received community phonebook---7
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of community xml or csv file
  *
 */
 	MSG_D2P_RECV_COMMUNITY,
 	
/** create bind code---8
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData-- -- DLCIENT_BIND_CODE_CREATE
  *
 */
	MSG_D2P_CREATE_BIND_CODE,
	
/** delete bind code---9
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DLCIENT_BIND_CODE_CREATE
  *
 */
	MSG_D2P_DELETE_BIND_CODE,
	
/** get bind code list---A
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DLCIENT_BIND_CODE_LIST
  *
 */
	MSG_D2P_BIND_CODE_LIST,

/** notify bindcode change---B
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- NULL
  *
 */
	MSG_D2P_NOTIFY_BIND_CODE_CHANGE,

/** notify alarm deal---C
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_RECV_ALARM_DEAL
  *
 */	
	MSG_D2P_ALARM_DEAL_NOTIFY,
	
/** alarm ACK---D
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_ALARM_MSG
  *
 */
	MSG_D2P_ALARM_ACK,


/** Discover ACK---E
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_DISCOVER_ACK
  *
 */
	MSG_D2P_DISCOVER_ACK,

/** ACK---F
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MSG_ACK
  *
 */
	MSG_D2P_ACK,
	
/** post when dclient received check tmp key---10
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CHECK_TMP_KEY
  *
 */
 	MSG_D2P_CHECK_TMP_KEY_ACK,
 	

/** post when dclient received check tmp key---11
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- deviceinfo.xml path
  *
 */
	MSG_D2P_DEVICE_LIST_INFO,

/** post when dclient received check tmp key---12
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- NULL
  *
 */
	MSG_D2P_LOGOUT_SIP,

/** Request Arming---13
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REQUEST_ARMING_MSG
  *
 */
	MSG_D2P_REQUEST_ARMING,

/** Report Noanswer Fwd Number---14
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_NOANSWER_FWD_NUMBER_MSG
  *
 */
	MSG_D2P_REPORT_NOANSWER_FWD_NUMBER,

  /** Clear RFID and Privatekey---15
  *
  *@param param1 -- what kind of Key to clear
  *@param param2 -- NULL
  *@param lpData -- full path of local address xml file
  *
 */
  MSG_D2P_CLEAR_FILE_DATA,

  /*---16
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REQUEST_ARMING_P2P
  *
 */
	MSG_D2P_REQUEST_ARMING_P2P,
	
/** notify PHONE to start vrtsp---17
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- vrtsp data
  *
  */
  	MSG_D2P_START_VRTSP,

/** notify PHONE to stop vrtsp---18
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- vrtsp data
  *
  */
  	MSG_D2P_STOP_VRTSP,

/** notify PHONE to keep vrtsp---19
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- vrtsp data
  *
  */
  	MSG_D2P_KEEP_VRTSP,

/** Report ARMING P2P---1A
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_ARMING
  *
 */
	MSG_D2P_REPORT_ARMING,
	

/** boot up---1B
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- NULL
  *
 */
	MSG_D2P_BOOTUP,

/** check dtmf ack---1C
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CHECK_DTMF_ACK
  *
 */
	MSG_D2P_CHECK_DTMF_ACK,

/** 云平台发下来的设备码用来实现自己注册账号---1D
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_DEVICE_CODE
  *
 */	
	MSG_D2P_DEVICE_CODE,

/**  ---1E
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_DEVICE_CODE
  *
 */	
	MSG_D2P_REPORT_DEVICE_CODE,

/**  注册响应---1F
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_PERSONAL_REGISTER_ACK,

/**  登录响应---20
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_PERSONAL_LOGIN_ACK,
	
/**  添加从账号响应---21
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_ADD_SLAVE_ACCOUNT_ACK,

/**  获取从账号列表响应---22
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK
  *
 */
	MSG_D2P_GET_SLAVE_ACCOUNT_LIST_ACK,

/**  删除从账号响应---23
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_DELETE_SLAVE_ACCOUNT_ACK,

/**  绑定设备响应---24
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_ADD_DEVICE_BY_DEVICECODE_ACK,

/**判断邮箱是否存在响应---25
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_EMAIL_EXIST_ACK,

/**通知上层连接状态的变化---26
  *
  *@param param1 -- NULL
  *@param param2 -- 0为未连接，1为连接
  *@param lpData -- NULL
  *
 */
	MSG_D2P_NOTIFY_CONNECT_STATUS,
	
/**上报网络信息---27
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_NETWORK_INFO
  *
 */
	MSG_D2P_REPORT_NETWORK_INFO,

/**清除device code---28
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- NULL
  *
 */
	MSG_D2P_CLEAR_DEVICE_CODE,

/**通过discover上报ip---29
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- IP
  *
 */
	MSG_D2P_DISCOVER_REPORT_IP,

/**室内机根据mac查找联系人列表，确定是走平台还是本地的预览---2A
	*
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_DOOR_MOTION_ALERT
  *
*/
	MSG_D2P_DOOR_MOTION_ALERT,

/**通知motion和robincall的消息---2B
	*
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MOTION_AND_ROBINCALL
  *
*/
	MSG_D2P_MOTION_AND_ROBINCALL_INFO,

/**  设置motion响应---2C
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_SET_PERSONAL_ACCOUNT_MOTION_ACK,

/**  设置robincall响应---2D
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_WEB_ACK_MSG
  *
 */
	MSG_D2P_SET_PERSONAL_ACCOUNT_CALLBIN_ACK,

/** post when dclient received new privitekey list---2E
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of local faceid xml file
  *
 */
	MSG_D2P_RECV_FACEID_XML,


/** Clear Face Xml ---2F
  *
  *@param param1 -- what kind of xml to clear,SDMC or FacePro
  *@param param2 -- NULL
  *@param lpData -- NULL
 */
	MSG_D2P_CLEAR_FACE_DATA,

/**  设置dtmf---30
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_SET_DTMF
  *
 */
	MSG_D2P_SET_DTMF,
	
/** post when dclient received new privitekey list---31
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- full path of download face pic xml file
  *
 */
	MSG_D2P_RECV_DOWNLOAD_FACEPIC_XML,

/** post when dclient received FacePro msg to upload FaceID---32
   *
   *@param param1 -- NULL
   *@param param2 -- NULL
   *@param lpData -- NULL
   *
 */
  MSG_D2P_RECV_UPLOAD_DEVICE_FACEID,

/** 设备上报配置---33
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CONFIG_MODULE
  *
 */
	MSG_D2P_REPORT_CONFIG_FORM_DEVICE,

/** 通知phone打电话---34
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MAKE_CALL
  *
 */
	MSG_D2P_REMOTE_ACTION_MAKE_CALL,
	
/** 通知phone挂断电话---35
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MAKE_CALL
  *
 */
	MSG_D2P_REMOTE_ACTION_HANGUP_CALL,
	/** ---36
	  *
	  *@param param1 -- NULL
	  *@param param2 -- type  ;  1 - file or 2 - buf
	  *@param lpData -- control4 message
	  *
	*/
	  MSG_D2P_CONTROL4_MESSAGE,
/** ---37
	*control4下发联系人列表
	*@param param1 -- NULL
	*@param param2 -- type  ;  1 - file or 2 - buf
	*@param lpData -- control4 message
	*
	*/

MSG_D2P_CONTROL4_DEVICE_LIST_INFO,

/** 通知phone 远程控制的消息---38
  *
  *@param param1 -- NULL
  *@param param2 -- NULL 
  *@param lpData -- DCLIENT_REMOTE_CONTROL_COMMON
  *
*/
  	MSG_D2P_REMOTE_CONTROL_COMMON,
  	

	/** 转发alarm消息给管理机---39
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL 
	  *@param lpData -- DCLIENT_MANAGE_ALARM_MSG
	  *
	*/
	MSG_D2P_MANAGE_ALARM,

	/** post when dclient receive fp file---3A
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- full path of local fp tgz file
	  *
	 */
	MSG_D2P_RECV_FP_TGZ,

	//---3B
	MSG_D2P_REQUEST_SENSOR_TRIGGER,
	/** Clear Face Xml ---3C
	  *
	  *@param param1 -- SDMC Clear Data
	  *@param param2 -- NULL
	  *@param lpData -- NULL
	 */
	MSG_D2P_CLEAR_FINGER_DATA,
	/** 请求触发状态---3D
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_ALL_TRIGGER_STARUS
	  *
	 */
	MSG_D2P_REQUEST_ALL_TRIGGER_STATUS,

	/** 上报触发状态---3E
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_ALL_TRIGGER_STARUS
	  *
	 */
	MSG_D2P_REPORT_ALL_TRIGGER_STATUS,
/** report door status to phone when indoor monitor received dclient message from door phone.---3F
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_DOORSTATUS
	  *
	 */
	  MSG_D2P_REPORT_DOOR_STATUS,
	  
	  /** .---40
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_VISITOR_AUTH_INFO
	  *
	 */
	  MSG_D2P_NOTIFY_VISITOR_AUTH,
	  
	/** ---41
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- full path of facedata file
	  *
	 */
	  MSG_D2P_RECV_FACEDATA,

	 /** ---42
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_SEND_TEMP_KEY
	  *
	 */
	  MSG_D2P_SEND_TEMP_KEY,
	  /** ---43
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_SEND_OSS_STS
	  *
	 */
	  MSG_D2P_SEND_OSS_STS,
	  
	 /** ---44
	  *
	  *@param param1 -- int; 0 NormalRelay; 1 SecurityRelay
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REMOTE_CONTROL_OPENDOOR
	  *
	 */
	  MSG_D2P_REMOTE_CONTROL_OPENDOOR,
	  
	 /** ---45
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_OPENDOOR_ACK
	  *
	 */
	  MSG_D2P_OPENDOOR_ACK,

	/** 注册人脸---46
	 *
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_FACE_INFO
	 *
	*/
	 MSG_D2P_REGISTER_FACE,

	/**修改人脸 ---47
	 *
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_FACE_INFO
	 *
	*/
	 MSG_D2P_MODIFY_FACE,

	/**删除人脸 ---48
	 *
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_FACE_INFO
	 *
	*/
	 MSG_D2P_DELETE_FACE,

	/**同步人脸---49
	 *
	 *@param param1 -- 0 本地下载;1-云公网下载
	 *@param param2 -- NULL
	 *@param lpData -- FILE PATH
	 *
	*/
     MSG_D2P_SYNC_FACE_PIC,
     
     /**更新时区---4A
	 *
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- FILE PATH
	 *
	*/
     MSG_D2P_UPDATE_TZ,

	/** 门禁用户数据---4B
	*
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_NOTIFY_HANDLE_FILE_INFO
	 *
	*/
	MSG_D2P_ACCESS_CONTROL_INFO,
	
	/** 门禁用户元数据---4C
	*
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_NOTIFY_HANDLE_FILE_INFO
	 *
	*/
	MSG_D2P_ACCESS_CONTROL_META,

	/** 请求设备上传人脸和用户信息---4D
	*
	 *@param param1 -- NULL
	 *@param param2 -- NULL
	 *@param lpData -- DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA
	 *
	*/
	MSG_D2P_REQUEST_PERSONEL_DATA,

	/** 通知设备同步人脸和用户信息---4E
		*
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA
		 *
	*/
	MSG_D2P_SYNC_PERSONEL_DATA,

	/** 通知设备同步人脸和用户信息---4F
		*
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT
		 *
	*/
	MSG_D2P_REQUEST_FINGER_PRINT,

	/** 通知设备同步人脸和用户信息---50
		*
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT
		 *
	*/
	MSG_D2P_SYNC_FINGER_PRINT,
	
	/** 通知处理schedule文件 ---51
		* 
		 *@param param1 -- NULL 
		 *@param param2 -- NULL 
		 *@param lpData --  DCLIENT_NOTIFY_HANDLE_FILE_INFO
		* 
	*/
	MSG_D2P_SCHEDULE_INFO,
	
	/** 开门记录上报的ACK ---52
		* 
		 *@param param1 -- NULL 
		 *@param param2 -- NULL 
		 *@param lpData -- DCLIENT_REPORT_ACTIVITY_ACK
		* 
	*/
	MSG_D2P_REPORT_ACTIVITY_ACK,

	/** 通知可以批量上传开门记录 ---53
		* 
		 *@param param1 -- NULL 
		 *@param param2 -- 0表示可以开始上传，1表示异常，可以停止上传 
		 *@param lpData -- NULL
		* 
	*/
	MSG_D2P_SYNC_ACTIVITY,

	/** 批量上传开门记录的ACK ---54
		* 
		 *@param param1 -- NULL 
		 *@param param2 -- NULL 
		 *@param lpData -- NULL
		* 
	*/
	MSG_D2P_SYNC_ACTIVITY_ACK,
	
   /** 语音助手统计ACK回复---55
   *
    *@param param1 -- NULL
    *@param param2 -- nSeq //MSG_P2D_SEND_WAKE_WORD中结构体携带的nSeq
    *@param lpData -- NULL
    *
   */
	MSG_D2P_SEND_WAKE_WORD_ACK,

	/** 通知phone保持relay常开或者常关---56
	  *
	   *@param param1 -- NULL
	   *@param param2 -- int //0表示Open，1表示Close
	   *@param lpData -- DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE
	   *
	*/
	MSG_D2P_REQUEST_KEEP_OPEN_CLOSE_RELAY,

	/** 通知phone开启考勤功能(之前的所有开门记录都不需要重传)---57
	  *
	   *@param param1 -- NULL
	   *@param param2 -- NULL 
	   *@param lpData -- NULL
	   *
	*/
	MSG_D2P_NOTIFY_ATTENDANCE_START,

	/** 通知phone关闭考勤功能(之后的数据都不需要再次重传)---57
	  *
	   *@param param1 -- NULL
	   *@param param2 -- NULL 
	   *@param lpData -- NULL
	   *
	*/
	MSG_D2P_NOTIFY_ATTENDANCE_STOP,
	
	/** 通知Phone备份文件---58
	  *
	   *@param param1 -- NULL
	   *@param param2 -- NULL 
	   *@param lpData -- DCLIENT_BACKUP_CONFIG
	   *
	*/
	MSG_D2P_BACKUP_CONFIG,
	
	/** 请求监控---59
	  *
	   *@param param1 -- NULL
	   *@param param2 -- NULL 
	   *@param lpData -- DCLIENT_REQUEST_RTSP_MONITOR
	   *
	*/
	MSG_D2P_REQUEST_RTSP_MONITOR,

	/** 请求监控停止---60
		*
		*@param param1 -- NULL
		*@param param2 -- NULL
		*@param lpData -- DCLIENT_RTSP_MONITOR_STOP
	 */
	 MSG_D2P_RTSP_MONITOR_STOP,
	 
	 /** 扫二维码注册终端账号---5A
		*
		*@param param1 -- NULL
		*@param param2 -- NULL
		*@param lpData -- DCLIENT_REG_END_USER
	 */
	 MSG_D2P_REG_END_USER,
	 
	 /** 下发通知设备是否是kit方案---5B
	  *
	  *@param param1 -- NULL
	  *@param param2 -- int  0,不是kit方案, 1,Kit方案;正常情况下都会是1，0的情况是不会下发的
	  *@param lpData -- NULL
	  */
	 MSG_D2P_REQUEST_IS_KIT,
	
	 /** 将设备的固件号和MAC地址传输给云---5C
		*
		*@param param1 -- 0
		*@param param2 -- 0
		*@param lpData -- DCLIENT_REPORT_KIT_DEVICE_LIST
	 */
	 MSG_D2P_REPORT_KIT_DEVICES,
};

enum
{
/** post when phone tigger an alarm
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_ALARM_MSG
  *
 */
	MSG_P2D_SEND_ALARM = MSG_P2D + 1,	

/** send text message---2
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_TEXT_MSG
  *
 */
	MSG_P2D_SEND_MSG,
	
/** send access info ---3
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_ACCESS_INFO
  *
 */	
	MSG_P2D_SEND_ACCESS_INFO,

/** create bind code---4
  *
  *@param param1 -- NULL
  *@param param2 -- unsigned int32 SERIAL NUM-----429496701
  *@param lpData -- NULL
  *
 */
	MSG_P2D_CREATE_BIND_CODE,
	
/** delete bind code---5
  *
  *@param param1 -- NULL
  *@param param2 -- unsigned int32 SERIAL NUM -----429496701
  *@param lpData -- bind code-----01010102Xxxxx
  *
 */
	MSG_P2D_DELETE_BIND_CODE,
	
/** get bind code list---6
  *
  *@param param1 -- NULL
  *@param param2 -- unsigned int32 SERIAL NUM -----429496701
  *@param lpData -- NULL
  *
 */
	MSG_P2D_GET_BIND_CODE_LIST,

/** post when phone deal an alarm---7
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_ALARM_DEAL_INFO
  *
 */
	MSG_P2D_SEND_ALARM_DEAL,

/** Discover Msg---8
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_DISCOVER_SEND
  *
 */
 	MSG_P2D_DISCOVER_SEND,
	
/** send Check tmp key ---9
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CHECK_TMP_KEY
  *
 */	
	MSG_P2D_SEND_CHECK_TMP_KEY,

/** Report Arming ---A
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_ARMING
  *
 */
 	MSG_P2D_REPORT_ARMING,

/** Push Noanswer Fwd Number---B
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_PUSH_NOANSWER_FWD_NUMBER
  *
 */
 	MSG_P2D_PUSH_NOANSWER_FWD_NUMBER,

/** motion alert---C
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MOTION_ALERT
  *
 */
 	MSG_P2D_MOTION_ALERT,

/** report activity---D
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_ACTIVITY
  *
 */
 	MSG_P2D_REPORT_ACTIVITY,

/** request arming---E
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REQUEST_ARMING
  *
 */
	MSG_P2D_REQUEST_ARMING,
/** report arming p2p---F
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_ARMING_P2P
  *
 */
	MSG_P2D_REPORT_ARMING_P2P,
	
/** check dtmf---10
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CHECK_DTMF
  *
 */
	MSG_P2D_CHECK_DTMF,
	
/**  ---11
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_DEVICE_CODE
  *
 */	
	MSG_P2D_REPORT_DEVICE_CODE,
	
/**  设备注册云平台账号---12
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_PERSONAL_REGISTER
  *
 */
	MSG_P2D_PERSONAL_REGISTER,

/**  设备登录云平台账号---13
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_PERSONAL_LOGIN
  *
 */
	MSG_P2D_PERSONAL_LOGIN,

/**  添加从账号---14
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_ADD_SLAVE_ACCOUNT
  *
 */
	MSG_P2D_ADD_SLAVE_ACCOUNT,

/**  获取从账号列表---15
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_GET_SLAVE_ACCOUNT_LIST
  *
 */
	MSG_P2D_GET_SLAVE_ACCOUNT_LIST,

/**  删除从账号---16
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_DEL_SLAVE_ACCOUNT
  *
 */
	MSG_P2D_DELETE_SLAVE_ACCOUNT,

/** 绑定设备---17
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_BIND_DEVICE
  *
 */
	MSG_P2D_BIND_DEVICE_BY_DEVICECODE,


/**判断邮件是否存在---18
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_EMAIL_EXIST
  *
 */
	MSG_P2D_EMAIL_EXIST,

/**通知上报网络信息---19
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_NETWORK_INFO
  *
 */
	MSG_P2D_REPORT_NETWORK_INFO,

/**自动更新DISCOVER消息---1A
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- 
  *
 */
	MSG_P2D_AUTO_DISCOVER,

/**获取motion and robincall 信息---1B
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- 
  *
 */
 	MSG_P2D_GET_MOTION_AND_ROBINCALL_INFO,

/**  设置motion---1C
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_SET_PERSONAL_ACCOUNT_MOTION
  *
 */
 	MSG_P2D_SET_PERSONAL_ACCOUNT_MOTION,

/**  设置ROBINCALL---1D
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_MOTION_AND_ROBINCALL
  *
 */
	MSG_P2D_SET_PERSONAL_ACCOUNT_ROBINCALL,

/**  设置dtmf---1E
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_SET_DTMF
  *
 */
	MSG_P2D_SET_DTMF,

/**  获取级联的配置--1F
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REQUEST_CONFIG
  *
 */
	MSG_P2D_REQUEST_CONFIG,
	
/**  phone下发回复给control4的消息--20
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_CONTROL4_RESPONSE_COMMON
  *
*/
	MSG_P2D_CONTROL4_RESPONSE_COMMON,
	
/**  phone下发上报通话截图信息---21
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_CALL_CAPTURE
  *
 */	
	MSG_P2D_REPORT_CALL_CAPTURE,

/**  phone下发上报Trigger---22
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_REPORT_TRIGGER
  *
 */
	MSG_P2D_REPORT_TRIGGER,

	/**  管理机广播消息--23
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_MANAGE_BROADCAST_MSG
	  *
	 */
	MSG_P2D_MANAGE_BROADCAST_MSG,
	
	/** REPORT HEALTH STATUS---24
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_HEALTH
	  *
	*/
	MSG_P2D_REPORT_HEALTH,
	
	/** MSG_P2D_RESPONSE_SENSOR_TRIGGER---25
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_SENSOR_TRIGGER
	*/
	MSG_P2D_RESPONSE_SENSOR_TRIGGER,
	
/** REPORT VIDEO INFO---26
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- DCLIENT_UPLOAD_VIDEO_NOTIFY
  *
*/
	MSG_P2D_UPLOAD_VIDEO_NOTIFY,

	
	/** 请求触发状态--27
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_ALL_TRIGGER_STATUS
	  *
	 */
	 
	MSG_P2D_REQUEST_ALL_TRIGGER_STATUS,

	/** 上报触发状态--28
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_ALL_TRIGGER_STATUS
	  *
	 */
	MSG_P2D_REPORT_ALL_TRIGGER_STATUS,
/** report door status (it should be used for doorphone, broadcast the door status to all indoor monitors when it be changed.)---29
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_DOORSTATUS
	  *
	 */
	  MSG_P2D_REPORT_DOOR_STATUS,


	/** report gas (it would be used for indoor monitor, reporting GAS count to SDMC or Cloud)---2A
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_GAS
	  *
	 */
	  MSG_P2D_REPORT_GAS,
	  
	  /** report VISITOR_INFO ---2B
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_VISITOR_INFO
	  *
	 */
	  MSG_P2D_REPORT_VISITOR_INFO,
	  
	   /** report VISITOR_AUTH_MSG ---2C
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REPORT_VISITOR_AUTH_INFO
	  *
	 */
	  MSG_P2D_REPORT_VISITOR_AUTH_INFO,

	 /** REQUEST_OSS_STS---2D
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REQUEST_OSS_STS
	  *
	 */
	  MSG_P2D_REQUEST_OSS_STS,
	  
	  /** REMOTE_CONTROL_OPENDOOR_RESPONSE---2E
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REMOTE_CONTROL_OPENDOOR
	  *
	 */
	  MSG_P2D_REMOTE_CONTROL_OPENDOOR_RESPONSE,
	  
	  /** REMOTE_CONTROL_OPENDOOR_RESPONSE---2F
	  *
	  *@param param1 -- int; 0 Normal Relay; 1 Security Relay
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REQUEST_OPENDOOR
	  *
	 */
	  MSG_P2D_REQUEST_OPENDOOR,

	  /** MSG_P2D_MAINTENANCE_ALARM_REPORT---30
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_MAINTENANCE_ALARM_REPORT
	  *
   	  */
	  MSG_P2D_MAINTENANCE_ALARM_REPORT,
	  
	  /** MSG_P2D_MAINTENANCE_ALARM_REPORT---31
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_REQUEST_ACINFO
	  *
   	  */
	  MSG_P2D_REQUEST_ACINFO,

	  /** MSG_P2D_SEND_DELIVERY_MSG---32
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_SEND_DELIVERY
	  *
   	  */
   	  MSG_P2D_SEND_DELIVERY_MSG,

	  /** MSG_P2D_SEND_WAKE_WORD---33
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_SEND_WAKE_WORD
	  *
   	  */
   	  MSG_P2D_SEND_WAKE_WORD,
   	  
   	  /** 通知ACMS正在准备人脸用户数据---34
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- NULL
	  *
   	  */
   	  MSG_P2D_UP_PERSONEL_DATA_WAITTING,
   	  
   	  /** 通知ACMS正在准备指纹数据---35
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- NULL
	  *
   	  */
   	  MSG_P2D_UP_FINGERPRINT_WAITTING,
   	  
	  /** --36
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_UPDATE_MD5
		  *
	  */
   	  MSG_P2D_ACCESS_CONTROL_META_ACK,
   	  
	  /** 37
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_UPDATE_MD5
		  *
	  */
   	  MSG_P2D_ACCESS_CONTROL_INFO_ACK,
   	  
   	  /** 批量上传打卡记录 --- 38
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_SYNC_ACTIVITY
		  *
	  */
   	  MSG_P2D_SYNC_ACTIVITY,

	  /** 通知SCHEDULE 写入MD5--39
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_UPDATE_MD5
		  *
	  */
   	  MSG_P2D_SCHEDULE_INFO_ACK,
   	  
   	  /** 通知上报Relay状态 -- 3A
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_REPORT_RELAY_STATUS
		  *
	  */
   	  MSG_P2D_REPORT_RELAY_STATUS,
   	  
	  /** Clear Face Xml ACK---3B
		*
		*@param param1 -- what kind of xml to clear,SDMC or FacePro ACK
		*@param param2 -- NULL
		*@param lpData -- DCLIENT_UPDATE_MD5
	   */
   	  MSG_P2D_CLEAR_FACE_DATA_ACK,
   	  
   	  /** Clear Finger Data ACK---3C
		*
		*@param param1 -- NULL
		*@param param2 -- NULL
		*@param lpData -- DCLIENT_UPDATE_MD5
	   */
   	  MSG_P2D_CLEAR_FINGER_DATA_ACK,

	  /** Recv AD ACK---3D
		 *
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_UPDATE_MD5
	 */
	  MSG_P2D_RECV_AD_ACK,
	  
	  /** Recv PRIVATEKEY ACK---3E
		 *
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_UPDATE_MD5
	 */
	  MSG_P2D_RECV_PRIVATEKEY_ACK,
	  
	   /** Recv PRIVATEKEY ACK---3F
		 *
		 *@param param1 -- NULL
		 *@param param2 -- NULL
		 *@param lpData -- DCLIENT_UPDATE_MD5
	 */
	  MSG_P2D_RECV_RFKEY_ACK,

	  /** Recv PRIVATEKEY ACK---40
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_UPDATE_MD5
	  */
	  MSG_P2D_RECV_ADDRESS_ACK,
	  
	   /** Recv COMMUNITY ACK---41
		  *
		  *@param param1 -- NULL
		  *@param param2 -- NULL
		  *@param lpData -- DCLIENT_UPDATE_MD5
	  */
	  MSG_P2D_RECV_COMMUNITY_ACK,

	  /** DEVICE_LIST_INFO ACK---42
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_UPDATE_MD5
   	  */
	  MSG_P2D_DEVICE_LIST_INFO_ACK,
	  
	  /** ACK---43
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_UPDATE_MD5
   	  */
	  MSG_P2D_RECV_FACEID_XML_ACK,

	  /** ACK---44
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_UPDATE_MD5
   	  */
	  MSG_P2D_RECV_DOWNLOAD_FACEPIC_XML_ACK,

	  /** ACK---45
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_UPDATE_MD5
   	  */
	  MSG_P2D_SYNC_FACE_PIC_ACK,

	  /** ACK---46
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_UPDATE_MD5
   	  */
	  MSG_P2D_RECV_FP_TGZ_ACK,
	  
	  /** 设备流量超额短信通知物业---46
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_FLOW_OUT_OF_LIMIT
   	  */
	  MSG_P2D_FLOW_OUT_OF_LIMIT,
	  
	  /** 设备上报通话记录---47
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_REPORT_CALLLOG
   	  */
	  MSG_P2D_REPORT_CALLLOG,

	  /** 设备通知上传配置备份---48
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_BACKUP_CONFIG_ACK
   	  */
	  MSG_P2D_BACKUP_CONFIG_ACK,
	  
	  /** 请求监控---49
	   *
	   *@param param1 -- NULL
	   *@param param2 -- NULL
	   *@param lpData -- DCLIENT_REQUEST_RTSP_MONITOR
   	  */
	  MSG_P2D_REQUEST_RTSP_MONITOR,

	  /** 请求监控停止---4A
		*
		*@param param1 -- NULL
		*@param param2 -- NULL
		*@param lpData -- DCLIENT_RTSP_MONITOR_STOP
	 */
	 MSG_P2D_RTSP_MONITOR_STOP,
	 
	  /** 请求注册二维码---49
		*
		*@param param1 -- 0
		*@param param2 -- 0
		*@param lpData -- NULL
	 */
	 MSG_P2D_REQUEST_END_USER_REG,

	 /** 将设备的固件号和MAC地址传输给云---4A
		*
		*@param param1 -- 0
		*@param param2 -- 0,自动扫描,1手动添加
		*@param lpData -- DCLIENT_REPORT_KIT_DEVICE_LIST
	 */
	 MSG_P2D_REPORT_KIT_DEVICES,

	 /** 请求当前家庭下的设备信息---4B
		 *
		 *@param param1 -- 0
		 *@param param2 -- 0
		 *@param lpData -- NULL
	  */
	 MSG_P2D_REQUEST_KIT_DEVICES,

	/** 请求当前家庭下的设备信息---4C
		 *
		 *@param param1 -- 0
		 *@param param2 -- 0
		 *@param lpData -- KIT_DEVICE_BASE_INFO,其中szSWVer和nType可以不设置值
	  */
	 MSG_P2D_MODIFY_DEVICE_LOCATION,

   	 /** 网卡切换---4D
		  *
		  *@param param1 -- 0
		  *@param param2 -- 0
		  *@param lpData -- NULL
	 */
	 MSG_P2D_ETHERNET_CHANGE,
	 
	/** REPORT CAPTURE INFO---4E
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_UPLOAD_CAPTURE_NOTIFY
	  *
	*/
	 MSG_P2D_UPLOAD_CAPTURE_NOTIFY,

	 /** 修改配置需要重启Dclient---4F
		*
		*@param param1 -- 0
		*@param param2 -- 0
		*@param lpData -- NULL
	*/
	 MSG_P2D_REBOOT_DCLIENT,

};

enum
{
/** pc manager 下发update消息时
  *
  *@param param1 -- NULL
  *@param param2 -- NULL
  *@param lpData -- NULL
  *
 */
	MSG_D2A_UPDATE_CONFIG = MSG_D2A + 1,

	/** 备份配置恢复 --- 2
	  *
	  *@param param1 -- NULL
	  *@param param2 -- NULL
	  *@param lpData -- DCLIENT_BACKUP_CONFIG_RECOVERY
	  *
	 */
	MSG_D2A_BACKUP_CONFIG_RECOVERY,


};

#ifdef __cplusplus
extern "C"
{
#endif



#ifdef __cplusplus
};
#endif

#endif
