#ifndef __WAITEVENT_H__
#define __WAITEVENT_H__

#if defined(_WIN32)
#include <windows.h>
typedef HANDLE EVENT_HANDLE;
typedef HANDLE MUTEX_HANDLE;

#else
#include <pthread.h>
typedef pthread_cond_t EVENT_HANDLE;
typedef pthread_mutex_t MUTEX_HANDLE;
#endif  // _WIN32


class CWaitEvent
{
public:
	CWaitEvent();
	~CWaitEvent();

	//设置事件
	void Set();

	//清除事件
	void Reset();

	//等待事件
	void Wait();

private:
	//事件句柄
	EVENT_HANDLE m_event;

	//互斥信号
	MUTEX_HANDLE m_mutex;
};

#endif
