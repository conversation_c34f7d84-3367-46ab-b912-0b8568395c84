#ifndef __LOCK_H__
#define __LOCK_H__

#if defined(_WIN32)
#include <windows.h>
typedef CRITICAL_SECTION LockHandler;
#define LOCK_INIT(lock) InitializeCriticalSection(lock)
#define LOCK_EXIT(lock) DeleteCriticalSection(lock)
#define LOCK_ON(lock) EnterCriticalSection(lock) 
#define LOCK_OFF(lock) LeaveCriticalSection(lock) 
#else
#include <pthread.h>
typedef pthread_mutex_t LockHandler;

#define LOCK_INIT(lock) pthread_mutex_init(lock, NULL)
#define LOCK_EXIT(lock) pthread_mutex_destroy(lock)
#define LOCK_ON(lock) pthread_mutex_lock(lock) 
#define LOCK_OFF(lock) pthread_mutex_unlock(lock) 

#endif  // _WIN32

class CLock
{
public:
	CLock();
	~CLock();

	//上锁
	void Lock();

	//解锁
	void Unlock();

private:
	LockHandler m_lock;
};

class CAutoLock
{
public:
	CAutoLock(CLock& lock);
	~CAutoLock();

private:
	CLock& m_lock;
};

#endif
