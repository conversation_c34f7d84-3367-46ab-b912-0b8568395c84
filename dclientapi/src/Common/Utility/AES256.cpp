#include "AES256.h"
#include "openssl/aes.h"
#include "string.h"
#include "Utility.h"
#include <rllog/rl_log.h>

void genKey(char *key, char *keyout, int nsize)
{
	if(key == NULL || keyout == NULL || nsize < KEY_LENGTH)
	{
		return;
	}
	int nSize = strlen(key);
	if(nSize > KEY_LENGTH)
	{
		nSize = KEY_LENGTH;
	}
	rl_strcpy_s(keyout, nSize+1, key);
	for(int i=nSize; i<KEY_LENGTH; i++)
	{
		keyout[i] = '0';
	}
	return;
}

void AES_256_DECRYPT(unsigned char *in, unsigned char *out, unsigned char *key, int nSize)
{
	if(in == NULL || key == NULL || out == NULL)
	{
		return;
	}
	AES_KEY aes_key;
	unsigned char iv[16];
	char szkey[KEY_LENGTH +1]={0};
	memset(iv,0,sizeof(iv));
	memset(&aes_key, 0, sizeof(AES_KEY));
	genKey((char*)key, szkey, sizeof(szkey));	
	AES_set_decrypt_key((const unsigned char*)szkey, 256, &aes_key);
	AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
}

void AES_256_DECRYPT_OFFSET(unsigned char *in, unsigned char *out, unsigned char *key, char *pOffset, int nSize)
{
	if(in == NULL || key == NULL || out == NULL)
	{
		return;
	}
	AES_KEY aes_key;
	unsigned char iv[16];
	memset(iv,0,sizeof(iv));
	rl_memcpy(iv, pOffset, sizeof(iv));
	char szkey[KEY_LENGTH +1]={0};
	memset(&aes_key, 0, sizeof(AES_KEY));
	genKey((char*)key, szkey, sizeof(szkey));	
	AES_set_decrypt_key((const unsigned char*)szkey, 256, &aes_key);
	AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
}

void AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char *key, int nSize)
{
	if(in == NULL || key == NULL || out == NULL)
	{
		return;
	}
	AES_KEY aes_key;
	unsigned char iv[16];
	char szkey[KEY_LENGTH +1]={0};
	memset(iv,0,sizeof(iv));
	memset(&aes_key, 0, sizeof(AES_KEY));
	genKey((char*)key, szkey, sizeof(szkey));	
	AES_set_encrypt_key((const unsigned char*)szkey, 256, &aes_key);
	AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
}

void AES_128_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char *key, int nSize)
{
	if(in == NULL || key == NULL || out == NULL)
	{
		return;
	}
	AES_KEY aes_key;
	unsigned char iv[16];
	char szkey[KEY_LENGTH +1]={0};
	memset(iv,'0',sizeof(iv));
	memset(&aes_key, 0, sizeof(AES_KEY));
	genKey((char*)key, szkey, sizeof(szkey));	
	AES_set_encrypt_key((const unsigned char*)szkey, 128, &aes_key);
	AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
}

void AES_128_DECRYPT(unsigned char *in, unsigned char *out, unsigned char *key, int nSize)
{
	if(in == NULL || key == NULL || out == NULL)
	{
		return;
	}
	AES_KEY aes_key;
	unsigned char iv[16];
	char szkey[KEY_LENGTH +1]={0};
	memset(iv,'0',sizeof(iv));
	memset(&aes_key, 0, sizeof(AES_KEY));
	genKey((char*)key, szkey, sizeof(szkey));	
	AES_set_decrypt_key((const unsigned char*)szkey, 128, &aes_key);
	AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
}

int FileAESEncrypt(char *pszFilePath, const char *pKey, char *pszDstFilePath)
{
	if(pszFilePath == NULL || pKey == NULL || pszDstFilePath == NULL)
	{
		return -1;
	}
	int nEncipherTextLen = 0;
	unsigned char *key = (unsigned char *)pKey;
	unsigned char *pEncipherText;
	unsigned char *pOriFileBuf;  //定义文件指针
	unsigned char *pEncryptFileBuf;
	int nHeaderLen = sizeof(AES_FILE_HEADER);
	//TRACE_INFO(_T("=====header len =====%d", nHeaderLen));
	AES_KEY aes_key;
	unsigned char iv[16];
	memset(iv,0,sizeof(iv));
	memset(&aes_key, 0, sizeof(AES_KEY));

	FILE *pFile = fopen(pszFilePath,"rb");
	if(NULL==pFile)
	{
		return -1;
	}
	fseek(pFile, 0, SEEK_END); //把指针移动到文件的结尾 ，获取文件长度
	int nOriFileLen = ftell(pFile); //获取文件长度
	int nFillOriFileLen =  ((nOriFileLen - 1)/16 + 1) * 16;

	UINT nEncryptFileLen = nFillOriFileLen + nHeaderLen;

	nEncipherTextLen = nFillOriFileLen;

	pOriFileBuf = new UCHAR[nEncipherTextLen];
	pEncryptFileBuf = new UCHAR[nEncryptFileLen];
	pEncipherText = new UCHAR[nEncipherTextLen];
	memset(pOriFileBuf, 0, nEncipherTextLen);
	memset(pEncryptFileBuf, 0, nEncryptFileLen);
	memset(pEncipherText, 0, nEncipherTextLen);

	rewind(pFile); //把指针移动到文件开头 因为我们一开始把指针移动到结尾，如果不移动回来 会出错
	fread(pOriFileBuf, 1, nOriFileLen, pFile);
	fclose(pFile);

	//组织文件头数据
	AES_FILE_HEADER *pHeader = (AES_FILE_HEADER *)pEncryptFileBuf;
	pHeader->byMagicMSB = AES_FILE_HEADER_MAGIC_MSB;
	pHeader->byMagicLSB = AES_FILE_HEADER_MAGIC_LSB;
	UINT nVersion = 1;
	pHeader->version = DCLIENT_HTONS(nVersion);
	pHeader->nFileSize = DCLIENT_HTONL(nOriFileLen);

	//加密
	AES_set_encrypt_key(key, 256, &aes_key);
	memset(iv,0,sizeof(iv));
	AES_cbc_encrypt(pOriFileBuf, pEncipherText, nFillOriFileLen, &aes_key, iv, AES_ENCRYPT);

	memcpy(pEncryptFileBuf + nHeaderLen, pEncipherText, nFillOriFileLen);
	//memcpy(pEncryptFileBuf, pEncipherText, nFillOriFileLen);

	//将文件头和加密后的文件数据写入文件
	FILE *fp = fopen(pszDstFilePath,"wb+");
	if(fp != NULL)
	{
		if(nEncryptFileLen != (int)(fwrite(pEncryptFileBuf, 1, nEncryptFileLen, fp)))
		{
			rl_log_err("Write error!");
		}
		fclose(fp);
		fp = NULL;
	}
	delete []pOriFileBuf;
	delete []pEncryptFileBuf;
	delete []pEncipherText;
	return 0;
}


int FileAESDecrypt(char *pszFilePath, const char *pKey, char * pDstFilePath)
{
	if(pszFilePath == NULL || pKey == NULL || pDstFilePath == NULL)
	{
		return -1;
	}
	UCHAR *pOriFileBuf = NULL;
	UCHAR *pEncryptFileBuf = NULL;
	CHAR *pszKey = (char*)pKey;
	UINT nOriFileLen = 0;
	UINT nEncryptFileLen = 0;
	int nVersion = 0;
	AES_KEY aes_key;
	unsigned char iv[16];
	int nRet = 0;

	//读取文件数据
	FILE *pFile = fopen(pszFilePath, "rb");
	if(pFile == NULL)
	{
		return -1;
	}

	fseek(pFile, 0, SEEK_END);
	nEncryptFileLen = ftell(pFile);
	pEncryptFileBuf = new UCHAR[nEncryptFileLen];
	rewind(pFile);
	fread(pEncryptFileBuf, 1, nEncryptFileLen, pFile);
	fclose(pFile);

	//判断MAGICSUM
	AES_FILE_HEADER *pHeader = (AES_FILE_HEADER *)pEncryptFileBuf;
	if((pHeader->byMagicMSB != AES_FILE_HEADER_MAGIC_MSB) || (pHeader->byMagicLSB != AES_FILE_HEADER_MAGIC_LSB))
	{
		nRet = -1;		
		goto OpenfileAndDecrypt_Exit;
	}
	//判断version
	nVersion = DCLIENT_NTOHS(pHeader->version);
	if(nVersion != 1)
	{
		nRet = -1;
		goto OpenfileAndDecrypt_Exit;
	}
	//获取长度
	nOriFileLen = DCLIENT_NTOHL(pHeader->nFileSize);

	//对收到的数据进行安全性判断
	if(nEncryptFileLen < nOriFileLen || nEncryptFileLen<(int)sizeof(AES_FILE_HEADER))
	{
		nRet = -1;
		rl_log_err("receive data error");
		goto OpenfileAndDecrypt_Exit;
	}
	//根据version解密对应的数据
	pOriFileBuf = new UCHAR[nEncryptFileLen-sizeof(AES_FILE_HEADER)];

	memset(&aes_key, 0, sizeof(AES_KEY));
	AES_set_decrypt_key((unsigned char*)pszKey, 256, &aes_key);
	memset(iv, 0, sizeof(iv));
	AES_cbc_encrypt(pEncryptFileBuf + sizeof(AES_FILE_HEADER),  pOriFileBuf, nEncryptFileLen-sizeof(AES_FILE_HEADER), &aes_key, iv, AES_DECRYPT);

	//将解密后的文件数据写入文件
	pFile = fopen(pDstFilePath, "wb+");
	if(pFile != NULL)
	{
		if((int)nOriFileLen != (int)(fwrite(pOriFileBuf, 1, nOriFileLen, pFile)))
		{
			rl_log_err("Write error!");
		}
		fclose(pFile);
		pFile = NULL;
	}
OpenfileAndDecrypt_Exit:
	if(pOriFileBuf != NULL)
	{
		delete []pOriFileBuf;
	}
	if(pEncryptFileBuf != NULL)
	{
		delete []pEncryptFileBuf;
	}
	return nRet;
}
