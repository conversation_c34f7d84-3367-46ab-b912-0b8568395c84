#ifndef __AES_INCLUDED__
#define __AES_INCLUDED__

#include "rl_msg.h"

#define KEY_LENGTH 32
#define AES_KEY_DEFAULT_MAC			"0C11050000FF"//fix me:TEXT ALARM 这一类设备跟设备通讯的，统一用统一MAC进行加密
#define AES_KEY_DEFAULT_MASK		"Akuvox55069013Akuvox"
#define AES_ENCRYPT_KEY_V1 		"Akuvox55069013!@Akuvox55069013!@"
#define AES_BASE64_MAX_LEN		2048
#define AES_OFFSET_DEFAULT		"1234567887654321"
#define AES_KEY_V1_MASK			"Akuvox55069013!@"

typedef struct AES_FILE_HEADER_T
{
#define AES_FILE_HEADER_MAGIC_MSB 0xAA
#define AES_FILE_HEADER_MAGIC_LSB 0xAE	
	UCHAR byMagicMSB;
	UCHAR byMagicLSB;
	USHORT version;
	UINT nFileSize;
	UINT nReserved1;
	UINT nReserved2;
}AES_FILE_HEADER;

void genKey(char *key, char*keyout, int nsize);

VOID AES_256_DECRYPT(unsigned char* in, unsigned char* out, unsigned char *key, int nSize);
void AES_256_DECRYPT_OFFSET(unsigned char *in, unsigned char *out, unsigned char *key, char *pOffset, int nSize);
VOID AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char *key, int nSize);
void AES_128_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char *key, int nSize);
void AES_128_DECRYPT(unsigned char *in, unsigned char *out, unsigned char *key, int nSize);
int FileAESDecrypt(char *pszFilePath, const char *pKey, char *pszDstFilePath);
int FileAESEncrypt(char *pszFilePath, const char *pKey, char *pszDstFilePath);

#endif

