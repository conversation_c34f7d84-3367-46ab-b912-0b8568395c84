#include "Utility.h"
#include "DclientMsg.h"
#include "string.h"
#include <rl_log.h>
#include <openssl/md5.h>
#include "AES256.h"
#include "Base64.h"
#include <dirent.h>
#include "curl.h"
#include <sys/stat.h> 


static CHAR *g_pszDeviceTypeName[] = 
{
	"STATIR",
	"DOOR",
	"INDOOR",
	"MANAGEMENT",
	"WALL",
	"SDMC",
};

//判断IP是否合法
BOOL IsValidIPAddr(char *pszIPAddr)
{
	int nFirstPart = -1;
	int nSecondPart = -1;
	int nThirdPart = -1;
	int nFourthPart = -1;
	if(pszIPAddr == NULL)
	{
		return FALSE;
	}
	sscanf(pszIPAddr,"%d.%d.%d.%d",&nFirstPart,&nSecondPart,&nThirdPart,&nFourthPart);  
    if (0<=nFirstPart && nFirstPart<=255  
     && 0<=nSecondPart && nSecondPart<=255  
     && 0<=nThirdPart && nThirdPart<=255  
     && 0<=nFourthPart && nFourthPart<=255) 
    {
    	return TRUE;
    }
	else
	{
		return FALSE;
	}
}

//判断端口是否合法
BOOL IsValidPort(UINT nPort)
{
	return TRUE;
}

//解析配置项的KEY和VALUE
int ParseConfigItemText(CHAR *pszLine, CHAR *pszKey, int nKeySize, CHAR *pszValue, int nValueSize)
{
	if((pszLine == NULL) || (pszKey == NULL) || (pszValue == NULL))
	{
		return -1;
	}

	CHAR *pszFromEqual = strchr(pszLine, '=');
	if(pszFromEqual == NULL)
	{
		rl_strcpy_s(pszKey, nKeySize, pszLine);
		rl_strcpy_s(pszValue, nValueSize, "");
		return 0;
	}
	
	int nKeyLen = pszFromEqual - pszLine;
	if(nKeyLen <= 0)
	{
		rl_strcpy_s(pszKey, nKeySize, "");
		rl_strcpy_s(pszValue, nValueSize, "");
		return -1;
	}
	
	strncpy(pszKey, pszLine, nKeyLen);
	rl_strcpy_s(pszValue, nValueSize, pszFromEqual + 1);

	return 0;
}

//获取URL中的文件名称
int GetFileNameByUrl(CHAR *pszFileName, int nSize, CHAR *pszUrl)
{
	if(rl_str_isempty(pszUrl) || (pszFileName == NULL))
	{
		return -1;
	}

	CHAR *pszName = strrchr(pszUrl, '/');
	if(pszName == NULL)
	{
		return -1;
	}

	rl_strcpy_s(pszFileName, nSize, pszName + 1);

	return 0;
}

#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
static char *gpDownload302URL = NULL;
static int DownloadUrlToFile302Callback(char *pUrl) 
{
	if (gpDownload302URL)
		free(gpDownload302URL);
	gpDownload302URL = strdup(pUrl);
	return 0;
}
#endif

//下载文件
int DownloadUrlToFile(char *pszUrl, char *pszFileName, int nRetryCount, int nMaxDownloadSize)
{
#if 0
	if(rl_str_isempty(pszUrl) || rl_str_isempty(pszFileName))
	{
		return -1;
	}
	TIMEOUT timeout ={255000, nRetryCount};
	BOOL bDownLoadSuccess = FALSE;
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	if (gpDownload302URL != NULL)
		free(gpDownload302URL);
	
	gpDownload302URL = NULL;
	set_rps_redirect_url_callback(DownloadUrlToFile302Callback);
#endif
	for(int i=0; i<3; i++)
	{
		if(rfo_download_to_file(pszUrl, pszFileName, timeout, nMaxDownloadSize) < 0)
		{
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
			if (gpDownload302URL!=NULL) {
				rl_log_err("redirect 302 new url:%s\n", gpDownload302URL);
				pszUrl = gpDownload302URL;
			}
#endif			
			usleep(200*1000);
			continue;
		}
		bDownLoadSuccess = TRUE;
		break;
	}
	if(!bDownLoadSuccess)
	{
		return -1;
	}
	char cmd[1024] = {0};
	rl_sprintf_s(cmd, sizeof(cmd), "chmod 777 \"%s\"", pszFileName);
	if(rl_system_100ms_ex(cmd, 15, 0) < 0)
	{
		//return -1;
		return 0;
	}
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	if (gpDownload302URL) {
		free(gpDownload302URL);
		gpDownload302URL = NULL;
	}
#endif
#endif
	return 0;
}


//获取字符串去掉某字符之后的字符串
int StrtokString(char *pszSource, char *pszDst, int nSize, char *pszChar)
{
	if(rl_str_isempty(pszSource) || rl_str_isempty(pszChar) || pszDst == NULL)
	{
		return -1;
	}
	for(int i=0,j=0; i<rl_strlen(pszSource); i++)
	{
		if((pszSource[i] != *pszChar) && (j<nSize-1))
		{
			pszDst[j] = toupper(pszSource[i]);
			j++;
		}
	}
	return 0;
}

int MACStandard(char *pszSource, char *pszDst, int nSize)
{
	if(pszSource == NULL || pszDst == NULL)
	{
		return -1;
	}
	if(rl_strchr(pszSource, ':') != NULL)
	{
		rl_strcpy_s(pszDst, nSize, pszSource);
	}
	else
	{
		char szTmpDst[MAC_SIZE] = {0};
		for(int i=0,j=0; i<rl_strlen(pszSource); i++)
		{
			szTmpDst[j++] = pszSource[i];
			if((i+1) % 2 == 0 && i < rl_strlen(pszSource)-1)
			{
				szTmpDst[j++] = ':';
			}
		}
		rl_memset(pszDst, 0, nSize);
		rl_strcpy_s(pszDst, nSize, szTmpDst);
	}
	return 0;
}

void MD5Encrypt(char *pszSrc, char *szMD5Final, int nLen)
{
	if (!pszSrc || rl_str_isempty(pszSrc) || !szMD5Final)
	{
		return;
	}
	rl_memset(szMD5Final, 0, nLen);
	MD5_CTX context;
	unsigned char Md5Digist[16];
	CHAR Md5Final[MD5_SIZE] = {0};
	MD5_Init (&context);
	MD5_Update (&context, (unsigned char *)pszSrc, strlen(pszSrc));
	MD5_Final (Md5Digist, &context);

	for (int i=0; i<16; i++)
	{
		rl_sprintf_s(&Md5Final[2*i], 3, "%02x", Md5Digist[i]);	
	}
	rl_memcpy(szMD5Final, Md5Final, nLen);
}

// 计算文件的MD5
int GetFileMD5(char *pFilePath, char *pCurrentMd5, int size)
{
	FILE *file;
	MD5_CTX context;
	int len;
	unsigned char Md5Digist[16];
	unsigned char buffer[1024];

	if(NULL == pCurrentMd5 || NULL == pFilePath)
	{
		rl_log_err("GetFileMD5 failed, the input pFilePath or pCurrentMd5 is NULL.");
		return RL_FAILED;
	}

	if ((file = fopen (pFilePath, "rb")) == NULL)
	{
		rl_log_err("GetFileMD5 failed, open file <%s> failed.\n", pFilePath);
		return RL_FAILED;
	}
	else
	{
		MD5_Init (&context);
		while ((len = fread (buffer, 1, 1024, file))>0)
		{
			MD5_Update (&context, buffer, len);
		}
		MD5_Final (Md5Digist, &context);

		for (int i=0; i<16; i++)
		{
			if(2*i < size)
				rl_sprintf_s(&pCurrentMd5[2*i], 3, "%02x", (unsigned char)Md5Digist[i]);
		}

		fclose (file);
	}

	return RL_SUCCESS;
}

//获取设备类型名称
INT GetDeviceTypeName(UINT nDeviceType, CHAR* pszDeviceTypeName, INT nSize)
{
	if(nDeviceType >= SDMC_DEVICE_TYPE_MAX || nDeviceType < 0 || pszDeviceTypeName == NULL || nSize <= 0)
	{
		return -1;
	}
	rl_strcpy_s(pszDeviceTypeName, nSize, g_pszDeviceTypeName[nDeviceType]);
	return 0;
}

//将类似1_1.1.1.1.2-1 替换成INDOOR_1.1.1.1.2-1
INT ReplaceDeviceTypeToTypeName(CHAR* pszDeviceInfo, CHAR* pszDeviceInfoOut, INT nSize)
{
	if(pszDeviceInfo == NULL || pszDeviceInfoOut == NULL || nSize <= 0)
	{
		return -1;
	}
	//解析出type、deviceid和extension
	INT nType = -1;
	CHAR szDeviceID[DEVICE_ID_SIZE] = {0};
	CHAR szDeviceTypeName[DEVICE_TYPE_SIZE] = {0};
	if(rl_strchr(pszDeviceInfo, '_') != NULL)
	{	
		sscanf(pszDeviceInfo, "%d_%[0-9a-zA-Z.-]", &nType,szDeviceID);
	}
	GetDeviceTypeName(nType, szDeviceTypeName, sizeof(szDeviceTypeName));
	if(!rl_str_isempty(szDeviceTypeName))
	{
		rl_memset(pszDeviceInfoOut, 0, nSize);
		rl_sprintf_s(pszDeviceInfoOut, nSize, "%s_%s", szDeviceTypeName, szDeviceID);
	}
	else
	{
		rl_strcpy_s(pszDeviceInfoOut, nSize, pszDeviceInfo);
	}
	return 0;
}

cJSON *TransCharBufToJson(char *pBuf)
{
	if(pBuf == NULL || rl_strchr(pBuf, '{') == NULL)
	{
		return NULL;
	}
	cJSON *json;
	
	json = cJSON_Parse(pBuf);
	if (!json) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		return NULL;
	}
	return json;
}

INT FreeJson(cJSON *json)
{
	if(json != NULL)
	{
		cJSON_Delete(json);
		json = NULL;
	}
	return 0;
}

int AesBase64Decrypt(const char *instr, char *outstr, const char *key)
{
	//先进行BASE64解码
	unsigned int len = 0;
	unsigned char *pBufOutCode = NULL;
	char szCiphertext[AES_BASE64_MAX_LEN] = {0};
	rl_strcpy_s(szCiphertext, sizeof(szCiphertext), instr);
	pBufOutCode = base64Decode(szCiphertext, &len, 1);


	//解密
	char *pszOutBuf = new char[len + 1];
	memset(pszOutBuf, 0, len + 1);
	
	AES_128_DECRYPT((unsigned char*)pBufOutCode, (unsigned char*)pszOutBuf, (unsigned char*)key, len);	
	
	//去掉填充值
	int nFillByte = pszOutBuf[rl_strlen(pszOutBuf) - 1];
	if(nFillByte < 16)
	{
		int i=0;
		for(i=0; i<nFillByte; i++)
		{
			if(pszOutBuf[rl_strlen(pszOutBuf) - 1 - i] != nFillByte)
			{
				break;
			}
		}
		if(i == nFillByte)
		{
			pszOutBuf[rl_strlen(pszOutBuf) - nFillByte] = 0;
		}
	}

	memcpy(outstr, pszOutBuf, rl_strlen(pszOutBuf)+1);

	delete []pszOutBuf;
	delete []pBufOutCode;

	return 0;
}

int AesBase64Encrypt(const char *instr, char *outstr, const char *key)
{
	//先进行AES加密
	if(NULL == instr || NULL == outstr)
	{
		return -1;
	}

	//对MSG进行AES加密
	int nLenth = rl_strlen(instr);

	int nFillByte = (16 - nLenth%16)%16;

	if(nLenth%16 != 0)
	{
		nLenth -= nLenth%16;
		nLenth += 16;
	}

	unsigned char *pszInBuf = new unsigned char[nLenth + 1];
	memset(pszInBuf, nFillByte, nLenth + 1);
	memcpy(pszInBuf, instr, rl_strlen(instr));


	//加密
	char *pszOutBuf = new char[nLenth + 1];
	memset(pszOutBuf, 0, nLenth + 1);

	AES_128_ENCRYPT((unsigned char*)pszInBuf, (unsigned char*)pszOutBuf, (unsigned char*)key, nLenth);

	//进行BASE64编码
	char *pszBufCode = base64Encode((char *)pszOutBuf, nLenth);

	memcpy(outstr, pszBufCode, strlen(pszBufCode) + 1);

	delete []pszOutBuf;	
	delete []pszBufCode;
	delete []pszInBuf;

	return 0;
}

int AesDecryptByKey(char *pIn, char *pOut, INT nDataSize, char *pKey, char *pOffset)
{
	if(pIn == NULL || pOut == NULL || pKey == NULL)
	{
		return -1;
	}

	//对MSG进行AES解密
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);
	if(pOffset == NULL)
	{
		AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pKey, nDataSize);	
	}
	else
	{
		AES_256_DECRYPT_OFFSET((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pKey, pOffset, nDataSize);
	}
	memcpy(pOut, pszOutBuf, rl_strlen(pszOutBuf)+1);
	delete []pszOutBuf;
	return 0;
}

/** 
 * @brief URLEncode 对字符串URL编码 
 * 
 * @param str 原字符串 
 * @param strSize 原字符串长度(不包括最后的\0) 
 * @param result 结果缓冲区的地址 
 * @param resultSize 结果缓冲区的大小(包括最后的\0) 
 * 
 * @return: >0:resultstring 里实际有效的长度 
 *            0: 解码失败. 
 */  
INT URLEncode(const char* str, const int strSize, char* result, const int resultSize)  
{  
    int i;  
    int j = 0;//for result index  
    char ch; 
	char *ptmpbuf = NULL;
    if ((str==NULL) || (result==NULL) || (strSize<=0) || (resultSize<=0)) {  
        return 0;  
    }  
	ptmpbuf = new char[resultSize];
	memset(ptmpbuf, 0, resultSize);
    for ( i=0; (i<strSize)&&(j<resultSize-1); ++i) {  
        ch = str[i];  
        if (((ch>='A') && (ch<='Z')) ||  
            ((ch>='a') && (ch<='z')) ||  
            ((ch>='0') && (ch<='9'))) {  
            ptmpbuf[j++] = ch;  
        } else if (ch == ' ') {  
            ptmpbuf[j++] = '+';  
        } else if (ch == '.' || ch == '-' || ch == '_' || ch == '*') {  
            ptmpbuf[j++] = ch;  
        } else {  
            if (j+3 < resultSize) {  
                sprintf(ptmpbuf+j, "%%%02X", (unsigned char)ch);  
                j += 3;  
            } else { 
            	delete[] ptmpbuf;
				ptmpbuf = NULL;
                return 0;  
            }  
        }  
    }   	
    ptmpbuf[j] = '\0';
	rl_strcpy_s(result, resultSize, ptmpbuf);
	delete[] ptmpbuf;
	ptmpbuf = NULL;
    return j;  
} 

//非递归删除当前目录下的指令类型的文件
int UnlinkFileByKeyWord(char *pszPath, char *pFileType)
{
	if(pszPath == NULL || pFileType == NULL)
	{
		return -1;
	}
	DIR	*pDir ;
	struct dirent  *ent  ;
	pDir=opendir(pszPath);
	while((ent=readdir(pDir))!=NULL)//调用readdir,不断得到文件信息
	{
		if(ent->d_type & DT_DIR)//递归的暂时不做处理
        {
        }
        else
        {
			if(rl_strstr(ent->d_name, pFileType) != NULL)
			{
				char szFilePath[512] = {0};
				rl_sprintf_s(szFilePath, sizeof(szFilePath), "%s%s", pszPath,ent->d_name);
				unlink(szFilePath);
			}
        }
	}
	if(pDir != NULL)
	{
		closedir(pDir);
	}
	return 0;
}

int CreateRandomPassWord(CHAR *pszPassword, UINT nSize, UINT nDigits)
{
	if(nSize < nDigits)
	{
		return -1;
	}
	CHAR szPasswdChar[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
	int nPwdCharSize = rl_strlen(szPasswdChar);
	srand((unsigned int)time(0));
	for(int i=0; i<nDigits; i++)
	{
		int nRandNum = rand()%nPwdCharSize;
		pszPassword[i] = szPasswdChar[nRandNum];
	}
	return 0;
}

int GetRandomNumFromTo(INT nBegin, INT nEnd)
{
	if(nBegin > nEnd)
	{
		return 0;
	}
	int nRandNum = rand()%(nEnd - nBegin + 1);
	return nRandNum+nBegin;
}

//Get IP string from UCHAR
CHAR *GetIpFromUchar(UCHAR *pIPAddr)
{
	static CHAR szIPAddr[IP_SIZE];
	UCHAR *p = (UCHAR *)pIPAddr;
	rl_sprintf_s(szIPAddr, sizeof(szIPAddr), "%d.%d.%d.%d", p[0], p[1], p[2], p[3]);

	return szIPAddr;
}

int GetMacHexFromString(const CHAR *pszMac, UCHAR *pMacHex)
{
	if((pszMac == NULL) || (pMacHex == NULL))
	{
		return -1;
	}
	
	CHAR szTmp[3] = {0};
//	UCHAR c = 0;
	for(int i=0; i<12; i+=2)
	{
		szTmp[0] = pszMac[i];
		szTmp[1] = pszMac[i+1];
		szTmp[2] = '\0';
			
		pMacHex[i/2] = strtoul(szTmp, NULL, 16);
	}

	return 0;
}

static size_t WriteDataCallBack(void *ptr, size_t size, size_t nmemb, void *userdata)
{
	if(ptr == NULL || userdata == NULL)
	{
		return 0;
	}
	CURL_WRITE_CALLBACK_BUF *pcallBackBuf = (CURL_WRITE_CALLBACK_BUF*)userdata;
	int nLastSize = pcallBackBuf->nRecvSize;
	rl_memcpy((char *)pcallBackBuf->pRecvBuf+nLastSize, ptr, size*nmemb);
	nLastSize += size*nmemb;
	pcallBackBuf->nRecvSize = nLastSize;
	return size*nmemb;
}

//内存需要主动释放
static size_t WriteDataCallBackNew(void *data, size_t size, size_t nmemb, void *userp)
{
	size_t realsize = size * nmemb;
	CURL_WRITE_CALLBACK_BUF *mem = (CURL_WRITE_CALLBACK_BUF*)userp;
	char *ptr = (char *)realloc(mem->pRecvBuf, mem->nRecvSize + realsize + 1);
	if(ptr == NULL)
		return 0;  /* out of memory! */

	mem->pRecvBuf = ptr;
	rl_memcpy((char *)mem->pRecvBuf + mem->nRecvSize, data, realsize);
	mem->nRecvSize += realsize;
	*((char*)mem->pRecvBuf + mem->nRecvSize) = 0;
	return realsize;
}

static size_t WriteHeaderDataCallBack(void *ptr, size_t size, size_t nmemb, void *userdata)
{
	if(ptr == NULL || userdata == NULL)
	{
		return 0;
	}
	CURL_WRITE_CALLBACK_BUF *pcallBackBuf = (CURL_WRITE_CALLBACK_BUF*)userdata;
	int nLastSize = rl_strlen((char*)pcallBackBuf->pRecvBuf);
	rl_memcpy((char *)pcallBackBuf->pRecvBuf+nLastSize, ptr, size*nmemb);
	return size*nmemb;
}

//下载文件
int DownloadUrlToFileByCurl(char *pszUrl, char *pszFileName, int nRetryCount, int nMaxDownloadSize, CURL_DOWNUPLOAD_FILE_CALLBACK fileSizeCallback)
{
	if(pszUrl == NULL || pszFileName == NULL)
	{
		return -1;
	}
	
	FILE *pFile = fopen(pszFileName, "wb+");
	if (pFile == NULL)
	{
		rl_log_err("%s: Failed to open file <%s>", __FUNCTION__, pszFileName);
		return -1;
	}
	
	CURL *curl = NULL;
	CURLcode res = CURLE_OK;
	//curl_global_init(CURL_GLOBAL_ALL);

	curl = curl_easy_init();
	char szUrl[URL_SIZE_MAX] = { 0 };

	if(curl == NULL)
	{
		fclose(pFile);
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}
			
	rl_sprintf_s(szUrl, sizeof(szUrl), "%s", pszUrl);

	curl_easy_setopt(curl, CURLOPT_URL, szUrl);
	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false); // 跳过证书验证（https）的网站无法跳过，会报错
 	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证
	curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
	curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);//5s的超时连接

	/* abort if slower than 1024 bytes/sec during 60 seconds */
	curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, 60L);
	curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, 1024L); 

	//curl_easy_setopt(curl, CURLOPT_TIMEOUT, 20);
	//curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION,WriteDateCallBack);
	curl_easy_setopt(curl, CURLOPT_WRITEDATA, pFile);//这是WriteDateCallBack的第四个参数
	curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
	curl_easy_setopt(curl, CURLOPT_MAXFILESIZE, nMaxDownloadSize); //tftp不生效
	if(fileSizeCallback != NULL)
	{
		curl_easy_setopt(curl, CURLOPT_NOPROGRESS, false);//设置为false才能设置CURLOPT_PROGRESSFUNCTION
		curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, fileSizeCallback);
	}
	res = curl_easy_perform(curl);

	char szLogMsg[VALUE_SIZE] = { 0 };
	rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: download %s failed: %s, try again\n", __FUNCTION__, szUrl, szLogMsg);
		res = curl_easy_perform(curl);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: download %s failed: %s \n", __FUNCTION__, szUrl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: download %s success\n", __FUNCTION__, szUrl);
			break;
		}
	}
	rl_log_debug("%s: download %s success\n", __FUNCTION__, szUrl);

	/* always cleanup */
	curl_easy_cleanup(curl);
	//curl_global_cleanup();
	fclose(pFile);

	int nFileSize = GetFileSize(pszFileName);
	if(nMaxDownloadSize > 0 && nFileSize > nMaxDownloadSize)
	{
		rl_log_err("%s: the %s size(%d) large than the max size(%d)", __FUNCTION__, pszFileName, nFileSize, nMaxDownloadSize);
		unlink(pszFileName);
	}
	else
	{
		//修改权限
		char szChmodCmd[VALUE_SIZE] = { 0 };
		rl_sprintf_s(szChmodCmd, sizeof(szChmodCmd), "chmod 777 %s", pszFileName);
		rl_system_100ms_ex(szChmodCmd, 3, 1);
	}		
	return (res == CURLE_OK) ? 0 : -1;
}


int SendRequestUrlByCurl(const char *pszUrl, int nType, char *pszPostData, char *pszHeadData, char *&pszRecvBuf, int &nSize, char *&pszHeadBuf)
{
	if(pszUrl == NULL)
	{
		return -1;
	}
	CURL *curl = NULL;
	struct curl_slist *headers=NULL;
	CURLcode res = CURLE_OK;
	//curl_global_init(CURL_GLOBAL_ALL);
	curl = curl_easy_init();
	char szUrl[BUF_SIZE] = { 0 };
	if(curl == NULL)
	{
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}
	
	CURL_WRITE_CALLBACK_BUF callBackBuf;
	rl_memset(&callBackBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
	CURL_WRITE_CALLBACK_BUF callBackHeaderBuf;
	rl_memset(&callBackHeaderBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));	
	rl_sprintf_s(szUrl, sizeof(szUrl), "%s", pszUrl);
	
	if(pszHeadData != NULL && rl_strlen(pszHeadData)>0)
	{
		headers = curl_slist_append(headers, pszHeadData);
		curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);//设置HTTP头
	}
	if(nType == HTTP_REQUEST_METHOD_POST)
	{
		curl_easy_setopt(curl, CURLOPT_POST, 1);            //设置请求为post类型
	}
	if(pszPostData != NULL && rl_strlen(pszPostData)>0)
	{
		curl_easy_setopt(curl, CURLOPT_POSTFIELDS, pszPostData);//post发送的数据
	}
	//if(pszHeadBuf != NULL)
	{						
		curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, WriteDataCallBackNew);
		curl_easy_setopt(curl, CURLOPT_HEADERDATA, &callBackHeaderBuf);
	}
	curl_easy_setopt(curl, CURLOPT_URL, szUrl);
	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false); // 跳过证书验证（https）的网站无法跳过，会报错
 	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证
	curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
	curl_easy_setopt(curl, CURLOPT_TIMEOUT, 20);
	curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteDataCallBackNew);
	curl_easy_setopt(curl, CURLOPT_WRITEDATA, &callBackBuf);//这是WriteDataCallBack的第四个参数
	curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);	
	res = curl_easy_perform(curl);

	int nRetryCount = 3;
	char szLogMsg[VALUE_SIZE] = { 0 };
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: request %s failed: %s, try again\n", __FUNCTION__, szUrl, szLogMsg);
		res = curl_easy_perform(curl);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: request %s failed: %s \n", __FUNCTION__, szUrl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: request %s success\n", __FUNCTION__, szUrl);
			break;
		}
	}
	rl_log_debug("%s: request %s %s\n", __FUNCTION__, szUrl, res==CURLE_OK ? "succeed":"failed");

	/* always cleanup */
	curl_easy_cleanup(curl);
	//curl_global_cleanup();
	nSize = callBackBuf.nRecvSize;
	pszRecvBuf = (char*)callBackBuf.pRecvBuf;
	pszHeadBuf = (char*)callBackHeaderBuf.pRecvBuf;
	return (res == CURLE_OK) ? 0 : -1;
}

int DownloadUrlToMemoryByCurl(char *pszUrl, int nRetryCount, int nMaxDownloadSize, void *psrcData, int *nSrcLen, CURL_DOWNUPLOAD_FILE_CALLBACK fileSizeCallback)
{
	if(pszUrl == NULL || psrcData == NULL)
	{
		return -1;
	}
	CURL *curl = NULL;
//	struct curl_slist *headers=NULL;
	CURLcode res = CURLE_OK;
	//curl_global_init(CURL_GLOBAL_ALL);
	curl = curl_easy_init();
	char szUrl[URL_SIZE_MAX] = { 0 };
	if(curl == NULL)
	{
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}		
	CURL_WRITE_CALLBACK_BUF callBackBuf;
	rl_memset(&callBackBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
	//callBackBuf.nRecvSize = nSrcLen;
	callBackBuf.pRecvBuf = (char*)psrcData;
	rl_sprintf_s(szUrl, sizeof(szUrl), "%s", pszUrl);
	
	curl_easy_setopt(curl, CURLOPT_URL, szUrl);
	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false); // 跳过证书验证（https）的网站无法跳过，会报错
 	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证
	curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
	//curl_easy_setopt(curl, CURLOPT_TIMEOUT, 20);
	curl_easy_setopt(curl, CURLOPT_MAXFILESIZE, nMaxDownloadSize);//tftp无效，需要特别处理
	curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteDataCallBack);
	curl_easy_setopt(curl, CURLOPT_WRITEDATA, &callBackBuf);//这是WriteDataCallBack的第四个参数
	curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);
	if(fileSizeCallback != NULL)
	{
		curl_easy_setopt(curl, CURLOPT_NOPROGRESS, false);//设置为false才能设置CURLOPT_PROGRESSFUNCTION
		curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, fileSizeCallback);
	}
	res = curl_easy_perform(curl);

	char szLogMsg[VALUE_SIZE] = { 0 };
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: failed: %s, try again\n", __FUNCTION__, szUrl, szLogMsg);
		res = curl_easy_perform(curl);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: failed: %s \n", __FUNCTION__, szUrl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: %s success\n", __FUNCTION__, szUrl);
			break;
		}
	}
	rl_log_debug("%s: %s success;size=%d\n", __FUNCTION__, szUrl,*nSrcLen);

	/* always cleanup */
	curl_easy_cleanup(curl);
	//curl_global_cleanup();
	*nSrcLen = callBackBuf.nRecvSize;
	return (res == CURLE_OK) ? 0 : -1;
}

std::string GetStrConnectModeByMode(int nMode)
{
	char szMode[VALUE_SIZE] = {0};
	switch(nMode)
	{
		case 0:
		{
			rl_strcpy_s(szMode, sizeof(szMode), "DISCOVER");
		}
		break;
		case 1:
		{
			rl_strcpy_s(szMode, sizeof(szMode), "SDMC");
		}
		break;
		case 2:
		{
			rl_strcpy_s(szMode, sizeof(szMode), "CLOUD");
		}
		break;
		default:
			rl_strcpy_s(szMode, sizeof(szMode), "ERROR");
		break;
	}
	std::string strMode = szMode;
	return strMode;
}

int SendRequestUrlByCurl(DCLIENT_CURL_HTTP_REQUEST &curlHttpRequest)
{
	if(curlHttpRequest.pUrl == NULL)
	{
		return -1;
	}
	BOOL bHeadFlag = FALSE;
	CURL *curl = NULL;
	struct curl_slist *headers = NULL;
	CURLcode res = CURLE_OK;
	//curl_global_init(CURL_GLOBAL_ALL);
	curl = curl_easy_init();
	char szUrl[BUF_SIZE] = { 0 };
	if(curl == NULL)
	{
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}
	rl_sprintf_s(szUrl, sizeof(szUrl), "%s", curlHttpRequest.pUrl);
	
	if(curlHttpRequest.pHeadData != NULL)//是否有头数据
	{
		headers = curl_slist_append(headers, curlHttpRequest.pHeadData);
		bHeadFlag = TRUE;		
	}
	if(curlHttpRequest.nContentType == HTTP_REQUEST_CONTENT_TYPE_JSON)
	{
		headers = curl_slist_append(headers, "Content-Type: application/json");
		bHeadFlag = TRUE;
	}
	if(bHeadFlag)
	{
		curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);//设置HTTP头
	}
	if(curlHttpRequest.nRequestMethod == HTTP_REQUEST_METHOD_POST)
	{
		curl_easy_setopt(curl, CURLOPT_POST, 1);//设置请求为post类型
		if(curlHttpRequest.pPostData != NULL)
		{
			curl_easy_setopt(curl, CURLOPT_POSTFIELDS, curlHttpRequest.pPostData);//post发送的数据
		}
	}
	CURL_WRITE_CALLBACK_BUF callBackHeaderBuf;
	rl_memset(&callBackHeaderBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
	{	
		curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, WriteDataCallBackNew);
		curl_easy_setopt(curl, CURLOPT_HEADERDATA, &callBackHeaderBuf);
	}
	//鉴权方式
	switch(curlHttpRequest.nAuthMethod)
	{
	case HTTP_AUTH_METHOD_NONE:
		{
		}
		break;
	case HTTP_AUTH_METHOD_BASIC:
		{
			curl_easy_setopt(curl, CURLOPT_USERNAME, curlHttpRequest.pAuthUser);
			curl_easy_setopt(curl, CURLOPT_PASSWORD, curlHttpRequest.pAuthPassword);
			curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
		}
		break;
	case HTTP_AUTH_METHOD_DIGEST:
		{		
			curl_easy_setopt(curl, CURLOPT_USERNAME, curlHttpRequest.pAuthUser);
			curl_easy_setopt(curl, CURLOPT_PASSWORD, curlHttpRequest.pAuthPassword);
			curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
		}
		break;
	default:
		break;
	}
	curl_easy_setopt(curl, CURLOPT_URL, szUrl);
	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false); // 跳过证书验证（https）的网站无法跳过，会报错
 	curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书验证
	curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1);
	curl_easy_setopt(curl, CURLOPT_TIMEOUT, 20);
	
	CURL_WRITE_CALLBACK_BUF callBackBuf;
	rl_memset(&callBackBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
	{		
		curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteDataCallBackNew);
		curl_easy_setopt(curl, CURLOPT_WRITEDATA, &callBackBuf);//这是WriteDataCallBack的第四个参数
	}
	curl_easy_setopt(curl, CURLOPT_VERBOSE, 1);	
	res = curl_easy_perform(curl);

	int nRetryCount = 3;
	char szLogMsg[VALUE_SIZE] = { 0 };
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: request %s failed: %s, try again", __FUNCTION__, szUrl, szLogMsg);
		res = curl_easy_perform(curl);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: request %s failed: %s", __FUNCTION__, szUrl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: request %s success", __FUNCTION__, szUrl);
			break;
		}
	}
	rl_log_debug("%s: request %s %s\n", __FUNCTION__, szUrl, res==CURLE_OK ? "succeed":"failed");

	/* always cleanup */
	curl_easy_cleanup(curl);
	//curl_global_cleanup();
	curlHttpRequest.nRecvSize = callBackBuf.nRecvSize;
	curlHttpRequest.pHeadRecvBuf = (char*)callBackHeaderBuf.pRecvBuf;
	curlHttpRequest.pRecvBuf = (char*)callBackBuf.pRecvBuf;
	return (res == CURLE_OK) ? 0 : -1;
}

long GetDownloadFileSizeByCurl(const char *pszurl)
{
	if(pszurl == NULL)
	{
		return -1;
	}
	long nDownloadFileLenth = 0;
	CURL *handle = curl_easy_init();
	CURLcode res = CURLE_OK;
	if(handle == NULL)
	{
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}
	CHAR szHeadBuf[1024] = {0};
	CURL_WRITE_CALLBACK_BUF callBackHeaderBuf;
	rl_memset(&callBackHeaderBuf, 0, sizeof(CURL_WRITE_CALLBACK_BUF));
	callBackHeaderBuf.pRecvBuf = szHeadBuf;
	
	curl_easy_setopt(handle, CURLOPT_URL, pszurl);  
	curl_easy_setopt(handle, CURLOPT_HEADER, 1);    //只要求header头
	curl_easy_setopt(handle, CURLOPT_NOBODY, 1);    //不需求body  	
	curl_easy_setopt(handle, CURLOPT_HEADERFUNCTION, WriteHeaderDataCallBack);
	curl_easy_setopt(handle, CURLOPT_HEADERDATA, &callBackHeaderBuf);
	res = curl_easy_perform(handle);
	
 	int nRetryCount = 3;
	char szLogMsg[VALUE_SIZE] = { 0 };
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: request %s failed: %s, try again", __FUNCTION__, pszurl, szLogMsg);
		res = curl_easy_perform(handle);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: request %s failed: %s", __FUNCTION__, pszurl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: request %s success", __FUNCTION__, pszurl);
			break;
		}
	}
	if(res == CURLE_OK)
	{
		CHAR *pContLength = rl_strstr((const char*)szHeadBuf, "Content-Length: ");
		if(pContLength != NULL)
		{	
			CHAR szTmp[64] = {0};
			sscanf(pContLength, "%s %d", szTmp,&nDownloadFileLenth);
		}
	}
		
	/* always cleanup */
	curl_easy_cleanup(handle);
	//curl_global_cleanup();
	return nDownloadFileLenth;
}

/*
	pszUrl -- 上传的URL地址
	pszRemoteName --远程的文件名
	pszLocalFile --本地的文件路径
*/
int UploadFileByCurl(const char *pszUrl, const char *pszRemoteName, const char *pszLocalFilePath)
{
	if(pszUrl == NULL || pszRemoteName == NULL || pszLocalFilePath == NULL)
	{
		return -1;
	}
	
	FILE *hd_src;
    struct stat file_info;
    curl_off_t fsize;

	/* get the file size of the local file */
    if(stat(pszLocalFilePath, &file_info)) {
		rl_log_err("%s: Couldnt open '%s'",__FUNCTION__, pszLocalFilePath);
        return -1;
    }
	fsize = (curl_off_t)file_info.st_size;

	/* get a FILE * of the same file */
    hd_src = fopen(pszLocalFilePath, "rb");
	CURL *handle = curl_easy_init();
	CURLcode res = CURLE_OK;
	if(handle == NULL)
	{
		rl_log_err("%s: curl is NULL.", __FUNCTION__);
		return -1;
	}
	
	/* we want to use our own read function */
  	//curl_easy_setopt(handle, CURLOPT_READFUNCTION, read_callback);
 
  	/* enable uploading */
  	curl_easy_setopt(handle, CURLOPT_UPLOAD, 1L);
 
  	/* specify target */
	char szFullUrl[URL_SIZE] = {0};
	rl_sprintf_s(szFullUrl, sizeof(szFullUrl), "%s/%s", pszUrl, pszRemoteName);
  	curl_easy_setopt(handle, CURLOPT_URL, szFullUrl);

 	/* now specify which pointer to pass to our callback */
  	curl_easy_setopt(handle, CURLOPT_READDATA, hd_src);
 
  	/* Set the size of the file to upload */
  	curl_easy_setopt(handle, CURLOPT_INFILESIZE_LARGE, (curl_off_t)fsize);

	curl_easy_setopt(handle, CURLOPT_CONNECTTIMEOUT, 5L);//5s的超时连接

	
  	/* Now run off and do what you've been told! */
  	res = curl_easy_perform(handle);

	int nRetryCount = 3;
	char szLogMsg[VALUE_SIZE] = { 0 };
	while(res != CURLE_OK && --nRetryCount >= 0)
	{
		rl_log_err("%s: request %s failed: %s, try again", __FUNCTION__, pszUrl, szLogMsg);
		res = curl_easy_perform(handle);
		rl_strcpy_s(szLogMsg, sizeof(szLogMsg), curl_easy_strerror(res));
		if (res != CURLE_OK)
		{
			rl_log_err("%s: request %s failed: %s", __FUNCTION__, pszUrl, szLogMsg);
		}
		else
		{
			rl_log_debug("%s: request %s success", __FUNCTION__, pszUrl);
			break;
		}
	}
	curl_easy_cleanup(handle);
	return 0;
}

long GetFileSize(const char *pszFile)
{
	if(pszFile == NULL)
	{
		return 0;
	}
	struct stat statbuf;
    stat(pszFile, &statbuf);
    int size=statbuf.st_size;
    return size;
}

//AK凯撒加密算法
std::string AKKSEncryptMAC(const CHAR *pData, int nSize)
{
	if(pData == NULL || nSize <= 0)
	{
		return "";
	}
	CHAR szEncryptMAC[VALUE_SIZE] = {0};
	CHAR szFull[VALUE_SIZE] = {0};
	CreateRandomPassWord(szFull, sizeof(szFull), 15);
	int nKey = GetRandomNumFromTo(0, 9);

	CHAR szTmp[MAC_SIZE] = {0};
	rl_strcpy_s(szTmp, sizeof(szTmp), pData);
	for(int i=0; i<nSize; i++)
	{
		if(szTmp[i] >= '0' && szTmp[i] <= '9')
		{
			szTmp[i] = '0' + (szTmp[i] - '0' + nKey) % 10;
		}
		else if(szTmp[i] >= 'a' && szTmp[i] <= 'z')
		{
			szTmp[i] = 'a' + (szTmp[i] - 'a' + nKey) % 26;
		}
		else if(szTmp[i] >= 'A' && szTmp[i] <= 'Z')
		{
			szTmp[i] = 'A' + (szTmp[i] - 'A' + nKey) % 26;
		}	
	}
	CHAR szFullPart1[VALUE_SIZE] = {0};
	CHAR szFullPart2[VALUE_SIZE] = {0};
	CHAR szFullPart3[VALUE_SIZE] = {0};
	CHAR szMACPart1[VALUE_SIZE] = {0};
	CHAR szMACPart2[VALUE_SIZE] = {0};
	CHAR szMACPart3[VALUE_SIZE] = {0};
	sscanf(szTmp, "%4s%4s%4s", szMACPart1, szMACPart2, szMACPart3);
	sscanf(szFull, "%3s%4s%4s", szFullPart1, szFullPart2, szFullPart3);
	rl_sprintf_s(szEncryptMAC, sizeof(szEncryptMAC), "%d%s%s%s%s%s%s", nKey, szFullPart1, szMACPart1, szFullPart2, szMACPart2, szFullPart3, szMACPart3);
	std::string strEncryptMAC = szEncryptMAC;
	return strEncryptMAC;
}

int LogSplit(const char*pData, int nSize, const char *pFunction)
{
	if(pData == NULL || nSize <=0 || pFunction == NULL)
	{
		return -1;
	}
	int nSizePart1 = nSize/2;
	int nSizePart2 = nSize - nSizePart1;
	char *pDataPart1 = new char[nSizePart1+1];
	memset(pDataPart1, 0, nSizePart1+1);
	char *pDataPart2 = new char[nSizePart2+1];
	memset(pDataPart2, 0, nSizePart2+1);
	rl_strcpy_s(pDataPart1, nSizePart1, pData);
	rl_strcpy_s(pDataPart2, nSizePart2, pData+nSizePart1-1);
	rl_log_debug("%s:\r\n%s", pFunction, pDataPart1);
	rl_log_debug("%s:\r\n%s", pFunction, pDataPart2);
	delete pDataPart1;
	delete pDataPart2;
	return 0;
}

int cfg_get_string(unsigned int cfg_id, unsigned int key_id, char *buf, int size, char *default_val)
{
	return 0;
}

int cfg_save()
{
	return 0;
}


