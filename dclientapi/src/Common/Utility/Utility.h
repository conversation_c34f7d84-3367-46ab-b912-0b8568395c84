#ifndef __UTILITY_H__
#define __UTILITY_H__
#pragma once

#include "rl_msg.h"
#include "cJSON.h"
#include <string.h>
#include <string>

#ifndef DCLIENT_HTONS
#define DCLIENT_HTONS	htons
#endif
#ifndef DCLIENT_NTOHS
#define DCLIENT_NTOHS	ntohs
#endif
#ifndef DCLIENT_HTONL
#define DCLIENT_HTONL	htonl
#endif
#ifndef DCLIENT_NTOHL
#define DCLIENT_NTOHL	ntohl
#endif

typedef int (*CURL_DOWNUPLOAD_FILE_CALLBACK)(void *clientp, double dltotal, double dlnow, double ultotal, double ulnow);

typedef struct DEVICE_TYPE_MAP_T
{
#define SECTION_NAME_SIZE 24
	UINT nTypeID;
	CHAR szSectionName[SECTION_NAME_SIZE];
}DEVICE_TYPE_MAP;

typedef struct CURL_WRITE_CALLBACK_BUF_T
{
	VOID *pRecvBuf;
	int nRecvSize;
}CURL_WRITE_CALLBACK_BUF;

typedef enum{
	HTTP_AUTH_METHOD_NONE,
	HTTP_AUTH_METHOD_BASIC,
	HTTP_AUTH_METHOD_DIGEST,
}HTTP_AUTH_METHOD;

typedef enum{
	HTTP_REQUEST_METHOD_NONE,
	HTTP_REQUEST_METHOD_GET,
	HTTP_REQUEST_METHOD_POST,
}HTTP_REQUEST_METHOD;

typedef enum{
	HTTP_REQUEST_CONTENT_TYPE_NONE,
	HTTP_REQUEST_CONTENT_TYPE_JSON,
}HTTP_REQUEST_CONTENT_TYPE;

typedef enum DCLIENT_PROTOCAL_E{
	DCLIENT_PROTOCOL_HTTPS = 0,
	DCLIENT_PROTOCOL_HTTP,
	DCLIENT_PROTOCOL_TFTP,
	DCLIENT_PROTOCOL_FTP,
}DCLIENT_PROTOCAL;


typedef struct DCLIENT_CURL_HTTP_REQUEST_T
{
	int nRequestMethod;
	int nAuthMethod;
	int nRecvSize;
	int nContentType;
	char *pUrl;
	char *pPostData;
	char *pAuthUser;
	char *pAuthPassword;
	char *pHeadData;
	char *pHeadRecvBuf;
	char *pRecvBuf;
}DCLIENT_CURL_HTTP_REQUEST;

//判断IP是否合法
BOOL IsValidIPAddr(char *pszIPAddr);

//判断端口是否合法
BOOL IsValidPort(UINT nPort);

//解析配置项的KEY和VALUE
int ParseConfigItemText(CHAR *pszLine, CHAR *pszKey, int nKeySize, CHAR *pszValue, int nValueSize);

//获取URL中的文件名称
int GetFileNameByUrl(CHAR *pszFileName, int nSize, CHAR *pszUrl);

//下载文件
int DownloadUrlToFile(char *pszUrl, char *pszFileName, int nRetryCount, int nMaxDownloadSize);


//获取字符串去掉某字符之后的字符串
int StrtokString(char *pszSource, char *pszDst, int nSize, char *pszChar);

//MAC标准化处理
int MACStandard(char *pszSource, char *pszDst, int nSize);

//对字符串进行MD5计算
void MD5Encrypt(char *pszSrc, char *szMD5Final, int nLen);

// 计算文件的MD5
int GetFileMD5(char *pFilePath, char *pCurrentMd5, int size);
INT GetDeviceTypeName(UINT nDeviceType, CHAR* pszDeviceTypeName, INT nSize);

INT ReplaceDeviceTypeToTypeName(CHAR* pszDeviceInfo, CHAR* pszDeviceInfoOut, INT nSize);

cJSON *TransCharBufToJson(char *pBuf);
std::string GetStrConnectModeByMode(int nMode);
INT FreeJson(cJSON *json);
int AesBase64Decrypt(const char *instr, char *outstr, const char *key);
int AesBase64Encrypt(const char *instr, char *outstr, const char *key);
int AesDecryptByKey(char *pIn, char *pOut, INT nDataSize, char *pKey, char *pOffset = NULL);
INT URLEncode(const char* str, const int strSize, char* result, const int resultSize);
int UnlinkFileByKeyWord(char *pszPath, char *pFileType);
int CreateRandomPassWord(CHAR *pszPassword, UINT nSize, UINT nDigits);
int GetRandomNumFromTo(INT nBegin, INT nEnd);

//Get IP string from UCHAR
CHAR *GetIpFromUchar(UCHAR *pIPAddr);
int GetMacHexFromString(const CHAR *pszMac, UCHAR *pMacHex);
int DownloadUrlToFileByCurl(char *pszUrl, char *pszFileName, int nRetryCount, int nMaxDownloadSize, CURL_DOWNUPLOAD_FILE_CALLBACK fileSizeCallback = NULL);
int SendRequestUrlByCurl(const char *pszUrl, int nType, char *pszPostData, char *pszHeadData, char *&pszRecvBuf, int &nSize, char *&pszHeadBuf);
int DownloadUrlToMemoryByCurl(char *pszUrl, int nRetryCount, int nMaxDownloadSize, void *psrcData, int *nSrcLen, CURL_DOWNUPLOAD_FILE_CALLBACK fileSizeCallback = NULL);
int SendRequestUrlByCurl(DCLIENT_CURL_HTTP_REQUEST &curlHttpRequest);
long GetDownloadFileSizeByCurl(const char *pszurl);
int UploadFileByCurl(const char *pszUrl, const char *pszRemoteName, const char *pszLocalFilePath);

long GetFileSize(const char *pszFile);
std::string AKKSEncryptMAC(const CHAR *pszMAC, int nSize);
int LogSplit(const char*pData, int nSize, const char *pFunction);

int cfg_get_string(unsigned int cfg_id, unsigned int key_id, char *buf, int size, char *default_val);
int cfg_save();	


#endif

