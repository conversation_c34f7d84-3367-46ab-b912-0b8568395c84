#pragma once

enum
{
	LOG_LEVEL_EMERG,
	LOG_LEVEL_ALERT,
	LOG_LEVEL_CRIT,
	LOG_LEVEL_ERR,
	LOG_LEVEL_WARNING,
	LOG_LEVEL_NOTICE,
	LOG_LEVEL_INFO,
	LOG_LEVEL_DEBUG
};

int rl_log_init(const char* module_name, int in_log_level);
void rl_log_deinit();
void rl_log_reset_level(int log_level);
void rl_log_debug(const char *fmt, ...);
void rl_log_info(const char *fmt, ...);
void rl_log_warn(const char *fmt, ...);
void rl_log_err(const char *fmt, ...);
void rl_log_notice(const char *fmt, ...);
void rl_log_crit(const char *fmt, ...);
void rl_log_alert(const char *fmt, ...);
void rl_log_emerg(const char *fmt, ...);