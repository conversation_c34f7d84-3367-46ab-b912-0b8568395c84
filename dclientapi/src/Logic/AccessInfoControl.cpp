#include "MsgControl.h"
#include "ConnectControl.h"
#include "AccessInfoControl.h"
#include "dclient_ipc.h"

CAccessInfoControl *GetAccessInfoControlInstance()
{
	return CAccessInfoControl::GetInstance();
}

CAccessInfoControl::CAccessInfoControl()
{

}

CAccessInfoControl::~CAccessInfoControl()
{

}

CAccessInfoControl *CAccessInfoControl::instance = NULL;

CAccessInfoControl *CAccessInfoControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CAccessInfoControl();
	}

	return instance;
}

//发送AccessControl消息
int CAccessInfoControl::SendAccessInfo(DCLIENT_ACCESS_INFO *pAccessInfo)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildAccessInfo(&socketMsg, pAccessInfo) < 0)
	{
		rl_log_err("%s: BuildAccessInfo failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug("SendAccessInfo success.");
	return 0;
}


