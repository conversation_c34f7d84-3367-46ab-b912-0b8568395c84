#include "MsgControl.h"
#include "ActivityControl.h"
#include "ConnectControl.h"
#include "DclientDefine.h"
#include "Utility.h"
#if RL_SUPPORT_REPORT_ACTIVITY
CActivityControl *GetActivityControlInstance()
{
	return CActivityControl::GetInstance();
}

CActivityControl::CActivityControl()
{
	
}

CActivityControl::~CActivityControl()
{
	
}

CActivityControl *CActivityControl::instance = NULL;

CActivityControl *CActivityControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CActivityControl();
	}

	return instance;
}

int CActivityControl::SendMotionAlert(DCLIENT_MOTION_ALERT *pMotionAlert)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildMotionAlertMsg(&socketMsg, pMotionAlert) < 0)
	{
		rl_log_err("%s: BuildMotionAlertMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);

	return 0;
}

int CActivityControl::SendReportActivity(DCLIENT_REPORT_ACTIVITY *pReportActivity)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildReportActivityMsg(&socketMsg, pReportActivity) < 0)
	{
		rl_log_err("%s: BuildReportActivityMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);

	return 0;
}
#endif

