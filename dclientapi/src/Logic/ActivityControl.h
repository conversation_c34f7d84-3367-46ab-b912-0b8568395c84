#ifndef __ACTIVITY_CONTROL_H__
#define __ACTIVITY_CONTROL_H__
#include "dclient_ipc.h"

class CActivityControl
{
public:
	CActivityControl();
	~CActivityControl();


	int SendMotionAlert(DCLIENT_MOTION_ALERT *pMotionAlert);
	int SendReportActivity(DCLIENT_REPORT_ACTIVITY *pReportActivity);
	
	static CActivityControl *GetInstance();
private:
	static CActivityControl *instance;

};

CActivityControl *GetActivityControlInstance();

#endif

