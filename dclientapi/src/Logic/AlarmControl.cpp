#include "MsgControl.h"
#include "ConnectControl.h"
#include "AlarmControl.h"
#include "dclient_ipc.h"
#include "DclientDefine.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "Lock.h"

CAlarmControl *GetAlarmControlInstance()
{
	return CAlarmControl::GetInstance();
}

CAlarmControl::CAlarmControl()
{
	INIT_LIST_HEAD(&m_listAlarmMsg);
#if RL_SUPPORT_ARMING_P2P
	INIT_LIST_HEAD(&m_listRequestArmingMsg);
	INIT_LIST_HEAD(&m_listReportArmingMsg);
#endif
	m_lock = new CLock();
}

CAlarmControl::~CAlarmControl()
{
	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
}

CAlarmControl *CAlarmControl::instance = NULL;

CAlarmControl *CAlarmControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CAlarmControl();
	}

	return instance;
}

//发送ALARM消息
int CAlarmControl::SendAlarmMsg(DCLIENT_ALARM_MSG *pszAlarmMsg, int nSendType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildAlarmMsg(&socketMsg, pszAlarmMsg) < 0)
	{
		rl_log_err("%s: BuildAlarmMsg failed.", __FUNCTION__);
		return -1;
	}
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		if(GetConnectControlInstance()->SendUdpMsg(pszAlarmMsg->to_name, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	rl_log_debug("SendAlarmMsg success.");
	return 0;
}

//发送ALARM DEAL消息
int CAlarmControl::SendAlarmDealMsg(DCLIENT_ALARM_DEAL_INFO *pAlarmDeal)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildAlarmDealMsg(&socketMsg, pAlarmDeal) < 0)
	{
		rl_log_err("%s: BuildAlarmDealMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}
#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
int CAlarmControl::ReportArming(DCLIENT_REPORT_ARMING *pReportArming, int nSendType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	ReplaceDeviceTypeToTypeName(pReportArming->from, pReportArming->from, sizeof(pReportArming->from));
	ReplaceDeviceTypeToTypeName(pReportArming->to, pReportArming->to, sizeof(pReportArming->to));
	pReportArming->nSync = REPORT_ARMING_SYNC_MODE_NONE;
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		if (GetMsgControlInstance()->BuildReportArmingMsg(&socketMsg, pReportArming, TRUE) < 0)
		{
			rl_log_err("%s: BuildReportArmingMsg failed.", __FUNCTION__);
			return -1;
		}
		if(GetConnectControlInstance()->SendUdpMsg(pReportArming->to_ip, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(pReportArming->nActionType != REPORT_ARMING_ACTION_TYPE_GET)
		{
			pReportArming->nSync = REPORT_ARMING_SYNC_MODE_NORESPONSE;
		}
		if (GetMsgControlInstance()->BuildReportArmingMsg(&socketMsg, pReportArming) < 0)
		{
			rl_log_err("%s: BuildReportArmingMsg failed.", __FUNCTION__);
			return -1;
		}
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}
#endif


#if RL_SUPPORT_ARMING_P2P
int CAlarmControl::SendRequestArming(DCLIENT_REQUEST_ARMING *pRequestArming, int nSendType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	ReplaceDeviceTypeToTypeName(pRequestArming->from, pRequestArming->from, sizeof(pRequestArming->from));
	ReplaceDeviceTypeToTypeName(pRequestArming->to, pRequestArming->to, sizeof(pRequestArming->to));
	BOOL bForceMAC = FALSE;
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		bForceMAC = TRUE;
	}
	if (GetMsgControlInstance()->BuildRequestArmingMsg(&socketMsg, pRequestArming, bForceMAC) < 0)
	{
		rl_log_err("%s: BuildRequestArmingMsg failed.", __FUNCTION__);
		return -1;
	}
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		if(GetConnectControlInstance()->SendUdpMsg(pRequestArming->to_ip, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}
#endif

//收到ALARM消息
int CAlarmControl::OnRecvAlarmSend(SOCKET_MSG_ALARM_SEND *pAlarmSend)
{
	if(pAlarmSend == NULL)
	{
		return -1;
	}
	DLCIENT_RECV_ALARM_DATA alarm;
	memset(&alarm, 0, sizeof(DLCIENT_RECV_ALARM_DATA));

	alarm.id = pAlarmSend->id;
	alarm.nExtension = pAlarmSend->nExtension;
	alarm.nDeviceType = pAlarmSend->nDeviceType;
	alarm.id = pAlarmSend->id;
	alarm.nSequenceNum = pAlarmSend->nSequenceNum;
	alarm.nDuration = pAlarmSend->nDuration;
	alarm.nAlarmCode= pAlarmSend->nAlarmCode;
	alarm.unAlarmCustomize = pAlarmSend->unAlarmCustomize;
	alarm.unAlarmLocation = pAlarmSend->unAlarmLocation;
	alarm.unAlarmZone = pAlarmSend->unAlarmZone;
	rl_strcpy_s(alarm.szAddress, sizeof(alarm.szAddress), pAlarmSend->szAddress);
	rl_strcpy_s(alarm.szType, sizeof(alarm.szType), pAlarmSend->szType);
	rl_strcpy_s(alarm.szTime, sizeof(alarm.szTime), pAlarmSend->szTime);
	rl_strcpy_s(alarm.szMAC, sizeof(alarm.szMAC), pAlarmSend->szMAC);
	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_ALARM, 0, 0, &alarm, sizeof(alarm));
}

//收到ALARM DEAL消息
int CAlarmControl::OnRecvAlarmDealNotify(SOCKET_MSG_ALARM_DEAL *pAlarmDeal)
{
	if(pAlarmDeal == NULL)
	{
		return -1;
	}

	DCLIENT_ALARM_DEAL_INFO alarmDeal;
	memset(&alarmDeal, 0, sizeof(DCLIENT_ALARM_DEAL_INFO));	
	alarmDeal.id = pAlarmDeal->id;
	alarmDeal.nAlarmCode = pAlarmDeal->nAlarmCode;
	alarmDeal.unAlarmCustomize = pAlarmDeal->unAlarmCustomize;
	alarmDeal.unAlarmLocation = pAlarmDeal->unAlarmLocation;
	alarmDeal.unAlarmZone = pAlarmDeal->unAlarmZone;
	rl_strcpy_s(alarmDeal.szUser, sizeof(alarmDeal.szUser), pAlarmDeal->szUser);	
	rl_strcpy_s(alarmDeal.szResult, sizeof(alarmDeal.szResult), pAlarmDeal->szResult);	
	rl_strcpy_s(alarmDeal.szType, sizeof(alarmDeal.szType), pAlarmDeal->szType);
	rl_strcpy_s(alarmDeal.szTime, sizeof(alarmDeal.szTime), pAlarmDeal->szTime);

	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ALARM_DEAL_NOTIFY, 0, 0, &alarmDeal, sizeof(alarmDeal));
}

//收到ALARM ACK消息
int CAlarmControl::OnRecvAlarmAck(SOCKET_MSG_ALARM *pAlarmMsg)
{
	if(pAlarmMsg == NULL)
	{
		return -1;
	}


	DCLIENT_MSG_ACK alarmMsgAck;
	rl_memset(&alarmMsgAck, 0, sizeof(DCLIENT_MSG_ACK));
	rl_sprintf_s(alarmMsgAck.szMsgID, sizeof(alarmMsgAck.szMsgID), "%x", MSG_FROM_DEVICE_ALARM | 0x1000);
	alarmMsgAck.nSquenceNum = pAlarmMsg->nSequenceNum;
	//发送IPC消息给PHONE
	ipc_send(IPC_ID_PHONE, MSG_D2P_ACK, 0, 0, &alarmMsgAck, sizeof(alarmMsgAck));


	DCLIENT_ALARM_MSG alarmMsg;
	memset(&alarmMsg, 0, sizeof(DCLIENT_ALARM_MSG));	
	alarmMsg.nSequenceNum = pAlarmMsg->nSequenceNum;
	alarmMsg.id = pAlarmMsg->id;
	rl_strcpy_s(alarmMsg.type, sizeof(alarmMsg.type), pAlarmMsg->szType);
	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ALARM_ACK, 0, 0, &alarmMsg, sizeof(alarmMsg));
}

int CAlarmControl::AddAlarmMsgToList(DCLIENT_ALARM_MSG *pAlarmMsg)
{
	if(pAlarmMsg == NULL)
	{
		return -1;
	}
	DCLIENT_ALARM_MSG_NODE *pAlarmMsgNode;
	pAlarmMsgNode = new DCLIENT_ALARM_MSG_NODE;
	if(!pAlarmMsgNode)
		return -1;
	
	rl_memset(pAlarmMsgNode, 0, sizeof(DCLIENT_ALARM_MSG_NODE));
	rl_memcpy(&pAlarmMsgNode->dclientAlarmMsg, pAlarmMsg, sizeof(DCLIENT_ALARM_MSG));
	rl_gettimeofday(&pAlarmMsgNode->time_val);
	Lock();
	list_add_tail(&pAlarmMsgNode->node, &m_listAlarmMsg);
	Unlock();
	return 0;
}

#if RL_SUPPORT_ARMING_P2P
int CAlarmControl::AddRequestArmingMsgToList(DCLIENT_REQUEST_ARMING *pArmingMsg)
{
	if(pArmingMsg == NULL)
	{
		return -1;
	}
	DCLIENT_REQUEST_ARMING_MSG_NODE *pArmingMsgNode;
	pArmingMsgNode = new DCLIENT_REQUEST_ARMING_MSG_NODE;
	if(!pArmingMsgNode)
		return -1;
		
	rl_memset(pArmingMsgNode, 0, sizeof(DCLIENT_REQUEST_ARMING_MSG_NODE));
	pArmingMsgNode->dclientArmingMsg.nSequenceNum = pArmingMsg->nSequenceNum;	
	pArmingMsgNode->dclientArmingMsg.mode= pArmingMsg->mode;
	rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.action, sizeof(pArmingMsgNode->dclientArmingMsg.action),pArmingMsg->action);
	rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.from, sizeof(pArmingMsgNode->dclientArmingMsg.from), pArmingMsg->from);	
	rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.to, sizeof(pArmingMsgNode->dclientArmingMsg.to), pArmingMsg->to);
	if(rl_str_isempty(pArmingMsgNode->dclientArmingMsg.from_ip))
	{
		char szIP[IP_SIZE]={0};	
		GetSettingHandleInstance()->GetLanIPAddr(szIP, sizeof(szIP));
		rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.from_ip, sizeof(pArmingMsgNode->dclientArmingMsg.from_ip), szIP);
	}
	rl_gettimeofday(&pArmingMsgNode->time_val);
	Lock();
	list_add_tail(&pArmingMsgNode->node, &m_listRequestArmingMsg);
	Unlock();
	return 0;
}

int CAlarmControl::AddReportArmingMsgToList(DCLIENT_REPORT_ARMING *pArmingMsg)
{
	if(pArmingMsg == NULL)
	{
		return -1;
	}
	DCLIENT_REPORT_ARMING_MSG_NODE *pArmingMsgNode;
	pArmingMsgNode = new DCLIENT_REPORT_ARMING_MSG_NODE;
	if(!pArmingMsgNode)
		return -1;
		
	rl_memset(pArmingMsgNode, 0, sizeof(DCLIENT_REPORT_ARMING_MSG_NODE));
	pArmingMsgNode->dclientArmingMsg.nSequenceNum = pArmingMsg->nSequenceNum;	
	pArmingMsgNode->dclientArmingMsg.mode= pArmingMsg->mode;
	rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.from, sizeof(pArmingMsgNode->dclientArmingMsg.from), pArmingMsg->from);	
	rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.to, sizeof(pArmingMsgNode->dclientArmingMsg.to), pArmingMsg->to);
	if(rl_str_isempty(pArmingMsgNode->dclientArmingMsg.from_ip))
	{
		char szIP[IP_SIZE]={0};	
		GetSettingHandleInstance()->GetLanIPAddr(szIP, sizeof(szIP));
		rl_strcpy_s(pArmingMsgNode->dclientArmingMsg.from_ip, sizeof(pArmingMsgNode->dclientArmingMsg.from_ip), szIP);
	}
	rl_gettimeofday(&pArmingMsgNode->time_val);
	Lock();
	list_add_tail(&pArmingMsgNode->node, &m_listReportArmingMsg);
	Unlock();
	return 0;
}

#endif

int CAlarmControl::OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg)
{
	if(pDiscoverAckMsg == NULL)
	{
		return -1;
	}
	Lock();
	list_t *pos = NULL;
	list_for_each(pos, &m_listAlarmMsg)
	{
		DCLIENT_ALARM_MSG_NODE *pTmpAlarmMsgNode = list_entry(pos, DCLIENT_ALARM_MSG_NODE, node);
		INT nType = -1;
		INT nExtension = -1;
		CHAR szDeviceID[DEVICE_ID_SIZE] = "";
		if(pTmpAlarmMsgNode->dclientAlarmMsg.nSequenceNum != pDiscoverAckMsg->nSequenceNum)
		{
			continue;
		}
		if(rl_strchr(pTmpAlarmMsgNode->dclientAlarmMsg.to, '_') == NULL)
		{
			sscanf(pTmpAlarmMsgNode->dclientAlarmMsg.to, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
		}
		else
		{		
			sscanf(pTmpAlarmMsgNode->dclientAlarmMsg.to, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
		}
		if((rl_strcmp(szDeviceID, pDiscoverAckMsg->szDeviceID) == 0) &&
			(nType == -1 || nType == atoi(pDiscoverAckMsg->szType))&&
			(nExtension == -1 || nExtension == atoi(pDiscoverAckMsg->szExtension)) ||
			(nType == SDMC_DEVICE_TYPE_MANAGEMENT && atoi(pDiscoverAckMsg->szType) == SDMC_DEVICE_TYPE_MANAGEMENT && rl_strcmp(szDeviceID, "0.0.0.0.0") == 0))
		{
			//如果to不为NULL，且消息还没发送，则先把消息发送出去，然后再更新to的地址
			if(!rl_str_isempty(pTmpAlarmMsgNode->dclientAlarmMsg.to) && pTmpAlarmMsgNode->bSendFlag == TRUE)
			{		
				DCLIENT_ALARM_MSG dclietAlarmMsg;
				memset(&dclietAlarmMsg, 0, sizeof(DCLIENT_ALARM_MSG));
				rl_memcpy(&dclietAlarmMsg, &pTmpAlarmMsgNode->dclientAlarmMsg, sizeof(DCLIENT_ALARM_MSG));
				SendAlarmMsg(&dclietAlarmMsg, TRANSPORT_TYPE_UDP);
			}
			rl_strcpy_s(pTmpAlarmMsgNode->dclientAlarmMsg.to_name, sizeof(pTmpAlarmMsgNode->dclientAlarmMsg.to_name), pDiscoverAckMsg->szIPAddr);
			pTmpAlarmMsgNode->bSendFlag = TRUE;
		}
	}
#if RL_SUPPORT_ARMING_P2P
	pos = NULL;
	list_for_each(pos, &m_listRequestArmingMsg)
	{
		DCLIENT_REQUEST_ARMING_MSG_NODE *pTmpArmingMsgNode = list_entry(pos, DCLIENT_REQUEST_ARMING_MSG_NODE, node);
		INT nType = -1;
		INT nExtension = -1;
		CHAR szDeviceID[DEVICE_ID_SIZE] = "";
		if(pTmpArmingMsgNode->dclientArmingMsg.nSequenceNum != pDiscoverAckMsg->nSequenceNum)
		{
			continue;
		}
		if(rl_strchr(pTmpArmingMsgNode->dclientArmingMsg.to, '_') == NULL)
		{
			sscanf(pTmpArmingMsgNode->dclientArmingMsg.to, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
		}
		else
		{		
			sscanf(pTmpArmingMsgNode->dclientArmingMsg.to, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
		}
		if((rl_strcmp(szDeviceID, pDiscoverAckMsg->szDeviceID) == 0) &&
			(nType == -1 || nType == atoi(pDiscoverAckMsg->szType))&&
			(nExtension == -1 || nExtension == atoi(pDiscoverAckMsg->szExtension)) ||
			(nType == SDMC_DEVICE_TYPE_MANAGEMENT && atoi(pDiscoverAckMsg->szType) == SDMC_DEVICE_TYPE_MANAGEMENT && rl_strcmp(szDeviceID, "0.0.0.0.0") == 0))
		{
			//如果to不为NULL，且消息还没发送，则先把消息发送出去，然后再更新to的地址
			if(!rl_str_isempty(pTmpArmingMsgNode->dclientArmingMsg.to) && pTmpArmingMsgNode->bSendFlag == TRUE)
			{	
				DCLIENT_REQUEST_ARMING dclietArmingMsg;
				memset(&dclietArmingMsg, 0, sizeof(DCLIENT_REQUEST_ARMING));
				rl_memcpy(&dclietArmingMsg, &pTmpArmingMsgNode->dclientArmingMsg, sizeof(DCLIENT_REQUEST_ARMING));
				SendRequestArming(&dclietArmingMsg, TRANSPORT_TYPE_UDP);
			}
			rl_strcpy_s(pTmpArmingMsgNode->dclientArmingMsg.to_ip, sizeof(pTmpArmingMsgNode->dclientArmingMsg.to_ip), pDiscoverAckMsg->szIPAddr);
			pTmpArmingMsgNode->bSendFlag = TRUE;
		}
	}
	pos = NULL;
	list_for_each(pos, &m_listReportArmingMsg)
	{
		DCLIENT_REPORT_ARMING_MSG_NODE *pTmpArmingMsgNode = list_entry(pos, DCLIENT_REPORT_ARMING_MSG_NODE, node);
		INT nType = -1;
		INT nExtension = -1;
		CHAR szDeviceID[DEVICE_ID_SIZE] = "";
		if(pTmpArmingMsgNode->dclientArmingMsg.nSequenceNum != pDiscoverAckMsg->nSequenceNum)
		{
			continue;
		}
		if(rl_strchr(pTmpArmingMsgNode->dclientArmingMsg.to, '_') == NULL)
		{
			sscanf(pTmpArmingMsgNode->dclientArmingMsg.to, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
		}
		else
		{		
			sscanf(pTmpArmingMsgNode->dclientArmingMsg.to, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
		}
		if((rl_strcmp(szDeviceID, pDiscoverAckMsg->szDeviceID) == 0) &&
			(nType == -1 || nType == atoi(pDiscoverAckMsg->szType))&&
			(nExtension == -1 || nExtension == atoi(pDiscoverAckMsg->szExtension)) ||
			(nType == SDMC_DEVICE_TYPE_MANAGEMENT && atoi(pDiscoverAckMsg->szType) == SDMC_DEVICE_TYPE_MANAGEMENT && rl_strcmp(szDeviceID, "0.0.0.0.0") == 0))
		{
			//如果to不为NULL，且消息还没发送，则先把消息发送出去，然后再更新to的地址
			if(!rl_str_isempty(pTmpArmingMsgNode->dclientArmingMsg.to) && pTmpArmingMsgNode->bSendFlag == TRUE)
			{	
				DCLIENT_REPORT_ARMING dclietArmingMsg;
				memset(&dclietArmingMsg, 0, sizeof(DCLIENT_REPORT_ARMING));
				rl_memcpy(&dclietArmingMsg, &pTmpArmingMsgNode->dclientArmingMsg, sizeof(DCLIENT_REPORT_ARMING));
				ReportArming(&dclietArmingMsg, TRANSPORT_TYPE_UDP);
			}
			rl_strcpy_s(pTmpArmingMsgNode->dclientArmingMsg.to_ip, sizeof(pTmpArmingMsgNode->dclientArmingMsg.to_ip), pDiscoverAckMsg->szIPAddr);
			pTmpArmingMsgNode->bSendFlag = TRUE;
		}
	}
#endif
	Unlock();
	return 0;	
}


//处理定时器
int CAlarmControl::ProcessBaseTimer()
{
	rl_time_val time_val;
	rl_gettimeofday(&time_val);
	static int i=0;
	if(++i % 5 == 0)
	{
		Lock();
		//检查发送列表中的TextMsg是否符合条件发送
		list_t *pos = NULL;
		list_t *tmp = NULL;
		list_for_each_safe(pos, tmp, &m_listAlarmMsg)
		{
			DCLIENT_ALARM_MSG_NODE *pTmpAlarmMsgNode = list_entry(pos, DCLIENT_ALARM_MSG_NODE, node);
			if(pTmpAlarmMsgNode->bSendFlag == TRUE)
			{			
				SendAlarmMsg(&pTmpAlarmMsgNode->dclientAlarmMsg, TRANSPORT_TYPE_UDP);
				pTmpAlarmMsgNode->bSendFlag = FALSE;
			}
			
			int subtime = rl_get_subtimeint(&pTmpAlarmMsgNode->time_val, &time_val);
			if(subtime >= 3 || subtime < 0)//为防止没有发送成功然后整个链表堆积起来，3s之内没有发送出去的就直接删除掉
			{
				list_del(&pTmpAlarmMsgNode->node);
				delete pTmpAlarmMsgNode;
				pTmpAlarmMsgNode = NULL;
			}
		}
#if RL_SUPPORT_ARMING_P2P	
		//发送Request Arming Msg
		pos = NULL;	
		tmp = NULL;
		list_for_each_safe(pos, tmp, &m_listRequestArmingMsg)
		{
			DCLIENT_REQUEST_ARMING_MSG_NODE *pTmpArmingMsgNode = list_entry(pos, DCLIENT_REQUEST_ARMING_MSG_NODE, node);
			if(pTmpArmingMsgNode->bSendFlag == TRUE)
			{			
				SendRequestArming(&pTmpArmingMsgNode->dclientArmingMsg, TRANSPORT_TYPE_UDP);
				pTmpArmingMsgNode->bSendFlag = FALSE;
			}
			
			int subtime = rl_get_subtimeint(&pTmpArmingMsgNode->time_val, &time_val);
			if(subtime >= 3 || subtime < 0)//为防止没有发送成功然后整个链表堆积起来，3s之内没有发送出去的就直接删除掉
			{
				list_del(&pTmpArmingMsgNode->node);
				delete pTmpArmingMsgNode;
				pTmpArmingMsgNode = NULL;
			}
		}

		//发送Report Arming Msg
		pos = NULL;
		tmp = NULL;
		list_for_each_safe(pos, tmp, &m_listReportArmingMsg)
		{
			DCLIENT_REPORT_ARMING_MSG_NODE *pTmpArmingMsgNode = list_entry(pos, DCLIENT_REPORT_ARMING_MSG_NODE, node);
			if(pTmpArmingMsgNode->bSendFlag == TRUE)
			{			
				ReportArming(&pTmpArmingMsgNode->dclientArmingMsg, TRANSPORT_TYPE_UDP);
				pTmpArmingMsgNode->bSendFlag = FALSE;
			}
			
			int subtime = rl_get_subtimeint(&pTmpArmingMsgNode->time_val, &time_val);
			if(subtime >= 3 || subtime < 0)//为防止没有发送成功然后整个链表堆积起来，3s之内没有发送出去的就直接删除掉
			{
				list_del(&pTmpArmingMsgNode->node);
				delete pTmpArmingMsgNode;
				pTmpArmingMsgNode = NULL;
			}
		}
#endif
		Unlock();
	}
	if(i == 10000)
	{
		i = 0;
	}
	return 0;
}

int CAlarmControl::OnAlarmMsgAck(SOCKET_MSG_ACK *pAlarmMsgAck)
{
	if(pAlarmMsgAck == NULL)
	{
		return -1;
	}
	DCLIENT_MSG_ACK alarmMsgAck;
	rl_memset(&alarmMsgAck, 0, sizeof(DCLIENT_MSG_ACK));
	rl_strcpy_s(alarmMsgAck.szMsgID, sizeof(alarmMsgAck.szMsgID), pAlarmMsgAck->szMsgID);
	rl_strcpy_s(alarmMsgAck.szMsgCRC, sizeof(alarmMsgAck.szMsgCRC), pAlarmMsgAck->szMsgCRC);
	rl_strcpy_s(alarmMsgAck.szResult, sizeof(alarmMsgAck.szResult), pAlarmMsgAck->szResult);
	rl_strcpy_s(alarmMsgAck.szInfo, sizeof(alarmMsgAck.szInfo), pAlarmMsgAck->szInfo);	
	alarmMsgAck.nSquenceNum = pAlarmMsgAck->nSequenceNum;
	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ACK, 0, 0, &alarmMsgAck, sizeof(alarmMsgAck));
}


//上锁消息缓冲区
void CAlarmControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁消息缓冲区
void CAlarmControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}

