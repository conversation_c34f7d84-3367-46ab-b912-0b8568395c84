#ifndef __ALARM_CONTROL_H__
#define __ALARM_CONTROL_H__
#include <time.h>

typedef struct DCLIENT_ALARM_MSG_NODE_T
{
	DCLIENT_ALARM_MSG dclientAlarmMsg;
	BOOL bSendFlag;
	rl_time_val time_val;
	list_t node;
}DCLIENT_ALARM_MSG_NODE;

#if RL_SUPPORT_ARMING_P2P
typedef struct DCLIENT_REQUEST_ARMING_MSG_NODE_T
{
	DCLIENT_REQUEST_ARMING dclientArmingMsg;
	BOOL bSendFlag;
	rl_time_val time_val;
	list_t node;
}DCLIENT_REQUEST_ARMING_MSG_NODE;

typedef struct DCLIENT_REPORT_ARMING_MSG_NODE_T
{
	DCLIENT_REPORT_ARMING dclientArmingMsg;
	BOOL bSendFlag;
	rl_time_val time_val;
	list_t node;
}DCLIENT_REPORT_ARMING_MSG_NODE;
#endif

class CAlarmControl
{
public:
	CAlarmControl();
	~CAlarmControl();
	
	//发送ALARM消息
	int SendAlarmMsg(DCLIENT_ALARM_MSG *pszAlarmMsg, int nSendType = TRANSPORT_TYPE_TCP);

	int SendAlarmDealMsg(DCLIENT_ALARM_DEAL_INFO *pAlarmDeal);
#if RL_SUPPORT_ARMING_P2P
	int SendRequestArming(DCLIENT_REQUEST_ARMING *pRequestArming, int nSendType);
	int AddRequestArmingMsgToList(DCLIENT_REQUEST_ARMING *pArmingMsg);
	int AddReportArmingMsgToList(DCLIENT_REPORT_ARMING *pArmingMsg);
#endif
	//收到ALARM消息
	int OnRecvAlarmSend(SOCKET_MSG_ALARM_SEND *pAlarmSend);

	int OnRecvAlarmDealNotify(SOCKET_MSG_ALARM_DEAL *pAlarmDeal);

	int OnRecvAlarmAck(SOCKET_MSG_ALARM *pAlarmMsg);

	int AddAlarmMsgToList(DCLIENT_ALARM_MSG *pAlarmMsg);
	int OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg);
	int ProcessBaseTimer();
	int OnAlarmMsgAck(SOCKET_MSG_ACK *pAlarmMsgAck);
#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
	int ReportArming(DCLIENT_REPORT_ARMING *pReportArming, int nSendType = TRANSPORT_TYPE_TCP);
#endif
	static CAlarmControl *GetInstance();
protected:
	list_t m_listAlarmMsg;
#if RL_SUPPORT_ARMING_P2P
	list_t m_listRequestArmingMsg;
	list_t m_listReportArmingMsg;
#endif
	void Lock();
	void Unlock();

	void *m_lock;
private:
	static CAlarmControl *instance;

};

CAlarmControl *GetAlarmControlInstance();

#endif
