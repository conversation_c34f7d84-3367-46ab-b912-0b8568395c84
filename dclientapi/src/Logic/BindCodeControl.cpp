#include "MsgControl.h"
#include "ConnectControl.h"
#include "BindCodeControl.h"
#include "dclient_ipc.h"

CBindCodeControl *GetBindCodeControlInstance()
{
	return CBindCodeControl::GetInstance();
}

CBindCodeControl::CBindCodeControl()
{

}

CBindCodeControl::~CBindCodeControl()
{

}

CBindCodeControl *CBindCodeControl::instance = NULL;

CBindCodeControl *CBindCodeControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CBindCodeControl();
	}

	return instance;
}

//发送获取BindCode请求
int CBindCodeControl::SendGetBindCodeRequest(UINT nSerialNum)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if(GetMsgControlInstance()->BuildGetBindCodeRequestMsg(&socketMsg, nSerialNum) < 0)
	{
		rl_log_err("%s: BuildGetBindCodeRequestMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug("SendGetBindCodeRequest success.");
	return 0;
}

//发送UnBindCode请求
int CBindCodeControl::SendUnBindCodeRequest(UINT nSerialNum, CHAR *pszBindCode)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if(GetMsgControlInstance()->BuildUnBindCodeRequestMsg(&socketMsg, nSerialNum, pszBindCode) < 0)
	{
		rl_log_err("%s: BuildUnBindCodeRequestMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug("SendUnBindCodeRequest success.");
	return 0;
}

//发送GetBindCodeList请求
int CBindCodeControl::SendGetBindCodeListRequest(UINT nSerialNum)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if(GetMsgControlInstance()->BuildGetBindCodeListRequestMsg(&socketMsg, nSerialNum) < 0)
	{
		rl_log_err("%s: BuildUnBindCodeRequestMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug("SendUnBindCodeRequest success.");
	return 0;
}

int CBindCodeControl::OnGetBindCode(SOCKET_MSG_BIND_CODE_CREATE *pSocketMsgBindCodeCreate)
{
	if(pSocketMsgBindCodeCreate == NULL)
	{
		return -1;
	}
	DLCIENT_BIND_CODE_CREATE bindCodeCreate;	
	memset(&bindCodeCreate, 0, sizeof(bindCodeCreate));
	bindCodeCreate.nResult = pSocketMsgBindCodeCreate->nResult;
	bindCodeCreate.nSequenceNum = pSocketMsgBindCodeCreate->nSequenceNum;
	rl_strcpy_s(bindCodeCreate.szBindCode, sizeof(bindCodeCreate.szBindCode), pSocketMsgBindCodeCreate->szBindCode);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_CREATE_BIND_CODE, 0, 0, &bindCodeCreate, sizeof(bindCodeCreate));
}

int CBindCodeControl::OnDeleteBindCode(SOCKET_MSG_BIND_CODE_CREATE *pSocketMsgBindCodeDelete)
{
	if(pSocketMsgBindCodeDelete == NULL)
	{
		return -1;
	}

	DLCIENT_BIND_CODE_CREATE bindCodeDelete;	
	memset(&bindCodeDelete, 0, sizeof(bindCodeDelete));
	bindCodeDelete.nResult = pSocketMsgBindCodeDelete->nResult;
	bindCodeDelete.nSequenceNum = pSocketMsgBindCodeDelete->nSequenceNum;
	rl_strcpy_s(bindCodeDelete.szBindCode, sizeof(bindCodeDelete.szBindCode), pSocketMsgBindCodeDelete->szBindCode);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DELETE_BIND_CODE, 0, 0, &bindCodeDelete, sizeof(bindCodeDelete));
}

int CBindCodeControl::OnGetBindCodeList(SOCKET_MSG_BIND_CODE_LIST *pSocketMsgBindCodeList)
{
	if(pSocketMsgBindCodeList == NULL)
	{
		return -1;
	}

	DLCIENT_BIND_CODE_LIST bindCodeList;	
	memset(&bindCodeList, 0, sizeof(bindCodeList));
	bindCodeList.nResult = pSocketMsgBindCodeList->nResult;	
	bindCodeList.nBindCodeCount= pSocketMsgBindCodeList->nBindCodeCount;
	bindCodeList.nSequenceNum= pSocketMsgBindCodeList->nSequenceNum;	
	rl_memcpy(bindCodeList.bindCodeInfo, pSocketMsgBindCodeList->bindCodeInfo, sizeof(bindCodeList.bindCodeInfo));
	return ipc_send(IPC_ID_PHONE, MSG_D2P_BIND_CODE_LIST, 0, 0, &bindCodeList, sizeof(bindCodeList));
}


