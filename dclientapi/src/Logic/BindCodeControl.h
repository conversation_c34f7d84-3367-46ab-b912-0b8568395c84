#ifndef __BINDCODE_CONTROL_H__
#define __BINDCODE_CONTROL_H__

class CBindCodeControl
{
public:
	CBindCodeControl();
	~CBindCodeControl();
	
	//发送GetBindCode请求
	int SendGetBindCodeRequest(UINT nSerialNum);
	int SendUnBindCodeRequest(UINT nSerialNum, CHAR *pszBindCode);
	int SendGetBindCodeListRequest(UINT nSerialNum);
	int OnGetBindCode(SOCKET_MSG_BIND_CODE_CREATE *pSocketMsgBindCodeCreate);
	int OnDeleteBindCode(SOCKET_MSG_BIND_CODE_CREATE *pSocketMsgBindCodeDelete);
	int OnGetBindCodeList(SOCKET_MSG_BIND_CODE_LIST *pSocketMsgBindCodeList);
	static CBindCodeControl *GetInstance();
private:
	static CBindCodeControl *instance;

};

CBindCodeControl *GetBindCodeControlInstance();

#endif