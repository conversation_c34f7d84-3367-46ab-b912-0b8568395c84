#include <sys/socket.h>
#include <netinet/in.h>  
#include <arpa/inet.h>  
#include <rl_log.h>
#include <stdlib.h>
#include <stdio.h>
#include <netdb.h>  
#include <errno.h>  
#include <sys/types.h>  
#include <asm/ioctls.h> 
#include <sys/ioctl.h>
#include <signal.h>
#include <rllib/rllib.h>
#include <rllog/rl_log.h>
#include "Control.h"
#include "ConnectControl.h"
#include "MsgControl.h"
#include "SettingHandle.h"
#include "Lock.h"
#include "DclientDefine.h"
#include "Utility.h"
#include "DiscoverControl.h"
#include "rl_revision.h"
#include "DownloadControl.h"
#include "D2ChannelControl.h"
#include "MsgHandle.h"
#include "AES256.h"
#include "Base64.h"
#include <vector>
#include "Def.h"
#include "DClient.h"
#if RL_GLOBAL_USE_RL_GETADDRINFO
#include "rl_getaddrinfo.h"
#endif

#define CONNECT_RECV_BUF_SIZE 1024


#define DCLIENT_RETRY_GATEWAY_COUNT			720 //5S * 720

typedef struct DNS_PARSE_PARAS_T
{
	struct addrinfo *res;
	struct addrinfo hints;
	char szAddrUrl[URL_BUFFER_SIZE];
	char szIp[IP_SIZE];
}DNS_PARSE_PARAS;

enum 
{	
	TCP_CONNECT_MODE_NONE,
	TCP_CONNECT_MODE_CONNECTED,
	TCP_CONNECT_MODE_DISCONNECT,
};

static int g_tid;
static int TransStr2SocketAddrIn(char *pAddr, int nAddrSize, int nPort, void *pSocketAddr)
{
	if (NULL == pSocketAddr || 0 > nPort)
	{
		return -1;
	}

	struct sockaddr_in *pDest = (struct sockaddr_in *)pSocketAddr;
	pDest->sin_family = AF_INET;
	pDest->sin_port = DCLIENT_HTONS(nPort);

	if (NULL != pAddr && 0 < nAddrSize)
	{
		pDest->sin_addr.s_addr = inet_addr(pAddr);
	}
	else
	{
		pDest->sin_addr.s_addr = DCLIENT_HTONL(INADDR_ANY);
	}

	return 0;
}


static int SetPortReused(int nSocketFd, int nReUsed)
{
	if (0 > nSocketFd)
	{
		rl_log_err("[%s-%d]: Bad parameters", 
			__FUNCTION__, __LINE__);
		return -1;
	}

	int len = sizeof(nReUsed);
	int ret = setsockopt(nSocketFd, SOL_SOCKET, SO_REUSEADDR, &nReUsed, len);
	if (0 > ret) 
    { 
       rl_log_err("[%s-%d]: Set reused failed", __FUNCTION__, __LINE__);
		return -1;
    } 

	return 0;
}

static int SetLoop(int nSocketFd, int nLoop)
{
	if (0 > nSocketFd || 0 > nLoop || 1 < nLoop)
	{
		rl_log_err("[%s-%d]: Bad parameters", __FUNCTION__, __LINE__);
		return -1;
	}

	unsigned char cLoop = (unsigned char )nLoop;
	int ret = setsockopt(nSocketFd, IPPROTO_IP, IP_MULTICAST_LOOP, 
		&cLoop, sizeof(cLoop));
	if (0 > ret)
	{
		rl_log_err("[%s-%d]: setsockopt failed, ret %d", 
			__FUNCTION__, __LINE__, ret);
		return -1;
	}

	return 0;
}

static int CloseListen(int nSocketFd)
{
	if (0 > nSocketFd)
	{
		rl_log_err("[%s-%d]: Bad parameters", __FUNCTION__, __LINE__);
		return -1;
	}

	int nRet = close(nSocketFd);
	if (0 > nRet)
	{
		rl_log_err("[%s-%d]: Close socket failed", __FUNCTION__, __LINE__);
		return -1;
	}

	return 0;
}

static int ConnectTcp(CHAR *pszServer, UINT nPort)
{
	int nSocketFd = socket(AF_INET, SOCK_STREAM, 0);
	if(nSocketFd <= 0)
	{
		return -1;
	}

	struct sockaddr_in serverAddr; 
	serverAddr.sin_family = AF_INET;
	serverAddr.sin_addr.s_addr = inet_addr(pszServer);
	serverAddr.sin_port = DCLIENT_HTONS(nPort);

	//unsigned long ul = 1;
	//ioctl(nSocketFd, FIONBIO, &ul); //设置为非阻塞模式 
	
	int nRet = connect(nSocketFd, (struct sockaddr *)&serverAddr, sizeof(serverAddr));
	if(nRet < 0)
	{
		rl_log_err("connect failed. error=%d", errno);
		close(nSocketFd);
		return -1;
	}

	return nSocketFd;
}

//从缓冲区中循环查找符合MAGIC的消息，并返回最后需要留存的数据的位置
static int ParseSocketMsg(UCHAR *pRecvBuf, UINT nRecvSize, UINT *pLeftOffset, SOCKET_MSG *pRecvMsg)
{
	if((pRecvBuf == NULL) || (pLeftOffset == NULL) || (pRecvMsg == NULL))
	{
		return -1;
	}
	
	UINT nLeftOffset = 0;
	UCHAR *pMsgHeader = NULL;

	//循环遍历RecvBuf内容
	while(nLeftOffset < nRecvSize)
	{
		//遍历消息直到找到MAGIC匹配的数据
		for(UINT i=nLeftOffset; i<nRecvSize - 1; i++)
		{
			if((pRecvBuf[i] == SOCKET_MSG_MAGIC_MSB) && (pRecvBuf[i + 1] == SOCKET_MSG_MAGIC_LSB))
			{
				//rl_log_debug("&pRecvBuf[%d]=%X, %p", i, pRecvBuf[i], &pRecvBuf[i]);
				pMsgHeader = &pRecvBuf[i];
				nLeftOffset = i;
				break;
			}
		}

		//没有MAGIC头匹配的数据
		if(pMsgHeader == NULL)
		{
			if(pRecvBuf[nRecvSize - 1] == SOCKET_MSG_MAGIC_MSB)
			{
				*pLeftOffset = nRecvSize - 1;
			}
			else
			{
				*pLeftOffset = -1;
			}
			return -1;
		}

		UINT nLeftSize = nRecvSize - nLeftOffset;
		*pLeftOffset = nLeftOffset;

		if(nLeftSize < SOCKET_MSG_NORMAL_HEADER_SIZE)
		{
			return -1;
		}
		
		SOCKET_MSG_NORMAL *pNormalMsg = (SOCKET_MSG_NORMAL *)pMsgHeader;
		UINT nHeadSize = DCLIENT_NTOHS(pNormalMsg->nHeadSize);
		UINT nDataSize = DCLIENT_NTOHS(pNormalMsg->nDataSize);
		int nMsgSize = nHeadSize + nDataSize;
		
		if(nMsgSize > MAX_SOCKET_FRAME_SIZE)
		{
			//如果头+数据的大小太大，说明数据无效
			nLeftOffset += 2;
			continue;
		}
		else if(nMsgSize > nLeftSize)
		{
			//数据不够一帧
			return -1;
		}
		else
		{
			//数据足够1帧
			memcpy(pRecvMsg->byData, pMsgHeader, nMsgSize);
			pRecvMsg->nSize = nMsgSize;
			nLeftSize -= nMsgSize;
			*pLeftOffset = nRecvSize - nLeftSize;

			break;
		}
	}

	return 0;
}

void *DnsThread(void *arg)
{
	/* 设置自己为可分离线程 */
    pthread_detach(pthread_self());
	DNS_PARSE_PARAS *pDnsParseParas = (DNS_PARSE_PARAS *)arg;
	if(pDnsParseParas == NULL)
	{
		return NULL;
	}
	
	//重新备份一份数据，防止主线程退出，数据出现 异常
	DNS_PARSE_PARAS dnsParseParas;
	memset(&dnsParseParas, 0, sizeof(DNS_PARSE_PARAS));
	rl_memcpy(&dnsParseParas, pDnsParseParas, sizeof(DNS_PARSE_PARAS));
	rl_time_val time_begin;
	rl_gettimeofday(&time_begin);
	
#if RL_GLOBAL_USE_RL_GETADDRINFO
	int result = rl_getaddrinfo2(dnsParseParas.szAddrUrl, NULL, &dnsParseParas.hints, &dnsParseParas.res);
#else
	int result = getaddrinfo(dnsParseParas.szAddrUrl, NULL, &dnsParseParas.hints, &dnsParseParas.res);	
#endif
	if (0 == result)
	{
		struct sockaddr_in *addrin = (struct sockaddr_in *)dnsParseParas.res->ai_addr;
		char *ip = inet_ntoa(addrin->sin_addr);
		rl_strcpy_s(dnsParseParas.szIp, sizeof(dnsParseParas.szIp), ip);
		rl_time_val time_end;
		rl_gettimeofday(&time_end);
		if(g_tid == (int)pthread_self() && pDnsParseParas != NULL)/*没有超时*/
		{		
			rl_memcpy(pDnsParseParas, &dnsParseParas, sizeof(DNS_PARSE_PARAS));
		}
		freeaddrinfo(dnsParseParas.res);
	}
	pthread_detach(pthread_self());
	return NULL;
}

static void *GetServerThread(void *arg)
{
	CConnectControl *pConnectControl = (CConnectControl *)arg;
	if(pConnectControl == NULL)
	{
		pthread_detach(pthread_self());
		return NULL;
	}
	while(pConnectControl->IsGetServerThreadRun())
	{	
		if(pConnectControl->m_bThreadGetGateWay)
		{
			pConnectControl->GetGateServer();
			if(pConnectControl->m_nGateWayState == GATE_WAY_STATE_SUCCEED || pConnectControl->m_nGateWayState == GATE_WAY_STATE_FAILED_OLD)
			{
				pConnectControl->m_bThreadGetAccess = TRUE;
			}
			pConnectControl->m_bThreadGetGateWay = FALSE;
		}
		if(pConnectControl->m_bThreadGetAccess || pConnectControl->m_bHasForceGateWay)
		{
			pConnectControl->GetAccessServer();
			pConnectControl->m_bThreadGetAccess = FALSE;
		}
		sleep(1);
	}
	return NULL;
}

void *TcpThread(void *arg)
{
	CHAR szLastTcpServerAddr[VALUE_SIZE] = {0};
	CHAR szCurTcpServerAddr[VALUE_SIZE] = {0};
	UINT nLastTcpServerPort = 0;
	UINT nCurTcpServerPort = 0;
	int nTcpSocketFd = -1;

	CConnectControl *pConnectControl = (CConnectControl *)arg;
	if(pConnectControl == NULL)
	{
		pthread_detach(pthread_self());
		return NULL;
	}

	fd_set rs;
	fd_set es;
	struct timeval tv;
	rl_time_val timeLast;
	rl_gettimeofday(&timeLast);
	srand((unsigned int)time(0));
	
	UCHAR byLastRecvBuf[MAX_SOCKET_FRAME_SIZE] = {0};
	UINT nLastRecvSize = 0;

	BOOL bDnsParseFlag = FALSE;
	while(pConnectControl->IsTcpThreadRun())
	{
		if(!pConnectControl->TcpAntiShockConnect())
		{
			sleep(1);
			continue;
		}
		CHAR szServerIPAddr[VALUE_SIZE] = {0};
		std::vector<std::string> v_szServerIPAddr;

		//TODO: 判断连接失败连续超过3次且半小时内未重新获取服务器地址，则重新找GATEWAY获取服务器地址
		
		pConnectControl->GetTcpServerAddr(szCurTcpServerAddr, sizeof(szCurTcpServerAddr));
		nCurTcpServerPort = pConnectControl->GetTcpServerPort();
		nTcpSocketFd = pConnectControl->GetTcpSocketFd();
		if(pConnectControl->m_bHasForceAccess)
		{
			pConnectControl->m_bHasForceAccess = FALSE;
			rl_memset(szCurTcpServerAddr, 0, sizeof(szCurTcpServerAddr));
			sscanf(pConnectControl->m_szHasForceAccess, "%[^:]:%d", szCurTcpServerAddr, &nCurTcpServerPort);
			pConnectControl->SetTcpServerAddr(szCurTcpServerAddr);
			pConnectControl->SetTcpServerPort(nCurTcpServerPort);
		}
		BOOL bServerChanged = FALSE;	
		BOOL bServerUrlChanged = (rl_strcmp(szCurTcpServerAddr, szLastTcpServerAddr) != 0);
		
		if( rl_str_isempty(szCurTcpServerAddr) || nCurTcpServerPort <=0 )
		{
			sleep(1);
			pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_NONE);
			continue;
		}
		//判断SERVER和PORT是否有改变
		if((rl_strcmp(szCurTcpServerAddr, szLastTcpServerAddr) != 0) || (nCurTcpServerPort != nLastTcpServerPort))
		{
			bServerChanged = TRUE;
			rl_memset(szLastTcpServerAddr, 0, sizeof(szLastTcpServerAddr));
			rl_strcpy_s(szLastTcpServerAddr, sizeof(szLastTcpServerAddr), szCurTcpServerAddr);
			nLastTcpServerPort = nCurTcpServerPort;			
		}
		//判断是否是域名？
		if(!rl_str_isempty(szCurTcpServerAddr) && !IsValidIPAddr(szCurTcpServerAddr))
		{
			//如果是域名则判断是否有变化
			struct addrinfo *res0 = NULL;
			struct addrinfo *cur = NULL;
			int result = -1;
			struct addrinfo hints;
			memset(&hints, 0, sizeof(struct addrinfo));
		    hints.ai_family = AF_INET; /* Allow IPv4 */
		    hints.ai_flags = AI_PASSIVE; /* For wildcard IP address */
		    hints.ai_protocol = 0; /* Any protocol */
		    hints.ai_socktype = SOCK_STREAM;
		#if RL_GLOBAL_USE_RL_GETADDRINFO
			result = rl_getaddrinfo2(szCurTcpServerAddr, NULL, &hints, &res0);
		#else
			result = getaddrinfo(szCurTcpServerAddr, NULL, &hints, &res0);
		#endif

			if (0 == result)
			{
				for(cur=res0; cur!=NULL; cur=cur->ai_next)
				{
					struct sockaddr_in *addrin = (struct sockaddr_in *)cur->ai_addr;
					char *ip = inet_ntoa(addrin->sin_addr);
					v_szServerIPAddr.push_back(ip);
					
				}
				rl_strcpy_s(szServerIPAddr, sizeof(szServerIPAddr), v_szServerIPAddr[0].data());
				rl_gettimeofday(&timeLast);
				freeaddrinfo(res0);
			}		
			#if 0
			if(bServerUrlChanged)
			{
				bDnsParseFlag = TRUE;
			}			
			else
			{
				//如果无变化则判断是否超时1小时
				rl_time_val timeCur;
				rl_gettimeofday(&timeCur);
				int nSeconds = rl_get_subtimeint(&timeLast, &timeCur);
				if((nSeconds >= DNS_PARSE_PERIOD_TIMEOUT_DEFAULT) || (nSeconds < 0))
				{
					bDnsParseFlag = TRUE;
				}
			}
			#endif
		}
		else
		{
			rl_strcpy_s(szServerIPAddr, sizeof(szServerIPAddr), szCurTcpServerAddr);
			v_szServerIPAddr.push_back(szServerIPAddr);
		}

		bool bReConnnect = pConnectControl->GetReConnectFlag();
		//rl_log_debug("bServerChanged=%d, nTcpSocketFd=%d, bReConnnect=%d", bServerChanged, nTcpSocketFd, bReConnnect);
		if(bServerChanged || (nTcpSocketFd <= 0) || bReConnnect)
		{
			if(nTcpSocketFd > 0)
			{
				close(nTcpSocketFd);
				nTcpSocketFd = -1;
				pConnectControl->NotifyNetConnectChange(FALSE);
			}
			
			if(bReConnnect)
			{
				pConnectControl->SetReconnectFlag(FALSE);
			}

			//判断地址和端口是否有效
			if((!IsValidIPAddr(szServerIPAddr)) || (!IsValidPort(nCurTcpServerPort)))
			{
				sleep(1);
				pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_NONE);
				continue;
			}
			do
			{
				//建立连接
				nTcpSocketFd = ConnectTcp(szServerIPAddr, nCurTcpServerPort);
				pConnectControl->SetTcpSocketFd(nTcpSocketFd);
				if(nTcpSocketFd <= 0)
				{	
					rl_log_err("connect %s:%d failed.", szServerIPAddr, nCurTcpServerPort);
					pConnectControl->m_bConnectSucceed= FALSE;
					rl_strcpy_s(szServerIPAddr, sizeof(szServerIPAddr), v_szServerIPAddr.back().data());
					v_szServerIPAddr.pop_back();
					continue;
				}
				else
				{	
					pConnectControl->SetHeartBeatFlag(TRUE);
					pConnectControl->NotifyNetConnectChange(TRUE);
					if(pConnectControl->m_nConnectMode != DOORSETTING_CONNECT_SERVER_MODE_CLOUD)
					{
						pConnectControl->SetHeartBeatNewModeFlag(FALSE);
					}
					pConnectControl->m_bConnectSucceed= TRUE;
					pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_CONNECT);
					rl_log_err("connect %s:%d successful. server mode is %s.", szServerIPAddr, nCurTcpServerPort, GetStrConnectModeByMode(pConnectControl->m_nConnectMode).c_str());
					break;
				}
			}while (v_szServerIPAddr.size() > 0);
			if(!pConnectControl->m_bConnectSucceed)
			{
				if(pConnectControl->m_nTcpConnectStatus != TCP_CONNECT_STATUS_DISCONNECT)
				{
					pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_FAILED);
					int nRandNum = 1+ rand()%9;
					sleep(nRandNum);
				}
				continue;
			}
		}

		memset(&tv, 0, sizeof(tv));
		tv.tv_sec = 2;
		tv.tv_usec = 0;
		FD_ZERO(&rs);
		FD_ZERO(&es);
		FD_SET(nTcpSocketFd, &rs);
		FD_SET(nTcpSocketFd, &es);
		int nRet = select(nTcpSocketFd + 1, &rs, NULL, &es, &tv);
		if (nRet == 0) 
		{
			//rl_log_debug("Recv timeout.");
			continue;
		}
		else if(nRet < 0)
		{
			//rl_log_debug("Recv error.");
			continue;
		}
		else if (FD_ISSET(nTcpSocketFd, &rs)) 
		{
			struct sockaddr_in remoteAddr;
			rl_memset(&remoteAddr, 0, sizeof(remoteAddr));
			socklen_t nRemoteAddrLen = sizeof(remoteAddr);
			//先开辟个临时缓冲区将全部数据收回来
			UCHAR byCurRecvBuf[MAX_SOCKET_FRAME_SIZE];
			memset(byCurRecvBuf, 0, sizeof(byCurRecvBuf));
			int nCurRecvSize = recv(nTcpSocketFd, byCurRecvBuf, sizeof(byCurRecvBuf), 0);
			if(nCurRecvSize > 0)
			{
				UCHAR byRecvBuf[MAX_SOCKET_FRAME_SIZE * 4];
				memset(byRecvBuf, 0, sizeof(byRecvBuf));

				//与上一帧数据组合起来
				memcpy(byRecvBuf, byLastRecvBuf, nLastRecvSize);
				memcpy(byRecvBuf + nLastRecvSize, byCurRecvBuf, nCurRecvSize);
				UINT nRecvSize = nLastRecvSize + nCurRecvSize;
				
				//判断是否足够一帧数据
				SOCKET_MSG recvMsg;
				
				UINT nLeftOffset = 0;
				UINT nLeftSize = nRecvSize;
				UCHAR *pRecvBuf = byRecvBuf;

				//rl_log_debug("LINE %d, pRecvBuf=%p, nCurRecvSize=%d", __LINE__, pRecvBuf, nCurRecvSize);
				while(nLeftSize > 0)
				{
					memset(&recvMsg, 0, sizeof(recvMsg));
					//循环解析消息
					if(ParseSocketMsg(pRecvBuf, nLeftSize, &nLeftOffset, &recvMsg) == 0)
					{
						if(!getpeername(nTcpSocketFd, (struct sockaddr *)&remoteAddr, &nRemoteAddrLen))
						{
							rl_strcpy_s(recvMsg.szRemoteAddr, sizeof(recvMsg.szRemoteAddr), inet_ntoa(remoteAddr.sin_addr));
							recvMsg.nPort = ntohs(remoteAddr.sin_port);
						}
						pConnectControl->RecvTcpMsg(&recvMsg);
						pRecvBuf = pRecvBuf + nLeftOffset;
						nLeftSize -= nLeftOffset;
						nLeftOffset = 0;
					}
					else 
					{
						break;
					}
				}
				if((int)nLeftOffset < (int)nRecvSize)
				{
					if(nLeftOffset == -1)
					{
						nLeftSize = 0;
						nLeftOffset = 0;
					}
					nLastRecvSize = nLeftSize - nLeftOffset;
					memcpy(byLastRecvBuf, pRecvBuf + nLeftOffset, nLastRecvSize);
				}
			}
			else
			{
				if (nTcpSocketFd > 0)
				{
					close(nTcpSocketFd);
					nTcpSocketFd = -1;
				}
				pConnectControl->SetTcpSocketFd(nTcpSocketFd);
				pConnectControl->NotifyNetConnectChange(FALSE);	
				pConnectControl->m_bConnectSucceed= FALSE;
				pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_DISCONNECT);
				rl_log_err("disconnect %s:%d ", szServerIPAddr, nCurTcpServerPort);
			}
		}
		else if(FD_ISSET(nTcpSocketFd, &es))
		{
			if (nTcpSocketFd > 0)
			{
				close(nTcpSocketFd);
				nTcpSocketFd = -1;
			}
			pConnectControl->SetTcpSocketFd(nTcpSocketFd);
			pConnectControl->NotifyNetConnectChange(FALSE);
			pConnectControl->m_bConnectSucceed= FALSE;		
			pConnectControl->SetTcpConnectStatus(TCP_CONNECT_STATUS_DISCONNECT);
			rl_log_err("Disconnect %s:%d ", szServerIPAddr, nCurTcpServerPort);
		}
	}

	rl_log_debug("tcpthread exit.");
	return NULL;
}

void *RecvThread(void *arg)
{
	CConnectControl *pConnectControl = (CConnectControl *)arg;
	if(pConnectControl == NULL)
	{
		pthread_detach(pthread_self());
		return NULL;
	}

	fd_set rs;
	struct timeval tv;
	while(pConnectControl->IsRecvThreadRun())
	{
		memset(&tv, 0, sizeof(tv));
		tv.tv_sec = 2;
		tv.tv_usec = 0;
		FD_ZERO(&rs);
		int nMulticastSocketFd = pConnectControl->GetMulticastSocketFd();
		int nMaxFd = nMulticastSocketFd;
		if(nMulticastSocketFd > 0)
		{
			FD_SET(nMulticastSocketFd, &rs);
		}
		int nRet = select(nMaxFd + 1, &rs, NULL, NULL, &tv);

		if (nRet == 0) 
		{
			//rl_log_debug("Recv timeout.");
			continue;
		}
		else if(nRet < 0)
		{
			rl_log_debug("Recv error.");
			continue;
		}
		else if (FD_ISSET(nMulticastSocketFd, &rs)) 
		{
			int len = sizeof(struct sockaddr_in);
			struct sockaddr_in remoteAddr;
			rl_memset(&remoteAddr, 0, sizeof(remoteAddr));
			
			SOCKET_MSG *pRecvMsg = new SOCKET_MSG;
			memset(pRecvMsg, 0, sizeof(SOCKET_MSG));

			pRecvMsg->nSize = recvfrom(nMulticastSocketFd, pRecvMsg->byData, sizeof(pRecvMsg->byData), 0, (struct sockaddr *)&remoteAddr, (socklen_t*)&len);

			rl_strcpy_s(pRecvMsg->szRemoteAddr, sizeof(pRecvMsg->szRemoteAddr), inet_ntoa(remoteAddr.sin_addr));
			pRecvMsg->nPort = DCLIENT_HTONS(remoteAddr.sin_port);

			if(pRecvMsg->byData[0] == SOCKET_MSG_MAGIC_MSB && pRecvMsg->byData[1] == SOCKET_MSG_MAGIC_LSB)
			{
#if !RL_SUPPORT_ETHERNET_TRANSPORT
				//2019.7.2 By Will, ignore the msg here since we already received it from ethernet layer.
				GetConnectControlInstance()->RecvMulticastMsg(pRecvMsg);
#endif
			}

			delete pRecvMsg;

		}
	}

	return NULL;
}

static BOOL CheckCurrentIP()
{
	return RL_TRUE;
}
CConnectControl *GetConnectControlInstance()
{
	return CConnectControl::GetInstance();
}

CConnectControl::CConnectControl()
	: m_bTcpThreadRun(false)
	, m_bRecvThreadRun(false)
	, m_bGetServerThreadRun(false)
	, m_tidTcp(0)
	, m_tidRecv(0)
	, m_tidGetServer(0)
{
	m_nMulticastSocketFd = -1;
	m_nTcpSocketFd = -1;

	rl_strcpy_s(m_szLocalIPAddr, sizeof(m_szLocalIPAddr), "");
	rl_strcpy_s(m_szMulticastAddr, sizeof(m_szMulticastAddr), "");
	m_nMulticastListenPort = 0;

	rl_strcpy_s(m_szTcpServerAddr, sizeof(m_szTcpServerAddr), "");
	m_nTcpServerPort = 0;
	m_bReConnectFlag = FALSE;
	m_nCloudServerEnable = FALSE;
	m_nCloudServerPort = 0;
	m_nHeartBeatTimeVal = 60;
	m_bHeartBeatFlag = FALSE;
	m_nConnectMode = DOORSETTING_CONNECT_SERVER_MODE_NONE;
	m_bHasForceRPS = FALSE;
	m_bHasForceGateWay = FALSE;
	m_bHasForceAccess = FALSE;
	m_nLastConnectStatus = 0;
	m_bHeartBeatAckFlag = FALSE;
	m_bHeartBeatSendFlag = FALSE;
	m_bHeartBeatNewMode = FALSE;
	m_bConnectSucceed = FALSE;
	m_nGateWayState = GATE_WAY_STATE_NONE;
	m_nAccessServerState = ACCESS_SERVER_STATE_NONE;
	m_bHasLoginGW = FALSE;
	m_nAkcsVersion = 0;
	m_bThreadGetAccess = FALSE;
	m_bThreadGetGateWay = FALSE;
	m_nTcpConnectStatus = TCP_CONNECT_MODE_NONE;
	m_nPBXSrvPort = 0;
	m_nGateTimeInterval = 10;
	m_nConnectOriFrom = TCP_CONNECT_ORI_FROM_NONE;
	m_nLastDiscoverMode = 0;
	memset(m_szLastDeviceID, 0, sizeof(m_szLastDeviceID));
	memset(m_szLastDeviceStatus, 0, sizeof(m_szLastDeviceStatus));
	memset(m_szLastDeviceExtension, 0, sizeof(m_szLastDeviceExtension));
	memset(m_szLastDeviceDownloadServer, 0, sizeof(m_szLastDeviceDownloadServer));
	memset(m_szLastDeviceUploadServer, 0, sizeof(m_szLastDeviceUploadServer));
	memset(m_szLastDeviceType, 0, sizeof(m_szLastDeviceType));
	memset(m_szLastLocation, 0, sizeof(m_szLastLocation));

	memset(m_szLastLanIP, 0, sizeof(m_szLastLanIP));
	memset(m_szLastSubnetMask, 0, sizeof(m_szLastSubnetMask));
	memset(m_szLastGateway, 0, sizeof(m_szLastGateway));
	memset(m_szLastPrimaryDns, 0, sizeof(m_szLastPrimaryDns));
	memset(m_szLastSecondaryDns, 0, sizeof(m_szLastSecondaryDns));
	memset(m_szCloudServerIp, 0, sizeof(m_szCloudServerIp));
	memset(m_szMac, 0, sizeof(m_szMac));
	m_lock = new CLock();

	m_bHasInit = FALSE;
}

CConnectControl::~CConnectControl()
{
	if(m_nMulticastSocketFd > 0)
	{
		CloseListen(m_nMulticastSocketFd);
	}

	if(m_nTcpSocketFd > 0)
	{
		CloseListen(m_nTcpSocketFd);
	}
	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
}

CConnectControl *CConnectControl::instance = NULL;

CConnectControl *CConnectControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CConnectControl();
	}

	return instance;
}

bool CConnectControl::IsTcpThreadRun()
{
	return m_bTcpThreadRun;
}

bool CConnectControl::IsRecvThreadRun()
{
	return m_bRecvThreadRun;
}

bool CConnectControl::IsGetServerThreadRun()
{
	return m_bGetServerThreadRun;
}

//接收组播消息
int CConnectControl::RecvMulticastMsg(SOCKET_MSG *pRecvMsg)
{
	if(pRecvMsg == NULL)
	{
		return -1;
	}

	rl_log_info("Recv Multicast from(%s:%d) %d bytes", pRecvMsg->szRemoteAddr, pRecvMsg->nPort, pRecvMsg->nSize);
	pRecvMsg->nTransport = TRANSPORT_TYPE_UDP;
	GetControlInstance()->AddMsg(DCLIENT_MSG_MULTICAST, 0, 0, pRecvMsg, sizeof(SOCKET_MSG));
	return 0;
}

//接收TCP消息
int CConnectControl::RecvTcpMsg(SOCKET_MSG *pRecvMsg)
{
	if(pRecvMsg == NULL)
	{
		return -1;
	}
	pRecvMsg->nTransport = TRANSPORT_TYPE_TCP;
	rl_log_info("Recv TCP from(%s:%d) %d bytes", pRecvMsg->szRemoteAddr, pRecvMsg->nPort, pRecvMsg->nSize);
	GetControlInstance()->AddMsg(DCLIENT_MSG_TCP, 0, 0, pRecvMsg, sizeof(SOCKET_MSG));
	return 0;
}

int CConnectControl::BuildMulticast()
{
	char *pszAddr = SOCKET_MULTICAST_ADDR;
	int nPort = SOCKET_MULTICAST_PORT;

	int ret = 0;
	int nSocketFd = 0;
	nSocketFd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
	if (0 > nSocketFd)
	{
		rl_log_err("[%s-%d]: Create socket failed", __FUNCTION__, __LINE__);
		return -1;
	}

	struct sockaddr_in tListenAddr;
	rl_memset(&tListenAddr, 0, sizeof(tListenAddr));

	ret = TransStr2SocketAddrIn(NULL, 0, nPort, &tListenAddr);
	if (0 > ret)
	{
		rl_log_err("[%s-%d]: Trans failed", __FUNCTION__, __LINE__);
		CloseListen(nSocketFd);
		return -1;
	}

	ret = SetPortReused(nSocketFd, TRUE);
	if (0 > ret)
	{
		rl_log_err("[%s-%d]: Reused port %d failed", __FUNCTION__, __LINE__, nPort);
		CloseListen(nSocketFd);
		return -1;
	}
	struct ip_mreq   mreq;

	mreq.imr_multiaddr.s_addr = inet_addr(pszAddr);
#if RL_SUPPORT_MULTICAST_SEND_ASSIGN_LAN
	DCLIENT_DEVICE_INFO infoDevice;
	GetDClientInstance()->GetDeviceInfo(&infoDevice);
	mreq.imr_interface.s_addr = inet_addr(infoDevice.szIPAddr);
#else
	mreq.imr_interface.s_addr = inet_addr("0.0.0.0");
#endif
	if(setsockopt(nSocketFd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof(mreq)) <0)
	{
		rl_log_err( "setsockopt:IP_ADD_MEMBERSHIP ");
		CloseListen(nSocketFd);
		return   -1;
	}  
#if RL_SUPPORT_MULTICAST_SEND_ASSIGN_LAN
	/*此处指定组播数据的出口网卡，如果不设置则会根据路由表指定默认路由出口*/
	struct in_addr addr = {0};
    addr.s_addr=inet_addr(infoDevice.szIPAddr);
	if(-1 == setsockopt(nSocketFd, IPPROTO_IP, IP_MULTICAST_IF, (char *)&addr, sizeof(addr)))
	{
		perror("Setting IP_MULTICAST_IF error:");
		CloseListen(nSocketFd);
		return -1;
	}
#endif
	//2017.11.9 By Will, Discreet客户提出在GPON网络下组播的TTL要设置为5才可以正常 
	unsigned char ttl = 5; 
	if(setsockopt(nSocketFd, IPPROTO_IP,IP_MULTICAST_TTL, &ttl, sizeof(ttl)) < 0)
	{
		rl_log_err("[%s-%d]: setsockopt:IP_MULTICAST_TTL", __FUNCTION__, __LINE__);
		CloseListen(nSocketFd);
		return -1;
	}

	/*
	memset(&tListenAddr, 0, sizeof(tListenAddr));
	tListenAddr.sin_family = AF_INET;
	tListenAddr.sin_addr.s_addr =  DCLIENT_HTONL(INADDR_ANY);
	tListenAddr.sin_port = DCLIENT_HTONS(8400);
	int opt = 1;
	if(setsockopt(nSocketFd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))
	{
		perror("setsockopt failed: ");
		return -1;
	}


	int so_broadcast = 1;
	if (setsockopt(nSocketFd, SOL_SOCKET, SO_BROADCAST, &so_broadcast, sizeof(so_broadcast)))
     {
           perror("setsockopt failed: ");
           return -1;
     }*/
    int nRetry = 0;
	while(nRetry < 100)
	{
		ret = ::bind(nSocketFd, (struct sockaddr *)&tListenAddr, sizeof(tListenAddr));
		if (0 > ret)
		{
			rl_log_err("[%s-%d]: Bind failed,retry %d", __FUNCTION__, __LINE__,nRetry++);
			sleep(1);
			continue;
		}
		break;
	}
	if(ret < 0)
	{
		CloseListen(nSocketFd);
		//exit(0);
		return -1;
	}
	ret = SetLoop(nSocketFd, TRUE);
	if (0 > ret)
	{
		rl_log_err("[%s-%d]: SetLoop failed", __FUNCTION__, __LINE__);
		CloseListen(nSocketFd);
		return -1;
	}

	return nSocketFd;
}

int CConnectControl::AddMemberShip()
{
	char *pszAddr = SOCKET_MULTICAST_ADDR;
	int nPort = SOCKET_MULTICAST_PORT;
	struct ip_mreq   mreq;

	mreq.imr_multiaddr.s_addr = inet_addr(pszAddr);
	mreq.imr_interface.s_addr = inet_addr("0.0.0.0");
	if(setsockopt(m_nMulticastSocketFd, IPPROTO_IP, IP_DROP_MEMBERSHIP, &mreq,sizeof(mreq)) <0)
	{
		rl_log_err( "setsockopt:IP_DROP_MEMBERSHIP ");
		return -1;
	}
	if(setsockopt(m_nMulticastSocketFd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof(mreq)) <0)
	{
		rl_log_err( "setsockopt:IP_ADD_MEMBERSHIP ");
		return -1;
	}
	return 0;
}

int CConnectControl::GetMulticastSocketFd()
{
	return m_nMulticastSocketFd;
}

int CConnectControl::GetTcpSocketFd()
{
	return m_nTcpSocketFd;
}

void CConnectControl::SetTcpSocketFd(int nSocketFd)
{
	m_nTcpSocketFd = nSocketFd;
}
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
void CConnectControl::OnFactoryMode()
{
	while (true) {
		if (access("/tmp/factory_mode_network_ok", F_OK)==0)
			break;
		sleep(1);
	}
}
#endif

int CConnectControl::Init()
{
	//通知PHONE，Dclient刚启动，设备未连接任何服务器
	GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_CONNECT_STATUS, 0, 0, 0);
	
	//初始化地址和端口
	DCLIENT_DEVICE_INFO infoDevice;
	if (!GetDClientInstance()->GetDeviceInfo(&infoDevice))
	{
		return -1;
	}

	rl_strcpy_s(m_szLocalIPAddr, sizeof(m_szLocalIPAddr), infoDevice.szIPAddr);
	rl_strcpy_s(m_szMulticastAddr, sizeof(m_szMulticastAddr), SOCKET_MULTICAST_ADDR);
	m_nMulticastListenPort = SOCKET_MULTICAST_PORT;

	//监听组播
	m_nMulticastSocketFd = BuildMulticast();
	if(m_nMulticastSocketFd <= 0)
	{
	    rl_log_err("%s: BuildMulticast failed ",__FUNCTION__);
	}
	//创建socket监听线程
	m_bRecvThreadRun = true;
	pthread_create(&m_tidRecv, NULL, RecvThread, this);

	//获取配置项的连接模式
	m_nConnectMode = GetSettingHandleInstance()->GetConnectMode();

	rl_log_err("%s: connect mode is %s", __FUNCTION__, GetStrConnectModeByMode(m_nConnectMode).c_str());
	
	//直接将初始模式写到内存，方便phone端的操作
	GetDClientInstance()->CfgSetInt(CFG_ID_STATUS, STATUS_KEY_DOORSETTING_CONNECT_MODE, m_nConnectMode);
	StrtokString(infoDevice.szMAC, m_szMac, sizeof(infoDevice.szMAC), ":");	
	rl_log_err("StrtokString src:%s, dest:%s.", infoDevice.szMAC, m_szMac);

	if(m_nConnectMode != DOORSETTING_CONNECT_SERVER_MODE_SDMC)//非SDMC模式下，才去获取网关
	{
		m_bThreadGetGateWay = TRUE;	
	}

	rl_strcpy_s(m_szLastLanIP, sizeof(m_szLastLanIP), infoDevice.szIPAddr);
	rl_strcpy_s(m_szLastSubnetMask, sizeof(m_szLastSubnetMask), infoDevice.szMask);
	rl_strcpy_s(m_szLastGateway, sizeof(m_szLastGateway), infoDevice.szGateWay);
	rl_strcpy_s(m_szLastPrimaryDns, sizeof(m_szLastPrimaryDns), infoDevice.szPrimaryDNS);
	rl_strcpy_s(m_szLastSecondaryDns, sizeof(m_szLastSecondaryDns), infoDevice.szSecondaryDNS);
	//GetSettingHandleInstance()->GetDeviceID(m_szLastDeviceID, sizeof(m_szLastDeviceID));
	//GetSettingHandleInstance()->GetStatus(m_szLastDeviceStatus, sizeof(m_szLastDeviceStatus));
	//GetSettingHandleInstance()->GetDeviceExtension(m_szLastDeviceExtension, sizeof(m_szLastDeviceExtension));
	//GetSettingHandleInstance()->GetDeviceDownloadServer(m_szLastDeviceDownloadServer, sizeof(m_szLastDeviceDownloadServer));
	//GetSettingHandleInstance()->GetDeviceUploadServer(m_szLastDeviceUploadServer, sizeof(m_szLastDeviceUploadServer));
	//GetSettingHandleInstance()->GetDeviceType(m_szLastDeviceType, sizeof(m_szLastDeviceType));
	m_nCloudServerEnable = GetSettingHandleInstance()->GetCloudServerEnable();	
	GetSettingHandleInstance()->GetCloudServerIp(m_szCloudServerIp, sizeof(m_szCloudServerIp));
	m_nCloudServerPort = GetSettingHandleInstance()->GetCloudServerPort();

	//创建TCP连接线程
	m_bTcpThreadRun = true;
	pthread_create(&m_tidTcp, NULL, TcpThread, this);

	//创建获取网关和获取接入服务器的线程
	m_bGetServerThreadRun = true;
	pthread_create(&m_tidGetServer, NULL, GetServerThread, this);

	//判断是否开启云服务连接
	if(m_nCloudServerEnable != 0)
	{
		//进行TCP连接
		GetConnectControlInstance()->RestartConnection(m_szCloudServerIp, m_nCloudServerPort, TRUE);			
		rl_log_debug("connetc akcs m_szCloudServerIp=%s, m_nCloudServerPort=%d",m_szCloudServerIp,m_nCloudServerPort);
		//return 0;//2018.8.18 By will, 即使连上云了也发个DISCOVER信息出去
	}
	m_bHasInit = true;
	rl_log_debug("set tcpthreadrun is true.");

	//发送BOOTUP
	SOCKET_MSG bootupMsg;
	if(GetMsgControlInstance()->BuildBootupMsg(&bootupMsg) < 0)
	{
		return -1;
	}

	rl_log_debug("bootupMsg.nSize = %d", bootupMsg.nSize);
	if(SendMulticastMsg(bootupMsg.byData, bootupMsg.nSize) < 0)
	{
		return -1;
	}

	GetDiscoverControlInstance()->MuticastDeviceInfo();
	return 0;
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.29
******************************************************************/
void CConnectControl::UnInit()
{
	m_bTcpThreadRun = false;
	if (m_tidTcp != 0)
	{
		pthread_join(m_tidTcp, NULL);
	}

	m_bRecvThreadRun = false;
	if (m_tidRecv != 0)
	{
		pthread_join(m_tidRecv, NULL);
	}

	m_bGetServerThreadRun = false;
	if (m_tidGetServer != 0)
	{
		pthread_join(m_tidGetServer, NULL);
	}

	if(m_nMulticastSocketFd > 0)
	{
		CloseListen(m_nMulticastSocketFd);
		m_nMulticastSocketFd = -1;
	}

	if(m_nTcpSocketFd > 0)
	{
		CloseListen(m_nTcpSocketFd);
		m_nTcpSocketFd = -1;
	}

	m_bHasInit = false;
}

int CConnectControl::AutoCheckConnect()
{
	CHAR szLanIP[IP_SIZE];
	CHAR szSubnetMask[IP_SIZE];
	CHAR szGateway[IP_SIZE];
	CHAR szPrimaryDns[IP_SIZE];
	CHAR szSecondaryDns[IP_SIZE];
	CHAR szCloudServerIp[URL_BUFFER_MAX_SIZE]={0};
	UINT nCloudServerEnable;
	INT nCloudServerPort;

	GetSettingHandleInstance()->GetLanIPAddr(szLanIP,sizeof(szLanIP));
	GetSettingHandleInstance()->GetSubnetMask(szSubnetMask, sizeof(szSubnetMask));
	GetSettingHandleInstance()->GetGateway(szGateway, sizeof(szGateway));
	GetSettingHandleInstance()->GetPrimaryDNS(szPrimaryDns, sizeof(szPrimaryDns));
	GetSettingHandleInstance()->GetSecondaryDNS(szSecondaryDns, sizeof(szSecondaryDns));

	nCloudServerEnable = GetSettingHandleInstance()->GetCloudServerEnable();	
	GetSettingHandleInstance()->GetCloudServerIp(szCloudServerIp, sizeof(szCloudServerIp));
	nCloudServerPort = GetSettingHandleInstance()->GetCloudServerPort();
	
	BOOL bReport = FALSE;
	BOOL bAkcsEnableChange = FALSE;

	if(rl_strcmp(szSubnetMask, m_szLastSubnetMask) != 0)
	{
		bReport = TRUE;
	}
	else if(rl_strcmp(szGateway, m_szLastGateway) != 0)
	{
		bReport = TRUE;
		/* ANDY: gateway only maybe empty,no appear "*******"  */
		if(strlen(szGateway) != 0)
		{
			//rl_log_err("this is not bug, killself when gateway changed");
			//exit(0);
		}
	}
	else if(rl_strcmp(szPrimaryDns, m_szLastPrimaryDns) != 0)
	{
		bReport = TRUE;
	}
	else if(rl_strcmp(szSecondaryDns, m_szLastSecondaryDns) != 0)
	{
		bReport = TRUE;
	}
	
	if(nCloudServerEnable != m_nCloudServerEnable)
	{
		bAkcsEnableChange = TRUE;
	}
	
	//rl_log_debug("szLanIP=%s,%s", szLanIP, m_szLastLanIP);
	if((rl_strcmp(szLanIP, m_szLastLanIP) != 0) || bReport)
	{
		rl_strcpy_s(m_szLastLanIP, sizeof(m_szLastLanIP), szLanIP);
		if(IsValidIPAddr(szLanIP))
		{
		#if RL_SUPPORT_CONTROL4
			GetControlFourControlInstance()->reConnection();
		#endif
			
			rl_log_debug("%s:%d SetReconnectFlag(TRUE)", __FILE__, __LINE__);
			SetReconnectFlag(TRUE);

			//设备信息变化时需要DISCOVER设备信息出去
			GetDiscoverControlInstance()->MuticastDeviceInfo();

			//也发discover出去请求其他设备的信息
			GetDiscoverControlInstance()->OnRecvAutoDiscover();

			//将最后的网关等信息存储下来
			rl_strcpy_s(m_szLastSubnetMask, sizeof(m_szLastSubnetMask), szSubnetMask);
			rl_strcpy_s(m_szLastGateway, sizeof(m_szLastGateway), szGateway);
			rl_strcpy_s(m_szLastPrimaryDns, sizeof(m_szLastPrimaryDns), szPrimaryDns);
			rl_strcpy_s(m_szLastSecondaryDns, sizeof(m_szLastSecondaryDns), szSecondaryDns);

		}
	}
	if(bAkcsEnableChange)
	{
		rl_strcpy_s(m_szCloudServerIp, sizeof(m_szCloudServerIp), szCloudServerIp);
		m_nCloudServerEnable = nCloudServerEnable;
		m_nCloudServerPort = nCloudServerPort;
		if(nCloudServerEnable)
		{
			GetConnectControlInstance()->RestartConnection(m_szCloudServerIp, m_nCloudServerPort, TRUE);	
		}
		else
		{
			SetReconnectFlag(TRUE);
			//发送BOOTUP
			SOCKET_MSG bootupMsg;
			if(GetMsgControlInstance()->BuildBootupMsg(&bootupMsg) < 0)
			{
				return -1;
			}

			rl_log_debug("bootupMsg.nSize = %d", bootupMsg.nSize);
			if(SendMulticastMsg(bootupMsg.byData, bootupMsg.nSize) < 0)
			{
				return -1;
			}
		}
	}
	return 0;
}


//自动上报状态
int CConnectControl::AutoReportStatus()
{
	CHAR szDeviceID[DEVICE_ID_SIZE] = {0};
	CHAR szDeviceExtension[INT_SIZE] = {0};
	CHAR szDeviceType[INT_SIZE] = {0};
	CHAR szDeviceStatus[DEVICE_STATUS_SIZE] = {0};
	CHAR szDeviceDownloadServer[VALUE_SIZE] = {0};
	CHAR szUploadServer[VALUE_SIZE] = {0};
	CHAR szLocation[VALUE_SIZE] = {0};

	GetSettingHandleInstance()->GetDeviceID(szDeviceID, sizeof(szDeviceID));
	GetSettingHandleInstance()->GetStatus(szDeviceStatus, sizeof(szDeviceStatus));
	GetSettingHandleInstance()->GetDeviceExtension(szDeviceExtension, sizeof(szDeviceExtension));
	GetSettingHandleInstance()->GetDeviceDownloadServer(szDeviceDownloadServer, sizeof(szDeviceDownloadServer));
	GetSettingHandleInstance()->GetDeviceUploadServer(szDeviceDownloadServer, sizeof(szDeviceDownloadServer));
	GetSettingHandleInstance()->GetDeviceType(szDeviceType, sizeof(szDeviceType));
	GetSettingHandleInstance()->GetDeviceLocation(szLocation, sizeof(szLocation));
	int nDiscoverMode = GetSettingHandleInstance()->GetDiscoveryMode();

	BOOL bReport = FALSE;

	if(rl_strcmp(szDeviceID, m_szLastDeviceID) != 0)
	{
		rl_strcpy_s(m_szLastDeviceID, sizeof(m_szLastDeviceID), szDeviceID);
		bReport = TRUE;
	}

	if(rl_strcmp(szDeviceExtension, m_szLastDeviceExtension) != 0)
	{
		rl_strcpy_s(m_szLastDeviceExtension, sizeof(m_szLastDeviceExtension), szDeviceExtension);
		bReport = TRUE;
	}

	if(rl_strcmp(szDeviceType, m_szLastDeviceType) != 0)
	{
		rl_strcpy_s(m_szLastDeviceType, sizeof(m_szLastDeviceType), szDeviceType);
		bReport = TRUE;
	}

	if(rl_strcmp(szLocation, m_szLastLocation) != 0)
	{
		rl_strcpy_s(m_szLastLocation, sizeof(m_szLastLocation), szLocation);
		bReport = TRUE;
	}

	if(rl_strcmp(szDeviceDownloadServer, m_szLastDeviceDownloadServer) != 0)
	{
		rl_strcpy_s(m_szLastDeviceDownloadServer, sizeof(m_szLastDeviceDownloadServer), szDeviceDownloadServer);
		bReport = TRUE;
	}

	if(rl_strcmp(szUploadServer, m_szLastDeviceUploadServer) != 0)
	{
		rl_strcpy_s(m_szLastDeviceUploadServer, sizeof(m_szLastDeviceUploadServer), szUploadServer);
		bReport = TRUE;
	}

	if(!bReport && nDiscoverMode != m_nLastDiscoverMode)
	{
		m_nLastDiscoverMode = nDiscoverMode;
		GetDiscoverControlInstance()->MuticastDeviceInfo();
	}
	
	//是云模式就不用自动再次上报状态
	if(!bReport || (m_nConnectMode == DOORSETTING_CONNECT_SERVER_MODE_CLOUD))
	{
		return 0;
	}
	else
	{
		//判断IP是否合法，不合法则退出，当IP获取到时自动会上报
		CHAR szLanIP[IP_SIZE];
		GetSettingHandleInstance()->GetLanIPAddr(szLanIP,sizeof(szLanIP));
		if(!IsValidIPAddr(szLanIP))
		{
			return 0;
		}
	}

	//设备信息变化时需要DISCOVER设备信息出去
	GetDiscoverControlInstance()->MuticastDeviceInfo();

	SOCKET_MSG socketMsg;
	if(GetMsgControlInstance()->BuildReportStatusMsg(&socketMsg) < 0)
	{
		return -1;
	}

	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		return -1;
	}

	return 0;
}

//处理定时器
int CConnectControl::ProcessBaseTimer()
{
	static int i=0;

	BOOL bOneSecondFlag = FALSE;
	BOOL bOneHourFlag = FALSE;
	BOOL bFiveSecondFlag = FALSE;
	BOOL bTenSecondFlag = FALSE;
	BOOL bOneMinuteFlag = FALSE;
	if(++i % 1 == 0)
	{
		bOneSecondFlag = TRUE;
		if(i % 5 == 0)
		{
			bFiveSecondFlag = TRUE;
			if(i % 10 == 0)			
			{				
				bTenSecondFlag = TRUE;	
				if(i % 3600 == 0)				
				{					
					bOneHourFlag = TRUE;					
					i = 0;				
				}			
			}
		}
		if(i % 60 == 0)
		{
			bOneMinuteFlag = TRUE;
		}
	}	

	if(bOneSecondFlag)
	{
		AutoCheckConnect();
		AutoReportStatus();
	}
	if(bOneMinuteFlag)
	{		
		//加入组播
		AddMemberShip();
	}
	static int nHeartBeatTime = 0;
	int nHeartBeatExpire = 0;
	//除以2表示是period/2秒
	if(m_nConnectMode == DOORSETTING_CONNECT_SERVER_MODE_CLOUD && m_bHeartBeatNewMode)
	{
		nHeartBeatExpire = CLOUND_HEART_BEAT_EXPIRE;
	}
	else
	{
		nHeartBeatExpire = m_nHeartBeatTimeVal/2;
	}
	if(nHeartBeatTime++ >= nHeartBeatExpire && m_bHeartBeatFlag)
	{
		//发送HeartBeat信息
		SOCKET_MSG socketMsg;
		if(GetMsgControlInstance()->BuildHeartBeatMsg(&socketMsg) < 0)
		{
			nHeartBeatTime = 0;
			return -1;
		}
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
					
			nHeartBeatTime = 0;
			rl_log_err("SendHeartBeatMsg failed.");
			if(m_nCloudServerEnable != 0)
			{
				CHAR szCloudServerIp[URL_BUFFER_MAX_SIZE]={0};
				GetSettingHandleInstance()->GetCloudServerIp(szCloudServerIp, sizeof(szCloudServerIp));
				int nCloudServerPort = GetSettingHandleInstance()->GetCloudServerPort();
				m_nCloudServerPort = nCloudServerPort;
				rl_strcpy_s(m_szCloudServerIp, sizeof(m_szCloudServerIp), szCloudServerIp);
				GetConnectControlInstance()->RestartConnection(m_szCloudServerIp, m_nCloudServerPort, TRUE);
			}
			return -1;
		}
		rl_log_info("SendHeartBeatMsg success.");
		m_bHeartBeatSendFlag = TRUE;
		nHeartBeatTime = 0;
	}
	if(bTenSecondFlag && m_nConnectMode != DOORSETTING_CONNECT_SERVER_MODE_SDMC)//每隔10s检测一次
	{	
		//网关为空，则按照10s,60s,200s,600s,1800s,7200s...这样获取网关
		if(m_nGateWayState == GATE_WAY_STATE_EMPTY)
		{
			static int nTmpGateTimeInterval = 0;
			nTmpGateTimeInterval += 10;	
			if(m_nGateTimeInterval == nTmpGateTimeInterval)
			{
				nTmpGateTimeInterval = 0;
				switch(m_nGateTimeInterval)
				{
				case 10:
					{
						m_nGateTimeInterval = 60;
					}
					break;
				case 60:
					{
						m_nGateTimeInterval = 200;
					}
					break;
				case 200:
					{
						m_nGateTimeInterval = 600;
					}
					break;
				case 600:
					{
						m_nGateTimeInterval = 1800;
					}
					break;
				case 1800:
					{
						m_nGateTimeInterval = 7200;
					}
					break;
				default:
					break;						
				}
				m_bThreadGetGateWay = TRUE;
			}
		}
		//获取网关失败，且有旧的网关，间隔1800S后再获取网关
		else if(m_nGateWayState == GATE_WAY_STATE_FAILED_OLD || m_nGateWayState == GATE_WAY_STATE_FAILED_EMPTY)
		{
			static int nTmpGateTimeInterval = 0;
			nTmpGateTimeInterval += 10;
			if(TIME_INTERVAL_1800S == nTmpGateTimeInterval)
			{
				nTmpGateTimeInterval = 0;
				m_bThreadGetGateWay = TRUE;		
			}		
		}

		//状态为EMPTY的时倿或者获取失败的时候，每隔300S去重新获取Access地址，1800S后重新获取网关
		if(m_nAccessServerState == ACCESS_SERVER_STATE_EMPTY || m_nAccessServerState == ACCESS_SERVER_STATE_FAILED_OLD || m_nAccessServerState == ACCESS_SERVER_STATE_FAILED_EMPTY )
		{
			static int nTmpAccessTimeInterval = 0;
			static int nTmpGateTimeInterval = 0;
			nTmpAccessTimeInterval += 10;	
			nTmpGateTimeInterval += 10;
			if(TIME_INTERVAL_300S == nTmpAccessTimeInterval)
			{
				nTmpAccessTimeInterval = 0;
				m_bThreadGetAccess = TRUE;
			}
			if(nTmpGateTimeInterval >= TIME_INTERVAL_1800S)
			{
				nTmpGateTimeInterval = 0;
				m_bThreadGetGateWay = TRUE;
			}
		}
		//tcp连接失败，则每隔10S获取重新获取Acceess,1800S重新获取网关
		if(m_nTcpConnectStatus == TCP_CONNECT_STATUS_DISCONNECT || m_nTcpConnectStatus == TCP_CONNECT_STATUS_FAILED)
		{
			static int nTmpAccessTimeInterval = 0;
			nTmpAccessTimeInterval += 10;
			m_bThreadGetAccess = TRUE;
			if(TIME_INTERVAL_1800S == nTmpAccessTimeInterval)
			{
				nTmpAccessTimeInterval = 0;
				m_bThreadGetGateWay = TRUE;		
			}
		}			
	}
	else if(bTenSecondFlag && !m_bConnectSucceed && m_nConnectMode == DOORSETTING_CONNECT_SERVER_MODE_SDMC)//未连接，且为SDMC模式，则主动连接SDMC
	{
		CHAR szSDMCServer[IP_SIZE] = {0};
		GetSettingHandleInstance()->GetSDMCServerIP(szSDMCServer, sizeof(szSDMCServer));
		INT nSDMCServerPort = GetSettingHandleInstance()->GetSDMCServerPort();
		if(rl_strcmp(m_szTcpServerAddr, szSDMCServer) != 0)
		{
			RestartConnection(szSDMCServer, nSDMCServerPort, TRUE);
		}
	}
	static int nTryCnt = 0;
	//发送心跳成功后，10s之内要有ack，如果没有，则表示连接断开
	if(m_bHeartBeatNewMode && m_bHeartBeatSendFlag && (nTryCnt <= 10))
	{
		if(m_bHeartBeatAckFlag)
		{
			m_bHeartBeatAckFlag = FALSE;
			m_bHeartBeatSendFlag = FALSE;
			nTryCnt = 0;
		}
		else if(++nTryCnt >= 10)
		{
			SetReconnectFlag(TRUE);
			m_bHeartBeatSendFlag = FALSE;
			nTryCnt = 0;
		}
	}	
	return 0;
}

//发送组播消息
int CConnectControl::SendMulticastMsg(UCHAR *pData, INT nSize)
{
	if((m_nMulticastSocketFd < 0) || (pData == NULL) || (nSize <= 0))
	{
		return -1;
	}

	char *pszAddr = SOCKET_MULTICAST_ADDR;
	int nPort = SOCKET_MULTICAST_PORT;

	struct sockaddr_in remoteAddr;
	memset(&remoteAddr, 0, sizeof(remoteAddr));
	int len = sizeof(struct sockaddr_in);

	remoteAddr.sin_family = AF_INET;
	remoteAddr.sin_port = DCLIENT_HTONS(nPort);
	remoteAddr.sin_addr.s_addr = inet_addr(pszAddr);
	if(sendto(m_nMulticastSocketFd, pData, nSize, 0, (struct sockaddr*)&remoteAddr, len) <= 0)
	{
		rl_log_err("send multicast failed.");
		return -1;
	}

	return 0;
}

UINT CConnectControl::GetMulticastListenPort()
{
	return m_nMulticastListenPort;
}
void CConnectControl::GetLocalIPAddr(CHAR *pszLocalIPAddr, INT nSize)
{
	DCLIENT_DEVICE_INFO infoDevice;
	GetDClientInstance()->GetDeviceInfo(&infoDevice);
	rl_strcpy_s(pszLocalIPAddr, nSize, infoDevice.szIPAddr);
}
void CConnectControl::GetMulticastAddr(CHAR *pszMulticastAddr, INT nSize)
{
	rl_strcpy_s(pszMulticastAddr, nSize, m_szMulticastAddr);
}

UINT CConnectControl::GetTcpServerPort()
{
	return m_nTcpServerPort;
}
void CConnectControl::SetTcpServerPort(UINT nPort)
{
	m_nTcpServerPort = nPort;
}
void CConnectControl::GetTcpServerAddr(char *pszTcpServerAddr, UINT nSize)
{
	rl_strcpy_s(pszTcpServerAddr, nSize, m_szTcpServerAddr);
}	
void CConnectControl::SetTcpServerAddr(char *pszTcpServerAddr)
{
	rl_strcpy_s(m_szTcpServerAddr, sizeof(m_szTcpServerAddr), pszTcpServerAddr);
}

void CConnectControl::SetReconnectFlag(BOOL bReConnectFlag)
{
	m_bReConnectFlag = bReConnectFlag;
}

bool CConnectControl::GetReConnectFlag()
{
	return m_bReConnectFlag;
}

void CConnectControl::SetHeartBeatExpire(INT nHeartBeatExpire)
{
	m_nHeartBeatTimeVal = nHeartBeatExpire;
}

int CConnectControl::GetHeartBeatExpire()
{
	return m_nHeartBeatTimeVal;
}

void CConnectControl::SetHeartBeatFlag(INT nHeartBeatFlag)
{
	m_bHeartBeatFlag= nHeartBeatFlag;
}

bool CConnectControl::GetHeartBeatFlag()
{
	return m_bHeartBeatFlag;
}

int CConnectControl::RestartConnection(CHAR *pszRemoteIPAddr, UINT nPort, BOOL bForceConnect)
{
	if(pszRemoteIPAddr == NULL)
	{
		return -1;
	}

	CHAR szCurrRemoteIPAddr[VALUE_SIZE] = {0};
	UINT nCurrPort = GetTcpServerPort();
	GetTcpServerAddr(szCurrRemoteIPAddr, sizeof(szCurrRemoteIPAddr));
	if(!bForceConnect && (rl_strcmp(pszRemoteIPAddr, szCurrRemoteIPAddr) == 0) && (nCurrPort == nPort))
	{
		return 0;
	}

	rl_log_err("%s: connect %s:%d", __FUNCTION__, pszRemoteIPAddr, nPort);
	SetTcpServerAddr(pszRemoteIPAddr);
	SetTcpServerPort(nPort);
	
	SetReconnectFlag(TRUE);

	return 0;
}

//发送TCP消息
int CConnectControl::SendTcpMsg(UCHAR *pData, INT nSize)
{
	int nSocketFd = GetTcpSocketFd();

	if((nSocketFd < 0) || (pData == NULL) || (nSize <= 0))
	{
		return -1;
	}
	int nRet = 0;
	//fix me:设置为非阻塞模式，如果是buff满了，则尝试3次
	for(int i=0; i<3; i++)
	{
		nRet = send(nSocketFd, pData, nSize, MSG_DONTWAIT);//设置为非阻塞模式
		if(nRet <= 0)
		{		
			if(errno == 11)//EAGAIN , buff full
			{
				usleep(200*1000);
				continue;
			}
		}
		break;
	}	
	if(nRet <= 0)
	{
		rl_log_err("%s: send failed.", __FUNCTION__);
		return -1;
	}

	return 0;
}

//发送UDP消息
int CConnectControl::SendUdpMsg(CHAR *pszRemoteAddr, UINT nRemotePort, UCHAR *pData, INT nSize)
{
	if(rl_str_isempty(pszRemoteAddr) || (pData == NULL))
	{
		return -1;
	}

	char *pszAddr = pszRemoteAddr;
	int nPort = nRemotePort;

	struct sockaddr_in remoteAddr;
	memset(&remoteAddr, 0, sizeof(remoteAddr));
	int len = sizeof(struct sockaddr_in);

	remoteAddr.sin_family = AF_INET;
	remoteAddr.sin_port = DCLIENT_HTONS(nPort);
	remoteAddr.sin_addr.s_addr = inet_addr(pszAddr);
	int nRet = 0;
	for(int i=0; i<3; i++)
	{
		nRet = sendto(m_nMulticastSocketFd, pData, nSize, MSG_DONTWAIT, (struct sockaddr*)&remoteAddr, len);
		if(nRet <= 0)
		{		
			if(errno == 11)//EAGAIN , buff full
			{
				usleep(200*1000);
				continue;
			}
		}
		break;
	}
	if(nRet <= 0)
	{
		rl_log_err("%s: send udp failed.", __FUNCTION__);
		return -1;
	}

	return 0;
}

int CConnectControl::SetDownloadServer()
{
#if 0
	char szDownloadServer[VALUE_SIZE] = {0};
	char szNewDownLoadUrl[URL_SIZE] = {0};
	GetSettingHandleInstance()->GetDeviceDownloadServer(szDownloadServer, sizeof(szDownloadServer));
	rl_sprintf_s(szNewDownLoadUrl, sizeof(szNewDownLoadUrl), "%s/Download/RemotePhonebook/phonebook.xml", szDownloadServer);
	char szRemoteUrl[URL_SIZE] = {0};
	if(GetSettingHandleInstance()->GetDeviceRemoteBook(szRemoteUrl, sizeof(szRemoteUrl)) < 0)
	{
		return -1;
	}
	if(szDownloadServer[0] != '\0' && rl_strcmp(szNewDownLoadUrl, szRemoteUrl) != 0)
	{
		GetSettingHandleInstance()->SetRemoteBook(szNewDownLoadUrl);
		ipc_broadcast(BROAD_ID_CONFIG_CHANGED, CFG_ID_REMOTEBOOK, 0, NULL, 0);
	}
#endif // 0
	return 0;
}


//通过RPS服务器获取登录网关的ip和port
//http://rps.akuvox.com:8080/v2/redirect?user=xxxx
int CConnectControl::GetGateServer()
{	
	//CHAR szRecvBuf[CONNECT_RECV_BUF_SIZE] = {0};
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	CHAR szRpsUrl[CFG_URL_MAX_SIZE]={0};
	GetSettingHandleInstance()->GetCloudServerRpsServer(szRpsUrl, sizeof(szRpsUrl));
	if(m_bHasForceRPS)//指定专门的RPS
	{
		m_bHasForceRPS = FALSE;
		rl_strcpy_s(szRpsUrl, sizeof(szRpsUrl), m_szHasForceRPS);
	}
	
	if(rl_str_isempty(szRpsUrl))
	{
		rl_log_err("GetGateServer failed, rps url is empty.");
		m_nGateWayState = GATE_WAY_STATE_NONE;
		return -1;
	}
	//进行RPS的兼容处理
	string strUrl = szRpsUrl;
	int n = strUrl.find("/redirect?mac=");
	if(n != string::npos)
	{
		string strSubUrl = strUrl.substr(0, n);
		memset(szRpsUrl, 0, sizeof(szRpsUrl));
		rl_strcpy_s(szRpsUrl, sizeof(szRpsUrl), strSubUrl.data());	
	}
	//对MAC进行凯撒加密
	std::string strKSEncryptMac = AKKSEncryptMAC(m_szMac, rl_strlen(m_szMac));
	rl_log_err("EncryptMAC before:%s, after:%s.", m_szMac, strKSEncryptMac.c_str());
	rl_sprintf_s(szUrl, sizeof(szUrl), "%s%s?user=%s", szRpsUrl, DCLIENT_RPS_API_V3, strKSEncryptMac.c_str());
	rl_log_err("%s : RPS url = %s", __FUNCTION__, szUrl);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", "", pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			//连接不上则用旧的gateserver
			if(i < 2)
			{
				sleep(20);
				continue;
			}
			rl_log_err("GET gateway serverip and port failed");
			CHAR szGateServer[URL_SIZE] = {0};	
			GetSettingHandleInstance()->GetCloudServerGateServer(szGateServer, sizeof(szGateServer));
			if(rl_str_isempty(szGateServer))
			{
				m_nGateWayState = GATE_WAY_STATE_FAILED_EMPTY;
			}
			else
			{
				m_nGateWayState = GATE_WAY_STATE_FAILED_OLD;
			}
			//释放内存
			RL_FREE(pszRecvBuf);
			RL_FREE(pszHeadRecvBuf);
			
			return -1;
		}
		break;
	}
	//解析出登录网关的ip和端口
	CLOUD_SERVER_INFO gateServerInfo;
	rl_memset(&gateServerInfo, 0, sizeof(gateServerInfo));
	ParseRecvInfo(pszRecvBuf, &gateServerInfo);
	CHAR szGateServerIp[IP_SIZE] = {0};	
	INT nGateServerPort = 0;
	
	if(sscanf(gateServerInfo.szGatewayAddr, "%[^:]:%d", szGateServerIp, &nGateServerPort) == 2)
	{
		rl_log_err("GetGateServer success GateServer=%s:%d.", szGateServerIp, nGateServerPort);
		//将网关的ip和端口写到配置项里面
		GetSettingHandleInstance()->SetCloudServerGate(szGateServerIp, nGateServerPort);
		m_nGateWayState = GATE_WAY_STATE_SUCCEED;
		m_nGateTimeInterval = 10;
		//释放内存
		RL_FREE(pszRecvBuf);
		RL_FREE(pszHeadRecvBuf);
		return 0;
	}
	//如果是本地云，则不清除网关
	if(GetSettingHandleInstance()->GetSubServerMode() == DCLIENT_SUB_SERVER_MODE_NORMAL)
	{
		//如果RPS没有获取到gateway server，则直接清除掉gateway server
		GetSettingHandleInstance()->SetCloudServerGate("", 0);
		m_nGateWayState = GATE_WAY_STATE_EMPTY;		
	}
	else if(GetSettingHandleInstance()->GetSubServerMode() == DCLIENT_SUB_SERVER_MODE_LOCAL)
	{
		m_nGateWayState = GATE_WAY_STATE_FAILED_OLD;	
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return -1;
}

//通过网关获取接入服务器的ip和port
//http://***********:8008/login?user=xxxx&passwd=yyyy  (对密码做一次md5) 登录名默认为MAC地址，密码默认为akuvox
int CConnectControl::GetAccessServer()
{	
	//CHAR szRecvBuf[CONNECT_RECV_BUF_SIZE] = {0};
	CHAR *pszRecvBuf = NULL;
	INT nSize = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	CHAR szToken[CLOUDSERVER_TOKEN_SIZE] = {0};
	CHAR szGateServerIp[URL_SIZE] = {0};
	INT nGateServerPort = 0;
	char szHeadBuf[BUF_SIZE] = {0};
	//char szHeadRecvBuf[BUF_SIZE] = {0};
	CHAR *pszHeadRecvBuf = NULL;
	GetSettingHandleInstance()->GetCloudServerGateServer(szGateServerIp, sizeof(szGateServerIp));
	nGateServerPort = GetSettingHandleInstance()->GetCloudServerGatePort();
	GetSettingHandleInstance()->GetCloudServerToken(szToken, sizeof(szToken));
	m_nAccessServerState = ACCESS_SERVER_STATE_NONE;
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"dev-version: %d", GATE_PLAT_FORM_VER_NEW);
	if(rl_str_isempty(szGateServerIp))
	{
		rl_log_err("%s:gw server ip is empty", __FUNCTION__);
		m_nAccessServerState = ACCESS_SERVER_STATE_NONE;
		return -1;
	}
	CHAR szTmpServerAndPort[VALUE_SIZE] = {0};
	if(m_bHasForceGateWay)
	{
		m_bHasForceGateWay = FALSE;
		rl_sprintf_s(szTmpServerAndPort, sizeof(szTmpServerAndPort), "%s", m_szHasForceGateWay);
	}
	else
	{
		rl_sprintf_s(szTmpServerAndPort, sizeof(szTmpServerAndPort), "%s:%d", szGateServerIp, nGateServerPort);
	}

	if(m_bHasLoginGW && !rl_str_isempty(szToken))
	{		
		//fix me:之前为了兼容智能服务器的问题，目前出现csmain挂掉，重新获取网关的时候，直接走旧版本。目前不知道该修改对智能服务器有啥影响，先撤回该修改。
		//rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"dev-version: 4600");
		rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s/accessserver?token=%s", szTmpServerAndPort, szToken);
	}
	else
	{
		char szPasswd[VALUE_SIZE] = {0};	
		rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", m_szMac, AES_KEY_V1_MASK);
		//对密码进行MD5计算
		CHAR szPasswdMD5[MD5_SIZE] = {0};
		MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
		std::string strKSEncryptMac = AKKSEncryptMAC(m_szMac, rl_strlen(m_szMac));
		rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s/login?user=%s&passwd=%s", szTmpServerAndPort, strKSEncryptMac.c_str(), szPasswdMD5);
	}
	rl_log_err("%s : GateServer url = %s,%s", __FUNCTION__, szUrl, szHeadBuf);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2 && m_nTcpConnectStatus == TCP_CONNECT_STATUS_CONNECT)//如果是非TCP_CONNECT_STATUS_CONNECT的状态，则会10S就请求一次，就不用sleep20S。
			{
				sleep(20);
				continue;
			}
			rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
			CHAR szAccessServer[URL_SIZE] = {0};	
			GetSettingHandleInstance()->GetCloudServerIp(szAccessServer, sizeof(szAccessServer));
			if(rl_str_isempty(szAccessServer))
			{
				m_nAccessServerState = ACCESS_SERVER_STATE_FAILED_EMPTY;
			}
			else
			{
				m_nAccessServerState = ACCESS_SERVER_STATE_FAILED_OLD;
			}
			//释放内存
			RL_FREE(pszRecvBuf);
			RL_FREE(pszHeadRecvBuf);
			return -1;
		}
		break;
	}
	CLOUD_SERVER_INFO cloudServerInfo;
	rl_memset(&cloudServerInfo, 0, sizeof(cloudServerInfo));
	cloudServerInfo.nResult = -1;//默认当做解析失败，解析成功为0
	int nPlatformVer = OnAccessReqHttpRecv(cloudServerInfo, pszHeadRecvBuf, pszRecvBuf);
	CHAR szAccessServerIp[VALUE_SIZE] = {0};	
	CHAR szAccessPort[INT_SIZE] = {0};
	INT nAccessServerPort = -1;
	if(cloudServerInfo.nResult != 0)
	{
		rl_log_err("%s:message(%s) platformver=%d", __FUNCTION__, cloudServerInfo.szMessage, nPlatformVer);
		char szPasswd[VALUE_SIZE] = {0};	
		rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", m_szMac, AES_KEY_V1_MASK);
		//对密码进行MD5计算
		CHAR szPasswdMD5[MD5_SIZE] = {0};
		MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
		if(nPlatformVer < GATE_PLAT_FORM_VER_NEW)
		{
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"dev-version: %d", GATE_PLAT_FORM_VER_OLD);
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s/login?user=%s&passwd=%s", szTmpServerAndPort, m_szMac, szPasswdMD5);
		}
		else
		{
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"dev-version: %d", GATE_PLAT_FORM_VER_NEW);
			std::string strKSEncryptMac = AKKSEncryptMAC(m_szMac, rl_strlen(m_szMac));
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s/login?user=%s&passwd=%s", szTmpServerAndPort, strKSEncryptMac.c_str(), szPasswdMD5);
		}
		rl_log_err("%s : GateServer url = %s, %s", __FUNCTION__, szUrl, szHeadBuf);
		for(int i=0; i<3; i++)
		{
			if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
			{
				if(i < 2 && m_nTcpConnectStatus == TCP_CONNECT_STATUS_CONNECT)//如果是非TCP_CONNECT_STATUS_CONNECT的状态，则会10S就请求一次，就不用sleep20S。
				{
					sleep(20);
					continue;
				}
				rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
				CHAR szAccessServer[URL_SIZE] = {0};	
				GetSettingHandleInstance()->GetCloudServerIp(szAccessServer, sizeof(szAccessServer));
				if(rl_str_isempty(szAccessServer))
				{
					m_nAccessServerState = ACCESS_SERVER_STATE_FAILED_EMPTY;
				}
				else
				{
					m_nAccessServerState = ACCESS_SERVER_STATE_FAILED_OLD;
				}
				//释放内存
				RL_FREE(pszRecvBuf);
				RL_FREE(pszHeadRecvBuf);
				return -1;
			}
			break;
		}
		OnAccessReqHttpRecv(cloudServerInfo, pszHeadRecvBuf, pszRecvBuf);
	}
	if(cloudServerInfo.nResult == 0)
	{
		if(sscanf(cloudServerInfo.szAccessSrv, "%[^:]:%[0-9]", szAccessServerIp, szAccessPort) == 2)
		{
			if((!rl_str_isempty(szAccessServerIp)) && (!rl_str_isempty(szAccessPort)))
			{
				nAccessServerPort = rl_atoi(szAccessPort);

				rl_log_err("%s:access=%s:%d,token=%s", __FUNCTION__, szAccessServerIp, nAccessServerPort, cloudServerInfo.szToken);

				//将access服务器的ip和端口写进配置文件里面去
				GetSettingHandleInstance()->SetCloudServerIp(szAccessServerIp);
				GetSettingHandleInstance()->SetCloudServerPort(nAccessServerPort);
				if(!rl_str_isempty(cloudServerInfo.szToken))
				{
					GetSettingHandleInstance()->SetCloudServerToken(cloudServerInfo.szToken);
				}
				if(!rl_str_isempty(szAccessServerIp))
				{
					GetSettingHandleInstance()->SetCloudServerEnable(1);
				}
				m_nAccessServerState = ACCESS_SERVER_STATE_SUCCEED;
			}
		}
		else 
		{
			GetSettingHandleInstance()->SetCloudServerIp("");
			GetSettingHandleInstance()->SetCloudServerPort(0);
			GetSettingHandleInstance()->SetCloudServerToken("");
			m_nAccessServerState = ACCESS_SERVER_STATE_EMPTY;		
			rl_log_err("%s:addr(%s) invalid.", __FUNCTION__, cloudServerInfo.szAccessSrv);	
		}
		GetSettingHandleInstance()->SetWebSrv(cloudServerInfo.szWebSrv);
		GetSettingHandleInstance()->SetVrtspSrv(cloudServerInfo.szVrtspSrv);
		GetSettingHandleInstance()->SetFtpSrv(cloudServerInfo.szFtpSrv);
		GetSettingHandleInstance()->SetPBXSrv(cloudServerInfo.szPBXSrv);	
		SetPBXSrvPort(cloudServerInfo.szPBXSrv);
		m_bHasLoginGW = TRUE;
		rl_log_err("%s: websrv:%s, rtspsrv:%s, ftpsrv:%s, pbxsrv:%s", __FUNCTION__, cloudServerInfo.szWebSrv, cloudServerInfo.szVrtspSrv, cloudServerInfo.szFtpSrv, cloudServerInfo.szPBXSrv);

		//SET CLOUD MODE WHEN GET SERVER SUCCESS
		SetConnectMode(DOORSETTING_CONNECT_SERVER_MODE_CLOUD);
	}
	else 
	{
		rl_log_err("%s:message(%s)", __FUNCTION__, cloudServerInfo.szMessage);	
		GetSettingHandleInstance()->SetCloudServerIp("");
		GetSettingHandleInstance()->SetCloudServerPort(0);
		GetSettingHandleInstance()->SetCloudServerToken("");
		m_nAccessServerState = ACCESS_SERVER_STATE_EMPTY;		
		rl_log_err("%s:addr(%s) invalid.", __FUNCTION__, cloudServerInfo.szAccessSrv);	
	}
	RestartConnection(szAccessServerIp, rl_atoi(szAccessPort), TRUE);
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return 0;
}

int CConnectControl::ParseRecvInfo(char *pBuf, CLOUD_SERVER_INFO *pServerInfo)
{
	if(pBuf == NULL || pServerInfo == NULL)
	{
		return -1;
	}
	memset(pServerInfo, 0, sizeof(CLOUD_SERVER_INFO));
	pServerInfo->nResult = -1;
	cJSON * root = NULL;   
	cJSON *object = NULL;    
	cJSON* item = NULL;
	root = cJSON_Parse(pBuf); 
	if (!root) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		return -1;
	}
	
	item=cJSON_GetObjectItem(root,"result");
	if(item)
	{
		pServerInfo->nResult = item->valueint;
	}
	item=cJSON_GetObjectItem(root,"message");
	if(item)
	{
		rl_strcpy_s(pServerInfo->szMessage, sizeof(pServerInfo->szMessage), item->valuestring);
	}
	object = cJSON_GetObjectItem(root, "datas");
	if(object)
	{
		item=cJSON_GetObjectItem(object,"gate_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szGatewayAddr, sizeof(pServerInfo->szGatewayAddr), item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"web_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szWebSrv, sizeof(pServerInfo->szWebSrv), item->valuestring);
		}
		//DON'T PARSE rest_server on device side anymore. 2019.4.13 By Will.
		//item=cJSON_GetObjectItem(object,"rest_server");
		//if(item)
		//{
		//	rl_strcpy_s(pServerInfo->szWebSrv, sizeof(pServerInfo->szWebSrv), item->valuestring);
		//}
		item=cJSON_GetObjectItem(object,"access_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szAccessSrv, sizeof(pServerInfo->szAccessSrv), item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"vrtsp_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szVrtspSrv, sizeof(pServerInfo->szVrtspSrv), item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"pbx_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szPBXSrv, sizeof(pServerInfo->szPBXSrv), item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"ftp_server");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szFtpSrv, sizeof(pServerInfo->szFtpSrv), item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"token");
		if(item)
		{
			rl_strcpy_s(pServerInfo->szToken, sizeof(pServerInfo->szToken), item->valuestring);
		}	
		item=cJSON_GetObjectItem(object,"platform_ver");
		if(item)
		{
			SetAkcsVersion(rl_atoi(item->valuestring));
		}	
	}
	cJSON_Delete(root);

	//Use access server IP address for other servers if they were empty. Because the response from server lower than V4.4 only contains access server address. 2019.4.17, By Will.
	if((!rl_str_isempty(pServerInfo->szAccessSrv)) && 
		(rl_str_isempty(pServerInfo->szVrtspSrv) || rl_str_isempty(pServerInfo->szPBXSrv) || rl_str_isempty(pServerInfo->szFtpSrv) || rl_str_isempty(pServerInfo->szWebSrv)))
	{
		//PARSE THE ACCESS IP ADDRESS
		CHAR szAccessServerIp[IP_SIZE] = {0};	
		CHAR szAccessPort[INT_SIZE] = {0};
		if(sscanf(pServerInfo->szAccessSrv, "%[^:]:%[0-9]", szAccessServerIp, szAccessPort) == 2)
		{
			if(rl_str_isempty(pServerInfo->szWebSrv))
			{
				rl_sprintf_s(pServerInfo->szWebSrv, sizeof(pServerInfo->szWebSrv), "%s:443", szAccessServerIp);
			}
			if(rl_str_isempty(pServerInfo->szPBXSrv))
			{
				rl_sprintf_s(pServerInfo->szPBXSrv, sizeof(pServerInfo->szPBXSrv), "%s:5070", szAccessServerIp);
			}
			if(rl_str_isempty(pServerInfo->szFtpSrv))
			{
				rl_sprintf_s(pServerInfo->szFtpSrv, sizeof(pServerInfo->szFtpSrv), "%s:21", szAccessServerIp);
			}
			if(rl_str_isempty(pServerInfo->szVrtspSrv))
			{
				rl_sprintf_s(pServerInfo->szVrtspSrv, sizeof(pServerInfo->szVrtspSrv), "%s:554", szAccessServerIp);
			}
		}
	}
	
	return 0;
}

int CConnectControl::NotifyNetConnectChange(BOOL bConnectStatus)
{
	if (m_nLastConnectStatus == bConnectStatus)
	{
		return 0;
	}

	m_nLastConnectStatus = bConnectStatus;
	GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_CONNECT_STATUS, 0, 0, m_nLastConnectStatus);
	return 0;
}

int CConnectControl::GetConnectMode()
{
	return m_nConnectMode;
}

int CConnectControl::SetConnectMode(INT nMode)
{
	m_nConnectMode = nMode;	
	GetSettingHandleInstance()->SetConnectMode(nMode);
	return 0;
}

int CConnectControl::SetConnectOriFrom(INT nFrom)
{
	m_nConnectOriFrom = nFrom;
	return 0;
}

int CConnectControl::GetConnectOriFrom()
{
	return m_nConnectOriFrom;
}

int CConnectControl::SetHasForceRpsServer(char *pszServerAddr)
{
	m_bHasForceRPS = TRUE;
	rl_memset(m_szHasForceRPS, 0, sizeof(m_szHasForceRPS));
	rl_strcpy_s(m_szHasForceRPS, sizeof(m_szHasForceRPS), pszServerAddr);
	m_nGateWayState = GATE_WAY_STATE_FORCE;
	return 0;
}

int CConnectControl::SetHasForceGatewayServer(char *pszServerAddr)
{
	m_bHasForceGateWay= TRUE;	
	rl_memset(m_szHasForceGateWay, 0, sizeof(m_szHasForceGateWay));
	rl_strcpy_s(m_szHasForceGateWay, sizeof(m_szHasForceGateWay), pszServerAddr);
	m_nAccessServerState = ACCESS_SERVER_STATE_FORCE;
	return 0;
}

int CConnectControl::SetHasForceAccessServer(char *pszServerAddr)
{
	m_bHasForceAccess= TRUE;	
	rl_memset(m_szHasForceAccess, 0, sizeof(m_szHasForceAccess));
	rl_strcpy_s(m_szHasForceAccess, sizeof(m_szHasForceAccess), pszServerAddr);
	return 0;
}

int CConnectControl::SetHeartBeatAckFlag(BOOL bFlag)
{
	m_bHeartBeatAckFlag = bFlag;
	return 0;
}

int CConnectControl::SetHeartBeatNewModeFlag(BOOL bFlag)
{
	m_bHeartBeatNewMode = bFlag;
	return 0;
}

bool CConnectControl::IsConnectSucceed()
{
	return m_bConnectSucceed;
}

bool CConnectControl::TcpAntiShockConnect()
{
	static int nRetryCount = 0;
	static int nRand20S = GetRandomNumFromTo(1, 20);
	if(GetReConnectFlag())
	{
		return TRUE;
	}
	nRetryCount++;	
	if(m_nTcpConnectStatus == TCP_CONNECT_STATUS_DISCONNECT)
	{
		if(nRetryCount == nRand20S)
		{
			nRetryCount = 0;
			return TRUE;
		}
		else
		{
			return FALSE;
		}
	}
	else if(m_nTcpConnectStatus == TCP_CONNECT_STATUS_CONNECT)
	{
		nRetryCount = 0;
	}
	return TRUE;
}

//5s之后立马返回，不管有没有解析成功
int CConnectControl::DNSParse(char *pszURL, char*pszIP, int nSize)
{
	if(pszURL == NULL || pszIP == NULL)
	{
		return -1;
	}
	int nRet = -1;
	BOOL bTimeOut = TRUE;
	DNS_PARSE_PARAS dnsParseParas;
	memset(&dnsParseParas , 0, sizeof(DNS_PARSE_PARAS));
	dnsParseParas.res = NULL;
	memset(&dnsParseParas.hints, 0, sizeof(struct addrinfo));
	dnsParseParas.hints.ai_family = AF_INET; /* Allow IPv4 */
    dnsParseParas.hints.ai_flags = AI_PASSIVE; /* For wildcard IP address */
    dnsParseParas.hints.ai_protocol = 0; /* Any protocol */
   	dnsParseParas.hints.ai_socktype = SOCK_STREAM;
	rl_strcpy_s(dnsParseParas.szAddrUrl, sizeof(dnsParseParas.szAddrUrl), pszURL);
	if(rl_strcmp(dnsParseParas.szAddrUrl, "") == 0)
		return -1;

	//创建DNS解析线程
	pthread_t tidDns;
	pthread_create(&tidDns, NULL, DnsThread, &dnsParseParas);

	Lock();
	g_tid = tidDns;
	/* 循环查看DnsThread是否成功返回*/
    rl_time_val time_begin;
	rl_gettimeofday(&time_begin);
	rl_time_val time_end;
	rl_gettimeofday(&time_end);
	
    while(rl_get_subtimeint(&time_begin, &time_end) < 6)
    {
        nRet = pthread_kill(tidDns,0);
        if (0 == nRet) /*子线程仍然存在，说明getaddrinfo仍然在阻塞状态*/
        {
            usleep(50*1000); //sleep 50ms
        }
        else if(ESRCH == nRet) /*子线程已经不存在，说明getaddrinfo成功返回了*/         
        {        
            bTimeOut = FALSE;// not timeout
            break;
        }
		rl_gettimeofday(&time_end);
    }
	/* 根据超时标志和输出参数，判断子线程是否自行结束，是则返回成功，否则返回失败*/
    if (!bTimeOut && (NULL != dnsParseParas.res))
    {
        nRet = 0;
		rl_strcpy_s(pszIP, nSize, dnsParseParas.szIp);
    }
    else
    {
#if((RL_PLATFORMID != RL_PLATFORMID_ANDROID) && (RL_PLATFORMID != RL_PLATFORMID_V3S) && (RL_PLATFORMID != RL_PLATFORMID_V3)) 
    	pthread_cancel(tidDns);
#endif	
        nRet = -1;
    }
	
	g_tid = 0;
	Unlock();
	return nRet;
}

//上锁
void CConnectControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁
void CConnectControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}

bool CConnectControl::HasInit()
{
	return m_bHasInit;
}

int CConnectControl::GetShortMac(char *pszMac, int nSize)
{
	rl_strcpy_s(pszMac, nSize, m_szMac);
	return 0;
}

int CConnectControl::GetAkcsVersion()
{
	return m_nAkcsVersion;
}

int CConnectControl::SetAkcsVersion(UINT nVersion)
{
	m_nAkcsVersion = nVersion;
#if 0
	cfg_set_int(CFG_ID_STATUS, STATUS_KEY_CLOUD_PLATFORM_VERSION, nVersion);
#endif // 0
	return 0;
}

int CConnectControl::SetPBXSrvPort(char *pszPBXSrv)
{
	CHAR szPBXServerIp[IP_SIZE] = {0};	
	CHAR szPBXPort[INT_SIZE] = {0};	
	if(sscanf(pszPBXSrv, "%[^:]:%[0-9]", szPBXServerIp, szPBXPort) == 2)
	{
		m_nPBXSrvPort = rl_atoi(szPBXPort);
	}
	return 0;
}

int CConnectControl::GetPBXSrvPort()
{
	return m_nPBXSrvPort;
}

int CConnectControl::SetTcpConnectStatus(int nStatus)
{
	m_nTcpConnectStatus = nStatus;
#if 0
	cfg_set_int(CFG_ID_STATUS, STATUS_KEY_DOORSETTING_TCP_CONNECT_STATUS, nStatus);
#endif // 0
	return 0;
}

int CConnectControl::GetTcpConnectStatus()
{
	return m_nTcpConnectStatus;
}

int CConnectControl::OnAccessReqHttpRecv(CLOUD_SERVER_INFO& cloudServerInfo, char *pszHeadRecvBuf, char *pszRecvBuf)
{
	int nPlatformVer = 0;
	if(pszHeadRecvBuf == NULL || pszRecvBuf == NULL)
	{
		return nPlatformVer;
	}
	//如果szHeadRecvBuf中包含platform_ver：4600,则需要进行解密操作
	CHAR *pPlatformVer = rl_strstr(pszHeadRecvBuf, "platform_ver: ");
	if(pPlatformVer != NULL)
	{	
		
		CHAR szTmp[64] = {0};
		sscanf(pPlatformVer, "%s %d", szTmp,&nPlatformVer);
		if(nPlatformVer >= GATE_PLAT_FORM_VER_OLD)
		{
			UINT nAesLen = 0;
			unsigned char *pBufOutCode = NULL;
			pBufOutCode = base64Decode(pszRecvBuf, &nAesLen, 1);
			//生成解密的key, md5(user)前16位+Akuvox55069013!@
			CHAR szPasswdMD5[MD5_SIZE] = {0};
			MD5Encrypt(m_szMac, szPasswdMD5, sizeof(szPasswdMD5));
			szPasswdMD5[16] = 0;
			CHAR szKey[PASSWORD_SIZE] = {0};
			rl_sprintf_s(szKey, sizeof(szKey), "%s%s", szPasswdMD5, AES_KEY_V1_MASK);			
			AesDecryptByKey((char*)pBufOutCode, pszRecvBuf, nAesLen, szKey, AES_OFFSET_DEFAULT);
			char nCompletion = pszRecvBuf[nAesLen-1];
			pszRecvBuf[nAesLen - nCompletion] = '\0';
			delete []pBufOutCode;
			pBufOutCode = NULL;
		}
	}
	ParseRecvInfo(pszRecvBuf, &cloudServerInfo);
	return nPlatformVer;
}

