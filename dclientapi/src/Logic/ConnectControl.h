#ifndef __CONNECT_CONTROL_H__
#define __CONNECT_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "cJSON.h"

#define DCLIENT_RPS_API "/v2/redirect"
#define DCLIENT_RPS_API_V3 "/v3/redirect"

#define DCLIENT_RPS_URL	"http://rps.akuvox.com:8080"

#define CLOUND_HEART_BEAT_EXPIRE	180
#define TIME_INTERVAL_1800S		1800
#define TIME_INTERVAL_300S		300

#define GATE_PLAT_FORM_VER_NEW	6200
#define GATE_PLAT_FORM_VER_OLD	4600

typedef enum
{
	GATE_WAY_STATE_NONE = -1,
	GATE_WAY_STATE_SUCCEED,
	GATE_WAY_STATE_EMPTY,
	GATE_WAY_STATE_FAILED_EMPTY,
	GATE_WAY_STATE_FAILED_OLD,
	GATE_WAY_STATE_FORCE,
	GATE_WAY_STATE_MAX,
}GATE_WAY_STATE;

typedef enum
{
	ACCESS_SERVER_STATE_NONE = -1,
	ACCESS_SERVER_STATE_SUCCEED,
	ACCESS_SERVER_STATE_EMPTY,
	ACCESS_SERVER_STATE_FAILED_OLD,
	ACCESS_SERVER_STATE_FAILED_EMPTY,
	ACCESS_SERVER_STATE_FORCE,
	ACCESS_SERVER_STATE_MAX,
}ACCESS_SERVER_STATE;

typedef enum
{
	TCP_CONNECT_STATUS_NONE = 0,
	TCP_CONNECT_STATUS_CONNECT,
	TCP_CONNECT_STATUS_FAILED,
	TCP_CONNECT_STATUS_DISCONNECT,
}TCP_CONNECT_STATUS;

typedef enum
{
	TCP_CONNECT_ORI_FROM_NONE = 0,
	TCP_CONNECT_ORI_FROM_SDMC,
	TCP_CONNECT_ORI_FROM_CLOUD,
}TCP_CONNECT_ORI_FROM;

class CLock;

class CConnectControl
{
public:
	CConnectControl();
	~CConnectControl();
	
	//初始化
	int Init();
	void UnInit();		//反初始化
	int ProcessBaseTimer();

	//自动上报状态
	int AutoReportStatus();
	int AutoCheckConnect();

	int BuildMulticast();
	int AddMemberShip();
	int GetMulticastSocketFd();
	int GetTcpSocketFd();
	void SetTcpSocketFd(int nSocketFd);
	int SendMulticastMsg(UCHAR *pData, INT nSize);
	//发送TCP消息
	int SendTcpMsg(UCHAR *pData, INT nSize);
	
	//发送UDP消息
	int SendUdpMsg(CHAR *pszRemoteAddr, UINT nRemotePort, UCHAR *pData, INT nSize);

	//接收组播消息
	int RecvMulticastMsg(SOCKET_MSG *pRecvMsg);
	
	//接收TCP消息
	int RecvTcpMsg(SOCKET_MSG *pRecvMsg);

	//修改remotephonebook的 url
	int SetDownloadServer();

	UINT GetMulticastListenPort();
	void GetLocalIPAddr(CHAR *pszLocalIPAddr, INT nSize);
	void GetMulticastAddr(CHAR *pszMulticastAddr, INT nSize);
	
	UINT GetTcpServerPort();
	void SetTcpServerPort(UINT nPort);
	void GetTcpServerAddr(char *pszTcpServerAddr, UINT nSize);
	void SetTcpServerAddr(char *pszTcpServerAddr);

	void SetReconnectFlag(BOOL bReConnectFlag);
	bool GetReConnectFlag();

	void SetHeartBeatExpire(int nHeartBeatExpire);
	INT GetHeartBeatExpire();
	void SetHeartBeatFlag(BOOL bHeartBeatFlag);
	bool GetHeartBeatFlag();

	int GetGateServer();
	int GetAccessServer();
	int GetConnectMode();
	int SetConnectMode(INT nMode);
	int SetHasForceRpsServer(char *pszServerAddr);
	int SetHasForceGatewayServer(char *pszServerAddr);
	int SetHasForceAccessServer(char *pszServerAddr);	
	int SetHeartBeatAckFlag(BOOL bFlag);
	int SetHeartBeatNewModeFlag(BOOL bFlag);
	
	int DNSParse(char *pszURL, char*pszIP, int nSize);
	
	int RestartConnection(CHAR *pszRemoteIPAddr, UINT nPort, BOOL bForceConnect);
	int ParseRecvInfo(char *pBuf, CLOUD_SERVER_INFO *pServerInfo);
	int NotifyNetConnectChange(BOOL bConnectStatus);

	int GetShortMac(char *pszMac, int nSize);
	bool IsConnectSucceed();

	bool HasInit();
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)	
	void OnFactoryMode();
#endif
	int GetAkcsVersion();
	int SetAkcsVersion(UINT nVersion);
	bool TcpAntiShockConnect();
	int GetPBXSrvPort();
	int SetPBXSrvPort(char *pszPBXSrv);
	int SetTcpConnectStatus(int nStatus);
	int GetTcpConnectStatus();
	int SetConnectOriFrom(INT nFrom);
	int GetConnectOriFrom();
	int OnAccessReqHttpRecv(CLOUD_SERVER_INFO& cloudServerInfo, char *pszHeadRecvBuf, char *pszRecvBuf);
	static CConnectControl *GetInstance();

	bool IsTcpThreadRun();
	bool IsRecvThreadRun();
	bool IsGetServerThreadRun();

	int m_nMulticastSocketFd;
	int m_nTcpSocketFd;
	
private:
	void Lock();
	void Unlock();	

private:
	static CConnectControl *instance;

	CHAR m_szLocalIPAddr[VALUE_SIZE];
	CHAR m_szMulticastAddr[IP_SIZE];
	UINT m_nMulticastListenPort;
	CHAR m_szTcpServerAddr[VALUE_SIZE];
	UINT m_nTcpServerPort;
	BOOL m_bReConnectFlag;

	CHAR m_szLastDeviceID[DEVICE_ID_SIZE];
	CHAR m_szLastDeviceStatus[DEVICE_STATUS_SIZE];
    CHAR m_szLastLanIP[IP_SIZE];
	CHAR m_szLastSubnetMask[IP_SIZE];
	CHAR m_szLastGateway[IP_SIZE];
	CHAR m_szLastPrimaryDns[IP_SIZE];
	CHAR m_szLastSecondaryDns[IP_SIZE];
	CHAR m_szLastDeviceExtension[INT_SIZE];
	CHAR m_szLastDeviceDownloadServer[VALUE_SIZE];
	CHAR m_szLastDeviceUploadServer[VALUE_SIZE];
	CHAR m_szLastDeviceType[INT_SIZE];
	CHAR m_szLastLocation[VALUE_SIZE];
	UINT m_nCloudServerEnable;
	CHAR m_szCloudServerIp[URL_BUFFER_SIZE];
	UINT m_nCloudServerPort;
	UINT m_nHeartBeatTimeVal;
	INT m_nLastDiscoverMode;
	bool m_bHeartBeatFlag;
	CHAR m_szMac[MAC_SIZE];
	bool m_bNeedGetGateServer;
	bool m_bNeedGetAccessServer;	
	pthread_t m_tidRecv;
	pthread_t m_tidTcp;
	pthread_t m_tidGetServer;
	void *m_lock;

	bool m_bHasInit;
	bool m_bHasLoginGW;
	UINT m_nAkcsVersion;
	UINT m_nPBXSrvPort;
	int m_nGateTimeInterval;
	INT m_nConnectOriFrom;

	bool m_bTcpThreadRun;
	bool m_bRecvThreadRun;
	bool m_bGetServerThreadRun;

public:
	INT m_nConnectMode;
	bool m_bHasForceRPS;
	bool m_bHasForceGateWay;
	bool m_bHasForceAccess;
	CHAR m_szHasForceRPS[URL_BUFFER_SIZE];
	CHAR m_szHasForceGateWay[URL_BUFFER_SIZE];
	CHAR m_szHasForceAccess[URL_BUFFER_SIZE];
	INT m_nLastConnectStatus;
	bool m_bHeartBeatAckFlag;
	bool m_bHeartBeatSendFlag;
	bool m_bHeartBeatNewMode;//为了兼容新旧模式的心跳模式
	INT m_nGateWayState;
	INT m_nAccessServerState;
	bool m_bConnectSucceed;
	bool m_bThreadGetGateWay;
	bool m_bThreadGetAccess;
	INT m_nTcpConnectStatus;//TCP连接成功，连接失败，成功后断开
};

CConnectControl *GetConnectControlInstance();

#endif

