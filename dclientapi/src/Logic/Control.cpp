#include "Control.h"
#include "Lock.h"
#include "WaitEvent.h"
#include "DclientDefine.h"
#include "DoorControl.h"
#include "ConnectControl.h"
#include "MsgControl.h"
#include "DownloadControl.h"
#include "AlarmControl.h"
#include "TextMsgControl.h"
#include "AccessInfoControl.h"
#include "BindCodeControl.h"
#include "DiscoverControl.h"
#include "SettingControl.h"
#include "DeviceControl.h"
#include "D2ChannelControl.h"
#include "ActivityControl.h"
#include "DclientMsg.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "dclient_ipc.h"
#include <stdlib.h>
#include <stdio.h>
#include "revision.h"

#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL) 
#include "rlipc.h"
#else
#include "ipc.h"
#endif


#include "rllib.h"
#include <openssl/md5.h>
#include "AES256.h"
#include "curl.h"

#ifdef PJ_HAS_CFGXML
#include "cfgxml.h"
#endif

#include "DClient.h"
#include <fstream>
using namespace std;

// 计算文件的MD5
static int GetFileMD5(char *pFilePath, char *pCurrentMd5)
{
	FILE *file;
	MD5_CTX context;
	int len;
	unsigned char Md5Digist[16];
	unsigned char buffer[1024];

	if(NULL == pCurrentMd5 || NULL == pFilePath)
	{
		rl_log_err("GetFileMD5 failed, the input pFilePath or pCurrentMd5 is NULL.");
		return RL_FAILED;
	}

	if ((file = fopen (pFilePath, "rb")) == NULL)
	{
		rl_log_err("GetFileMD5 failed, open file <%s> failed.\n", pFilePath);
		return RL_FAILED;
	}
	else
	{
		MD5_Init (&context);
		while ((len = fread (buffer, 1, 1024, file))>0)
		{
			MD5_Update (&context, buffer, len);
		}
		MD5_Final (Md5Digist, &context);

		for (int i=0; i<16; i++)
		{
			rl_sprintf_s(&pCurrentMd5[2*i], 3, "%02x", (unsigned char)Md5Digist[i]);
		}

		fclose (file);
	}

	return RL_SUCCESS;
}

void *ProcessThread(void *pData);

void *ProcessThread(void *pData)
{
	CControl *pControl = (CControl*)pData;
	while(!pControl->IsUnInit())
	{
		pControl->ProcessMsg();
	}

	return NULL;
}

void* CControl::TimerLoop()
{
	while(!IsUnInit())
	{
		AddMsg(DCLIENT_MSG_TIMER, TIMER_ID_BASE, 0, NULL, 0);
		usleep(DCLIENT_TIMER_VAL_BASE * 1000);
	}

	return NULL;
}

CControl *GetControlInstance()
{
	return CControl::GetInstance();
}

CControl::CControl()
	: m_bUnInit(true)
	, m_bRuning(false)
	, m_tidProcMsg(0)
{
	m_msgHeader = NULL;
	m_lock = new CLock();
	m_wait = new CWaitEvent();
}

CControl::~CControl()
{
	DelAllMsg();

	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
	if(NULL != m_wait)
	{
		delete (CWaitEvent *)m_wait;
		m_wait = NULL;
	}

	//curl的全局初始化，此功能不是线程安全的，放到这里释放
	curl_global_cleanup();
}

CControl *CControl::instance = NULL;

CControl *CControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CControl();
	}

	return instance;
}

int CControl::Init()
{
	if (!IsUnInit())	//已初始化过的不再初始化
	{
		return 0;
	}
	SetUninitFlag(false);

	//curl的全局初始化，此功能不是线程安全的，放到这里做初始化
	curl_global_init(CURL_GLOBAL_ALL);
	GetDownloadControlInstance()->Init();
	pthread_create(&m_tidProcMsg, NULL, ProcessThread, this);
	GetConnectControlInstance()->Init();
	GetDeviceControlInstance()->Init();

	return 0;
}

/*@function
*******************************************************************
功  能:  反初始化.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.29
******************************************************************/
void CControl::UnInit()
{
	//等待线程退出
	SetUninitFlag(true);
	SetWaitEvent();
	if (m_tidProcMsg != 0)
	{
		pthread_join(m_tidProcMsg, NULL);
	}

	//反初始化其他Control
	GetDownloadControlInstance()->UnInit();
	GetConnectControlInstance()->UnInit();

	return;
}

int CControl::Run()
{
	if (m_bRuning)
	{
		return -1;
	}

	m_bRuning = true;
	//起来的时候通知一下phone
	GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_BOOTUP, 0, 0);
	TimerLoop();
	m_bRuning = false;
	return 0;
}

//处理消息
int CControl::ProcessMsg()
{
	WaitForEvent();
	Lock();
	MESSAGE *tmpNode = NULL;

	while(m_msgHeader != NULL)
	{
		tmpNode = (MESSAGE *)m_msgHeader;
		m_msgHeader = ((MESSAGE *)m_msgHeader)->next;
		Unlock();
		OnMessage(tmpNode->id, tmpNode->wParam, tmpNode->lParam, tmpNode->lpData);
		Lock();
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}
		delete(tmpNode);
	}

	m_msgHeader = NULL;

	ResetWaitEvent();

	Unlock();

	return 0;
}


//上锁消息缓冲区
void CControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁消息缓冲区
void CControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}

//设置事件
void CControl::SetWaitEvent()
{
	((CWaitEvent *)m_wait)->Set();
}

//清除事件
void CControl::ResetWaitEvent()
{
	((CWaitEvent *)m_wait)->Reset();
}

//等待事件触发
void CControl::WaitForEvent()
{
	((CWaitEvent *)m_wait)->Wait();
}


void CControl::SetUninitFlag(bool bUnInit)
{
	m_bUnInit = bUnInit;
}

/*@function
*******************************************************************
功  能:  解析 Config 文件.

参  数:  pstrConfigFile : Config 文件路径

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
map<string, map<string, string>> CControl::ParseConfigFile(const char* pstrConfigFile)
{
	map<string, map<string, string>> mapConfig;

	fstream infile;
	infile.open(pstrConfigFile, ios::in);

	int nLineIndex = 0;
	string strLine = "";
	while (getline(infile, strLine, '\n'))
	{
		if (nLineIndex == 0)
		{
			strLine = TrimBom(strLine);
		}

		// 1. 先将“Config.Account1.GENERAL.Enable=1”拆解成“Config.Account1.GENERAL.Enable”和“1”
		string strValue = "";
		string strKey = strLine;
		int nPos = strLine.find("=");
		if (nPos != string::npos)
		{
			strKey = strLine.substr(0, nPos);
			strValue = strLine.substr(nPos + 1);
		}

		// 2. 从 key 中拆出 section
		string strSection = "";
		static string strRemove = "Config.";
		strKey = strKey.substr(strRemove.length());
		int nPosDot = strKey.find(".");
		if (nPosDot != -1)
		{
			strSection = strKey.substr(0, nPosDot);
			strKey = strKey.substr(nPosDot + 1);
		}

		// 3. 添加到 map 中
		mapConfig[strSection][strKey] = strValue;

		// 4. 行索引加1
		++nLineIndex;
	}

	return mapConfig;
}

/*@function
*******************************************************************
功  能:  去除BOM头.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
string CControl::TrimBom(const string& strLine)
{
	const char* pstrLine = strLine.c_str();
	if (pstrLine[0] == '\0')
	{
		return strLine;
	}

	if (pstrLine[0] == 0xEF &&
		pstrLine[1] == 0xBB &&
		pstrLine[2] == 0xBF)
	{
		pstrLine += 3;
	}
	else if (pstrLine[0] == 0xFE &&
		pstrLine[1] == 0xFF)
	{
		pstrLine += 2;
	}
	else if (pstrLine[0] == 0xFF &&
		pstrLine[1] == 0xFE)
	{
		pstrLine += 2;
	}

	string strNoBom = pstrLine;
	return strNoBom;
}

//增加一个新的消息
int CControl::AddMsg(UINT id, UINT wParam, UINT lParam, void *lpData, int nDataLen)
{
	if (IsUnInit())
	{
		return -1;
	}

	Lock();
	MESSAGE *curNode = NULL;
	MESSAGE *newNode = new MESSAGE();
	if(NULL == newNode)
	{
		Unlock();
		return -1;
	}

	memset(newNode, 0, sizeof(MESSAGE));

	newNode->id = id;
	newNode->wParam = wParam;
	newNode->lParam = lParam;
	if((lpData != NULL) && (nDataLen > 0))
	{
		newNode->lpData = new char[nDataLen];
		memcpy(newNode->lpData, lpData, nDataLen);
	}

	if(m_msgHeader == NULL)
	{
		m_msgHeader = newNode;
	}
	else
	{
		curNode = (MESSAGE *)m_msgHeader;
		while((curNode != NULL) && (curNode->next != NULL))
		{
			curNode = curNode->next;
		}
		curNode->next = newNode;
	}
	SetWaitEvent();

	Unlock();

	return 0;
}


//删除所有消息
int CControl::DelAllMsg()
{
	Lock();

	MESSAGE *curNode = NULL;
	MESSAGE *tmpNode = NULL;

	curNode = (MESSAGE *)m_msgHeader;

	while(curNode != NULL)
	{
		tmpNode = curNode;
		curNode = curNode->next;
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}

		delete tmpNode;
	}

	m_msgHeader = NULL;

	Unlock();

	return 0;
}

//消息处理句柄
int CControl::OnMessage(UINT msg, UINT wParam, UINT lParam, void *lpData)
{
	int nMsgType = msg & MSG_TYPE_MASK;
	switch(nMsgType)
	{
	case DCLIENT_MSG_TIMER:
		{
			OnTimer(wParam);
		}
		break;
	case DCLIENT_MSG_MULTICAST:
		{		
			SOCKET_MSG *pRecvMsg = (SOCKET_MSG *)lpData;
			OnSocketMsg(pRecvMsg);
			break;
		}
	case DCLIENT_MSG_TCP:
		{
			SOCKET_MSG *pRecvMsg = (SOCKET_MSG *)lpData;
			OnSocketMsg(pRecvMsg);
		}
		break;
	case DCLIENT_MSG_CTRL:
		{
			OnCtrl(msg, wParam, lParam, lpData);
		}
		break;
	default:
		break;
	}

	return 0;
}

//控制消息处理句柄
int CControl::OnCtrl(UINT msg, UINT wParam, UINT lParam, void *lpData)
{
	switch(msg)
	{
	case MSG_CTRL_FILE_HANDLE_MD5_ACK:
		{
			switch(lParam)
			{
			case ACCESS_CONTROL_META_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetACMetaMD5(((DCLIENT_UPDATE_MD5 *)lpData)->szMD5);
					}
				}
				break;
			case ACCESS_CONTROL_INFO_ACK://业务需要，暂时不需要对ACInfo文件进行MD5的写入
				{
				#if 0
					if(lpData)
					{
						GetSettingHandleInstance()->SetACInfoMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				#endif
				}
				break;
			case SCHEDULE_INFO_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetScheduleMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case CLEAR_FINGER_DATA_ACK:
				{
					GetSettingHandleInstance()->SetFPMD5("");
				}
				break;
			case CLEAR_FACE_DATA_ACK:
				{
					if(wParam == 1)
					{
						GetSettingHandleInstance()->SetDownLoadFacePicMD5("");
					}
					else
					{
						GetSettingHandleInstance()->SetFaceIDMD5("");
					}
				}
				break;
			case RECV_AD_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetAdModuleMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_PRIVATEKEY_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetPrivatekeyMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}					
				}
				break;
			case RECV_RFKEY_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetRfidMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_ADDRESS_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetAddrMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_COMMUNITY_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetCommunityPhoneBookMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case DEVICE_LIST_INFO_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetContactMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_FACEID_XML_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetFaceIDMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_DOWNLOAD_FACEPIC_XML_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetDownLoadFacePicMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case SYNC_FACE_PIC_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetFaceSyncPicMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			case RECV_FP_TGZ_ACK:
				{
					if(lpData)
					{
						GetSettingHandleInstance()->SetFPMD5(((DCLIENT_UPDATE_MD5*)lpData)->szMD5);
					}
				}
				break;
			default:
				break;
			}
		}
		break;
	case MSG_CTRL_REBOOT_DCLIENT:
		{
			rl_log_info("process MSG_CTRL_REBOOT_DCLIENT");
			OnRebootDclient();
		}
		break;
	case MSG_CTRL_ETHERNET_CHANGE:
		{	
			rl_log_info("process MSG_CTRL_ETHERNET_CHANGE");
			rl_log_err("%s: ethernet change, DCLIENT will restart.", __FUNCTION__);
			exit(0);
		}
		break;
	case MSG_CTRL_MODIFY_DEVICE_LOCATION:
		{
			rl_log_info("process MSG_CTRL_REQUEST_KIT_DEVICES");
			GetDeviceControlInstance()->OnModifyDeviceLocation((KIT_DEVICE_BASE_INFO*)lpData);
		}
		break;
	case MSG_CTRL_REQUEST_KIT_DEVICES:
		{
			rl_log_info("process MSG_CTRL_REQUEST_KIT_DEVICES");
			GetDeviceControlInstance()->OnRequestKitDevice();
		}
		break;
	case MSG_CTRL_REPORT_KIT_DEVICES:
		{
			rl_log_info("process MSG_CTRL_REPORT_KIT_DEVICES");
			INT nAddMode = lParam;
			GetDeviceControlInstance()->OnReportKitDevice((DCLIENT_REPORT_KIT_DEVICE_LIST*)lpData, nAddMode);	
		}
		break;
	case MSG_CTRL_DOWNLOAD_BACKUP_CONFIG_RECOVERY_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_BACKUP_CONFIG_RECOVERY_DONE");
			DCLIENT_BACKUP_CONFIG_RECOVERY backupConfigRecovery;
			memset(&backupConfigRecovery, 0, sizeof(DCLIENT_BACKUP_CONFIG_RECOVERY));
			rl_strcpy_s(backupConfigRecovery.szFileName, sizeof(backupConfigRecovery.szFileName), (char*)lpData);		
			//ipc_send(IPC_ID_AUTOP, MSG_D2A_BACKUP_CONFIG_RECOVERY, 0, 0, &backupConfigRecovery, sizeof(DCLIENT_BACKUP_CONFIG_RECOVERY));
			GetDeviceControlInstance()->OnBackUpConfigRecovery(backupConfigRecovery);
		}
		break;
	case MSG_CTRL_BACKUP_CONFIG_ACK:
		{
			rl_log_info("process MSG_CTRL_BACKUP_CONFIG_ACK");
			GetDeviceControlInstance()->OnBackupConfigACK((DCLIENT_BACKUP_CONFIG_ACK *)lpData);	
		}
		break;
	case MSG_CTRL_REPORT_CALLLOG:
		{
			rl_log_info("process MSG_CTRL_REPORT_CALLLOG");
			GetDeviceControlInstance()->OnReportCallLog((DCLIENT_REPORT_CALLLOG *)lpData);	
		}
		break;
	case MSG_CTRL_REQUEST_END_USER_REG:
		{
			rl_log_info("process MSG_CTRL_REQUEST_END_USER_REG");
			GetDeviceControlInstance()->OnRequestEndUserReg();	
		}
		break;
	case MSG_CTRL_REQUEST_RTSP_MONITOR:
		{
			rl_log_info("process MSG_CTRL_REQUEST_RTSP_MONITOR");
			GetDeviceControlInstance()->OnRequestRtspMonitor((DCLIENT_REQUEST_RTSP_MONITOR *)lpData);	
		}
		break;
	case MSG_CTRL_RTSP_MONITOR_STOP:
		{
			rl_log_info("process MSG_CTRL_RTSP_MONITOR_STOP");
			GetDeviceControlInstance()->OnRtspMonitorStop((DCLIENT_RTSP_MONITOR_STOP *)lpData);	
		}
		break;
	case MSG_CTRL_FLOW_OUT_OF_LIMIT:
		{
			rl_log_info("process MSG_CTRL_FLOW_OUT_OF_LIMIT");
			GetDeviceControlInstance()->OnFlowOutOfLimit((DCLIENT_FLOW_OUT_OF_LIMIT *)lpData);	
		}
		break;
	case MSG_CTRL_REPORT_RELAY_STATUS:
		{
			rl_log_info("process MSG_CTRL_REPORT_RELAY_STATUS");
			GetDeviceControlInstance()->OnReportRelayStatus((DCLIENT_REPORT_RELAY_STATUS *)lpData);	
		}
		break;
	case MSG_CTRL_SEND_WAKE_WORD:
		{
			rl_log_info("process MSG_CTRL_SEND_WAKE_WORD");
			GetD2ChannelControlInstance()->SendWakeWord((DCLIENT_SEND_WAKE_WORD *)lpData);
		}
		break;
	case MSG_CTRL_SYNC_ACTIVITY:
		{
			rl_log_info("process MSG_CTRL_SYNC_ACTIVITY");
			GetDeviceControlInstance()->SendSyncActivityMsg((DCLIENT_SYNC_ACTIVITY *)lpData);	
		}
		break;
	case MSG_CTRL_UP_FINGERPRINT_WAITTING:
		{
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, MSG_TO_DEVICE_REQUEST_FINGER_PRINT, 0, 0, "Waitting") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_CTRL_UP_PERSONEL_DATA_WAITTING:
		{
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, MSG_TO_DEVICE_REQUEST_PERSONEL_DATA, 0, 0, "Waitting") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_CTRL_SEND_DELIVERY_MSG:
		{
			rl_log_info("process MSG_CTRL_SEND_DELIVERY_MSG");
			GetDeviceControlInstance()->OnSendDeliveryMsg((DCLIENT_SEND_DELIVERY *)lpData);	
		}
		break;
	case MSG_CTRL_REQUEST_ACINFO:
		{
			rl_log_info("process MSG_CTRL_REQUEST_ACINFO");
			GetDeviceControlInstance()->OnRequestACInfo((DCLIENT_REQUEST_ACINFO *)lpData);			
		}
		break;
	case MSG_CTRL_DOWNLOAD_ACMETA_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_ACMETA_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetACMetaMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_ACCESS_CONTROL_META, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_ACINFO_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_ACINFO_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetACInfoMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_ACCESS_CONTROL_INFO, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_SCHEDULE_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_SCHEDULE_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_SCHEDULE_INFO, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_TZ_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_TZ_DONE");
			ipc_send(IPC_ID_PHONE, MSG_D2P_UPDATE_TZ, 0, 0, (void *)lpData, rl_strlen((char *)lpData) + 1);
		}
		break;
	case MSG_CTRL_MAINTENANCE_ALARM_REPORT:
		{
			rl_log_info("process MSG_CTRL_MAINTENANCE_ALARM_REPORT");
			GetD2ChannelControlInstance()->D2MaintenanceAlarmHttpRequest((DCLIENT_MAINTENANCE_ALARM_REPORT *) lpData);
		}
		break;
	case MSG_CTRL_REQUEST_OPENDOOR:
		{
			rl_log_info("process MSG_CTRL_REQUEST_OPENDOOR");
			//1 为Security Relay,0为Normal Relay
			GetDeviceControlInstance()->RequestOpenDoor((DCLIENT_REQUEST_OPENDOOR *) lpData, wParam);
		}
		break;
	case MSG_CTRL_DOWNLOAD_PRIVATEKEY_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_PRIVATEKEY_DONE");

			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetPrivatekeyMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_PRIVATEKEY, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			
		}
		break;
	case MSG_CTRL_DOWNLOAD_RFID_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_RFID_DONE");

			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetRfidMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_RFKEY, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_ADDRESS_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_ADDRESS_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetAddrMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_ADDRESS, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			
		}
		break;
	case MSG_CTRL_DOWNLOAD_FIRMWARE_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_FIRMWARE_DONE");
		}
		break;
	case MSG_CTRL_DOWNLOAD_AD_DONE:
		{
	#if RL_SUPPORT_SDMC_AD	
			rl_log_info("process MSG_CTRL_DOWNLOAD_AD_DONE");
	#endif
		}
		break;
	case MSG_CTRL_DOWNLOAD_CONFIG_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_CONFIG_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			if(wParam == 1)//为1表示000000000001.cfg，该文件名称表示清除设备的一些配置
			{
				GetD2ChannelControlInstance()->SetClearCfgFlag(TRUE);
			}
			else
			{
				GetD2ChannelControlInstance()->SetClearCfgFlag(FALSE);
			}

			map<string, map<string, string>> mapConfig = ParseConfigFile((char*)lpData);
			for (map<string, map<string, string>>::iterator itSection = mapConfig.begin(); itSection != mapConfig.end(); ++itSection)
			{
				string strSection = itSection->first;
				map<string, string>& mapKeyValue = itSection->second;
				for (map<string, string>::iterator itKeyValue = mapKeyValue.begin(); itKeyValue != mapKeyValue.end(); ++itKeyValue)
				{
					string strKey = itKeyValue->first;
					string strValue = itKeyValue->second;

					if( GetConnectControlInstance()->GetConnectMode() == TCP_CONNECT_ORI_FROM_CLOUD &&
						strSection == CFG_ID_ACCOUNT_01 && strKey == ACCOUNT_KEY_SIP_TRANSTYPE)
					{
						UINT nPBXSrvPort = GetConnectControlInstance()->GetPBXSrvPort();
						if(rl_atoi(strValue.c_str()) == 2)//if the TransType is TLS,set the port as port value+1
						{
							GetDClientInstance()->CfgSetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, nPBXSrvPort+1);
							rl_log_info("cfgid=%s,keyid=%s,value=%d",CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, nPBXSrvPort+1);
						}
						else
						{
							GetDClientInstance()->CfgSetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, nPBXSrvPort);
							rl_log_info("cfgid=%s,keyid=%s,value=%d",CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, nPBXSrvPort);
						}

						continue;
					}

					GetDClientInstance()->CfgSetString(strSection.c_str(), strKey.c_str(), strValue.c_str());
				}
			}

			GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_CONFIG_CHANGED, 0, 0);
			
			//删除掉cfg文件
			rl_log_info("delete %s ",(char *)lpData);
			unlink((char *)lpData);
			GetSettingHandleInstance()->SetConfigMD5(szCurrentMd5);

			//通知Sip账户改变
			DCLIENT_SIP_INFO infoSip;
			GetDClientInstance()->CfgGetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_USERNAME, infoSip.szUserName, sizeof(infoSip.szUserName), "");
			GetDClientInstance()->CfgGetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PASSWORD, infoSip.szPassword, sizeof(infoSip.szPassword), "");
			GetDClientInstance()->CfgGetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_SERVER, infoSip.szServer, sizeof(infoSip.szServer), "");
			infoSip.nServerPort = GetDClientInstance()->CfgGetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, 0);
			GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_SIP_INFO_CHANGED, 0, 0, &infoSip, sizeof(DCLIENT_SIP_INFO));

			//通知设备名改变
			char szNewDeviceLocation[256] = {0};
			GetSettingHandleInstance()->GetDeviceLocation(szNewDeviceLocation, sizeof(szNewDeviceLocation));
			GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_DEVICE_LOCATION_CHANGED, 0, 0, szNewDeviceLocation);
		}
		break;
#if RL_SUPPORT_SDMC_AD		
	case MSG_CTRL_DOWNLOAD_ADMODULE_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_ADMODULE_DONE");

			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetAdModuleMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			
		}
		break;
#endif	
#if RL_SUPPORT_SDMC_REMOTE_PHONEBOOK		
//add by larry 2017/03/21
	case MSG_CTRL_DOWNLOAD_COMMUNITY_PHONEBOOK_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_COMMUNITY_PHONEBOOK_DONE");

			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetCommunityPhoneBookMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);

			//发送消息通知phone远程联系人发送了改变
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_COMMUNITY, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
#else
			cfg_parse_csv_to_xml((char *)lpData, CFG_COMMUNITY_TMP_PATH);
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), CFG_COMMUNITY_TMP_PATH);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			//发送消息通知phone远程联系人发送了改变
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_COMMUNITY, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
#endif
		}
		break;
#endif	
	case MSG_CTRL_SEND_ALARM:
		{
			DCLIENT_ALARM_MSG *pAlarmMsg = (DCLIENT_ALARM_MSG *)lpData;
			if(pAlarmMsg != NULL)
			{
				//先判断是否有云平台或者SDMC，如果没有，则先发DISCOVER获取ip
				if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
				{
					if(IsValidIPAddr(pAlarmMsg->to_name))
					{
						GetAlarmControlInstance()->SendAlarmMsg(pAlarmMsg, TRANSPORT_TYPE_UDP);
					}
					else
					{
						GetDiscoverControlInstance()->SendDiscoverMsg(pAlarmMsg->to, DISCOVER_FLAG_DCLIENT, pAlarmMsg->nSequenceNum);
						GetAlarmControlInstance()->AddAlarmMsgToList(pAlarmMsg);
					}
				}
				else
				{
					//TODO 发送ALARM消息给SDMC
					int nRet = GetAlarmControlInstance()->SendAlarmMsg(pAlarmMsg);
					if (nRet < 0)//TCP发送失败，改为UDP直接P2P发送
					{
						if(IsValidIPAddr(pAlarmMsg->to_name))
						{
							GetAlarmControlInstance()->SendAlarmMsg(pAlarmMsg, TRANSPORT_TYPE_UDP);
						}
					}
				}
			}
		}
		break;
	case MSG_CTRL_SEND_ALARM_DEAL:
		{
			DCLIENT_ALARM_DEAL_INFO *pAlarmDeal = (DCLIENT_ALARM_DEAL_INFO *)lpData;
			if(pAlarmDeal != NULL)
			{			
				GetAlarmControlInstance()->SendAlarmDealMsg(pAlarmDeal);
			}
		}
		break;
	case MSG_CTRL_SEND_MSG:
		{
			DCLIENT_TEXT_MSG *pTextMsg = (DCLIENT_TEXT_MSG *)lpData;		
			if(pTextMsg != NULL)
			{
				//先判断是否有云平台或者SDMC，如果没有，则先发DISCOVER获取ip
				if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
				{
					if(IsValidIPAddr(pTextMsg->to_name))
					{
						GetTextMessageControlInstance()->SendTextMsg(pTextMsg, TRANSPORT_TYPE_UDP);
					}
					else
					{
						GetDiscoverControlInstance()->SendDiscoverMsg(pTextMsg->to, DISCOVER_FLAG_DCLIENT, pTextMsg->nSequenceNum);
						GetTextMessageControlInstance()->AddTextMsgToList(pTextMsg);
					}
				}
				else
				{
					//TODO 发送TEXT消息给SDMC
					GetTextMessageControlInstance()->SendTextMsg(pTextMsg);
				}
			}
		}
		break;
	case MSG_CTRL_SEND_ACCESS_INFO:
		{
			DCLIENT_ACCESS_INFO *pAccessInfo = (DCLIENT_ACCESS_INFO *)lpData;		
			if(pAccessInfo != NULL)
			{
				//TODO 发送accesscontrol消息给SDMC
				GetAccessInfoControlInstance()->SendAccessInfo(pAccessInfo);
			}
		}
		break;
	case MSG_CTRL_CREATE_BIND_CODE:
		{
			GetBindCodeControlInstance()->SendGetBindCodeRequest((int)lParam);
		}
		break;
	case MSG_CTRL_DELETE_BIND_CODE:
		{
			GetBindCodeControlInstance()->SendUnBindCodeRequest((int)lParam, (char*)lpData);
		}
		break;
	case MSG_CTRL_GET_BINDCODE_LIST:
		{
			GetBindCodeControlInstance()->SendGetBindCodeListRequest((int)lParam);
		}
		break;
	case MSG_CTRL_SEND_DISVOCER:
		{
			GetDiscoverControlInstance()->SendDiscoverMsg((DCLIENT_DISCOVER_SEND *)lpData);
		}
		break;
#if RL_SUPPORT_ARMING
	case MSG_CTRL_REPORT_ARMING:
		{
			DCLIENT_REPORT_ARMING *pReportArmingMsg = (DCLIENT_REPORT_ARMING *)lpData;
			if(pReportArmingMsg != NULL)
			{
				if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
				{
					
					if(rl_str_isempty(pReportArmingMsg->to_ip))
					{
						GetDiscoverControlInstance()->SendDiscoverMsg(pReportArmingMsg->to, DISCOVER_FLAG_DCLIENT, pReportArmingMsg->nSequenceNum);
						GetAlarmControlInstance()->AddReportArmingMsgToList(pReportArmingMsg);
					}
					else
					{
						GetAlarmControlInstance()->ReportArming((DCLIENT_REPORT_ARMING *)lpData, TRANSPORT_TYPE_UDP);
					}
				}
				else
				{
					int nRet = GetAlarmControlInstance()->ReportArming((DCLIENT_REPORT_ARMING *)lpData, TRANSPORT_TYPE_TCP);
					if (nRet < 0)//TCP发送失败，改为UDP直接P2P发送
					{
						if(IsValidIPAddr(pReportArmingMsg->to_ip))
						{
							GetAlarmControlInstance()->ReportArming(pReportArmingMsg, TRANSPORT_TYPE_UDP);
						}
					}
				}
			}
		}
		break;
#endif
#if RL_SUPPORT_ARMING_P2P
	case MSG_CRTL_REQUEST_ARMING:
		{
			DCLIENT_REQUEST_ARMING *pRequestArmingMsg = (DCLIENT_REQUEST_ARMING *)lpData;		
			if(pRequestArmingMsg != NULL)
			{
				//先判断是否有云平台或者SDMC，如果没有，则先发DISCOVER获取ip
				if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
				{
					if(IsValidIPAddr(pRequestArmingMsg->to_ip))
					{
						GetAlarmControlInstance()->SendRequestArming(pRequestArmingMsg, TRANSPORT_TYPE_UDP);
					}
					else
					{
						GetDiscoverControlInstance()->SendDiscoverMsg(pRequestArmingMsg->to, DISCOVER_FLAG_DCLIENT, pRequestArmingMsg->nSequenceNum);
						GetAlarmControlInstance()->AddRequestArmingMsgToList(pRequestArmingMsg);
					}
				}
				else
				{
					int nRet = GetAlarmControlInstance()->SendRequestArming(pRequestArmingMsg, TRANSPORT_TYPE_TCP);
					if (nRet < 0)//TCP发送失败，改为UDP直接P2P发送
					{
						if(IsValidIPAddr(pRequestArmingMsg->to_ip))
						{
							GetAlarmControlInstance()->SendRequestArming(pRequestArmingMsg, TRANSPORT_TYPE_UDP);
						}
					}
				}
			}
		}
		break;
#endif
#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	case MSG_CTRL_PUSH_NOANSWER_FWD_NUMBER:
		{
			GetSettingControlInstance()->SendPushForwardNumber((DCLIENT_PUSH_NOANSWER_FWD_NUMBER *)lpData);
		}
		break;
#endif
#if RL_GLOBAL_SUPPORT_TMP_KEY		
	case MSG_CTRL_SEND_CHECK_TMP_KEY:
		{
			DCLIENT_CHECK_TMP_KEY *pCheckTmpKey = (DCLIENT_CHECK_TMP_KEY *)lpData;		
			if((pCheckTmpKey != NULL) && (GetConnectControlInstance()->GetTcpSocketFd() > 0))
			{
				//TODO 发送CheckTmpKey消息给SDMC
				GetDoorControlInstance()->SendCheckTmpKey(pCheckTmpKey);
			}
			else
			{
				//失败直接应答给PHONE
				DCLIENT_CHECK_TMP_KEY tTmpKey;
				memset(&tTmpKey, 0, sizeof(DCLIENT_CHECK_TMP_KEY));
				if(pCheckTmpKey != NULL)
				{
					rl_strcpy_s(tTmpKey.szMsgSeqCode, sizeof(tTmpKey.szMsgSeqCode), pCheckTmpKey->szMsgSeqCode);
				}

				tTmpKey.nResult = 1;
				ipc_send(IPC_ID_PHONE, MSG_D2P_CHECK_TMP_KEY_ACK, 0, 0, &tTmpKey, sizeof(tTmpKey));
			}
		}
		break;
#endif	
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX)
	case MSG_CTRL_DCLIENT_NETWORK_CHANGED:
		{
			GetMacProtoControlInstance()->OnNetworkChange();
		}
		break;
#endif
#if RL_SUPPORT_REPORT_ACTIVITY		
	case MSG_CTRL_MOTION_ALERT:
		{
			GetActivityControlInstance()->SendMotionAlert((DCLIENT_MOTION_ALERT *)lpData);
		}
		break;
	case MSG_CTRL_REPORT_ACTIVITY:
		{
			GetActivityControlInstance()->SendReportActivity((DCLIENT_REPORT_ACTIVITY *)lpData);
		}
		break;
#endif		
#if RL_SUPPORT_CLOUD_DEV_INFO
	case MSG_CTRL_DOWNLOAD_AKCS_CONTACT_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_AKCS_CONTACT_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetContactMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);

			char cmd[512] = {0};
			rl_sprintf_s(cmd, sizeof(cmd), "chmod 777 %s", lpData);
			rl_system_100ms_ex(cmd, 2, 1);
			GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_CONTACT_CHANGED, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));

			GetSettingHandleInstance()->SetContactMD5(szCurrentMd5);
		}
		break;
#endif
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC		
	case MSG_CTRL_DOWNLOAD_FACEID_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_FACEID_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetFaceIDMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_FACEID_XML, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_FACEDATA_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_FACEDATA_DONE");
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_FACEDATA, 0, 0, (void *)lpData, rl_strlen((char *)lpData) + 1);
		}
		break;
#endif
	case MSG_CTRL_CHECK_DTMF:
		{
			GetDeviceControlInstance()->CheckDtmf((DCLIENT_CHECK_DTMF *)lpData);
		}
		break;
	case MSG_CTRL_REPORT_DEVICE_CODE:
		{
			rl_log_err("%s: MSG_CTRL_REPORT_DEVICE_CODE.", __FUNCTION__);
			GetDeviceControlInstance()->ReportDeviceCode((DCLIENT_REPORT_DEVICE_CODE *) lpData);
		}
		break;
#if RL_SUPPORT_DEVICE_HTTP_REGISTER
	case MSG_CTRL_PERSONAL_REGISTER:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_PERSONAL_REGISTER, 0, 0, (DCLIENT_PERSONAL_REGISTER*) lpData, sizeof(DCLIENT_PERSONAL_REGISTER));
		}
		break;
	case MSG_CTRL_PERSONAL_LOGIN:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_PERSONAL_LOGIN, 0, 0, (DCLIENT_PERSONAL_LOGIN*) lpData, sizeof(DCLIENT_PERSONAL_LOGIN));
		}
		break;
	case MSG_CTRL_ADD_SLAVE_ACCOUNT:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_ADD_SLAVE_ACCOUNT, 0, 0, (DCLIENT_ADD_SLAVE_ACCOUNT*) lpData, sizeof(DCLIENT_ADD_SLAVE_ACCOUNT));
		}
		break;
	case MSG_CTRL_GET_SLAVE_ACCOUNT_LIST:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_GET_SLAVE_ACCOUNTLIST, 0, 0, (DCLIENT_GET_SLAVE_ACCOUNT_LIST*) lpData, sizeof(DCLIENT_GET_SLAVE_ACCOUNT_LIST));
		}
		break;
	case MSG_CTRL_DELETE_SLAVE_ACCOUNT:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_DEL_SLAVE_ACCOUNT, 0, 0, (DCLIENT_DEL_SLAVE_ACCOUNT*) lpData, sizeof(DCLIENT_DEL_SLAVE_ACCOUNT));
		}
		break;
	case MSG_CTRL_BIND_DEVICE_BY_DEVICECODE:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_BIND_DEVICE_BY_DEVICE_CODE, 0, 0, (DCLIENT_BIND_DEVICE*) lpData, sizeof(DCLIENT_BIND_DEVICE));
		}
		break;
	case MSG_CTRL_EMAIL_EXIST:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_EMAIL_EXIT, 0, 0, (DCLIENT_EMAIL_EXIST*) lpData, sizeof(DCLIENT_EMAIL_EXIST));
		}
		break;
#endif
#if RL_SUPPORT_CLOUD_DEV_INFO
	case MSG_CTRL_REPORT_NETWORK_INFO:
		{
			GetDeviceControlInstance()->ReportNetworkInfo((DCLIENT_REPORT_NETWORK_INFO*) lpData);
		}
		break;
#endif
	case MSG_CTRL_AUTO_DISCOVER:
		{
			GetDiscoverControlInstance()->OnRecvAutoDiscover();
		}
		break;
#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR		
	case MSG_CTRL_GET_MOTION_AND_ROBINCALL_INFO:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_GET_MOTION_ROBCALL_CFG, 0, 0, NULL, 0);
		}
		break;
	case MSG_CTRL_SET_PERSONAL_ACCOUNT_MOTION:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_SET_PER_ACC_MOTION, 0, 0, (DCLIENT_SET_PERSONAL_ACCOUNT_MOTION*) lpData, sizeof(DCLIENT_SET_PERSONAL_ACCOUNT_MOTION));
		}
		break;
	case MSG_CTRL_SET_PERSONAL_ACCOUNT_ROBINCALL:
		{
			GetDownloadControlInstance()->AddMsg(RFO_REQUEST_MSG_SET_PER_ACC_ROBINCALL, 0, 0, (DCLIENT_MOTION_AND_ROBINCALL*) lpData, sizeof(DCLIENT_MOTION_AND_ROBINCALL));
		}
		break;
#endif		
#if RL_SUPPORT_DTMF_SET
	case MSG_CTRL_SET_DTMF:
		{
			GetSettingControlInstance()->SendDtmfSetMsg((DCLIENT_SET_DTMF*)lpData);
		}
		break;
#endif
#if	RL_SUPPORT_DOWNUPLOAD_FACE_PIC
	case MSG_CTRL_DOWNLOAD_D_FACEPIC_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_D_FACEPIC_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetDownLoadFacePicMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_DOWNLOAD_FACEPIC_XML, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;

#endif
	case MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_DONE");
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			char szCurrentMd5[MD5_SIZE] = {0};
			GetSettingHandleInstance()->GetFaceSyncMD5(szCurrentMd5, sizeof(szCurrentMd5));
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_SYNC_FACE_PIC, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
	case MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_AES_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_AES_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetFaceSyncPicMD5(szCurrentMd5);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_SYNC_FACE_PIC, 1, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	case MSG_CTRL_REQUEST_CONFIG:
		{
			GetDeviceControlInstance()->RequestConfigFromDevice((DCLIENT_REQUEST_CONFIG *)lpData);
		}
		break;
#endif
#if RL_SUPPORT_CONTROL4
	case MSG_CTRL_CONTROL4_RESPONSE_COMMON:
		{
			GetControlFourControlInstance()->OnResponsCommon((DCLIENT_CONTROL4_RESPONSE_COMMON*)lpData);
		}
		break;
#endif
	case MSG_CTRL_REPORT_CALL_CAPTURE:
		{
			GetDeviceControlInstance()->OnReportCallCapture((DCLIENT_REPORT_CALL_CAPTURE*)lpData);
		}
		break;
	case MSG_CTRL_REPORT_TRIGGER:
		{
			GetDeviceControlInstance()->OnReportTrigger((DCLIENT_REPORT_TRIGGER*)lpData);
		}
		break;
	case MSG_CTRL_MANAGE_BROADCAST_MSG:
		{
			GetDeviceControlInstance()->OnManageBroadcastMsg((DCLIENT_MANAGE_BROADCAST_MSG*)lpData);
		}
		break;
#if RL_SUPPORT_FP_DOWNLOAD
	case MSG_CTRL_DOWNLOAD_FP_DONE:
		{
			rl_log_info("process MSG_CTRL_DOWNLOAD_FP_DONE");
			char szCurrentMd5[MD5_SIZE] = {0};
			GetFileMD5((char *)lpData, szCurrentMd5);
			//GetSettingHandleInstance()->SetFPMD5(szCurrentMd5);
		#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			//解密文件
			FileAESDecrypt((char *)lpData, AES_ENCRYPT_KEY_V1, (char *)lpData);
		#endif
			DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
			memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
			rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)lpData);
			rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szCurrentMd5);
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_FP_TGZ, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
		}
		break;
#endif
	case MSG_CTRL_REPORT_HEALTH:
		{
			GetDeviceControlInstance()->OnReportHealth((DCLIENT_REPORT_HEALTH*)lpData);
		}
		break;
	case MSG_CTRL_RESPONSE_SENSOR_TRIGGER:
		{
			GetDeviceControlInstance()->OnResponseSensorTriggerMsg((DCLIENT_SENSOR_TRIGGER*)lpData);
		}
		break;
	case MSG_CTRL_UPLOAD_VIDEO_NOTIFY:
		{
			GetDeviceControlInstance()->OnUploadVideoNotifyMsg((DCLIENT_UPLOAD_VIDEO_NOTIFY*)lpData);
		}
		break;
	case MSG_CTRL_UPLOAD_CAPTURE_NOTIFY:
		{
			GetDeviceControlInstance()->OnUploadCaptureNotifyMsg((DCLIENT_UPLOAD_CAPTURE_NOTIFY*)lpData);
		}
		break;
	case MSG_CTRL_REQUEST_ALL_TRIGGER_STATUS:
		{
			GetDeviceControlInstance()->OnRequestAllTrigger((DCLIENT_ALL_TRIGGER_STATUS*)lpData);
		}
		break;
	case MSG_CTRL_REPORT_ALL_TRIGGER_STATUS:
		{
			GetDeviceControlInstance()->OnReportAllTrigger((DCLIENT_ALL_TRIGGER_STATUS*)lpData);
		}
		break;
#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
	case MSG_CTRL_SEND_REPORT_DOOR_STATUS:
		{
			GetDeviceControlInstance()->SendReportDoorStatus((DCLIENT_REPORT_DOORSTATUS*)lpData);
		}
		break;
#endif
#if RL_SUPPORT_SEND_REPORT_GAS
	case MSG_CTRL_SEND_REPORT_GAS:
		{
			GetDeviceControlInstance()->SendReportGas((DCLIENT_REPORT_GAS*)lpData);
		}
		break;
#endif
	case MSG_CTRL_SEND_REPORT_VISITOR_INFO:
		{
			GetDeviceControlInstance()->SendReportVisitorInfo((DCLIENT_REPORT_VISITOR_INFO*)lpData);
		}
		break;
	case MSG_CTRL_SEND_REPORT_VISITOR_AUTH_INFO:
		{
			GetDeviceControlInstance()->SendReportVisitorAuthInfo((DCLIENT_REPORT_VISITOR_AUTH_INFO*)lpData);
		}
		break;
	case MSG_CTRL_REQUEST_OSS_STS:
		{
			GetDeviceControlInstance()->SendRequestOssSts((DCLIENT_REQUEST_OSS_STS*)lpData);
		}
		break;
	case MSG_CTRL_REMOTE_CONTROL_OPENDOOR_RESPONSE:
		{
			GetDeviceControlInstance()->RemoteControlOpenDoorResponse((DCLIENT_REMOTE_CONTROL_OPENDOOR*)lpData);
		}
		break;
	default:
		break;
	}

	return 0;
}

int CControl::OnSocketMsg(SOCKET_MSG *pRecvMsg)
{
	if(pRecvMsg == NULL)
	{
		return -1;
	}
	//判断MAGIC

	//判断CRC

	//判断类型
	SOCKET_MSG_NORMAL *pNormalMsg = (SOCKET_MSG_NORMAL *)pRecvMsg->byData;
	INT nMsgID = pNormalMsg->nMsgID & SOCKET_MSG_ID_MASK;
	USHORT nCRC = pNormalMsg->nCrc;
	CHAR *pszRemoteAddr = pRecvMsg->szRemoteAddr;
	UINT nRemotePort = pRecvMsg->nPort;
	UINT nDataSize = DCLIENT_NTOHS(pNormalMsg->nDataSize);
	UINT nTransportType = pRecvMsg->nTransport;
	GetSettingHandleInstance()->GetCloudServerEnable();
	rl_log_info("%s: nMsgID = 0x%X", __FUNCTION__, nMsgID);
	switch(nMsgID)
	{
#if 0
	case MSG_TO_DEVICE_REPORT_KIT_DEVICES:
		{
			DCLIENT_REPORT_KIT_DEVICE_LIST kitDeviceList;
			memset(&kitDeviceList, 0, sizeof(DCLIENT_REPORT_KIT_DEVICE_LIST));
			if(GetMsgControlInstance()->ParseReportKitDevices(pNormalMsg, &kitDeviceList, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReportKitDevices failed.", __FUNCTION__);
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_KIT_DEVICES, 0, 0, &kitDeviceList, sizeof(DCLIENT_REPORT_KIT_DEVICE_LIST));
		}
		break;
	case MSG_TO_DEVICE_REQUEST_IS_KIT:
		{
			INT nKitFlag = 0;
			if(GetMsgControlInstance()->ParseRequestIsKit(pNormalMsg, nKitFlag, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestIsKit failed.", __FUNCTION__);
				return -1;
			}			
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_IS_KIT, 0, nKitFlag, NULL, 0);
		}
		break;
	case MSG_TO_DEVICE_BACKUP_CONFIG_RECOVERY:
		{
			SOCKET_MSG_BACKUP_CONFIG_RECOVERY backupConfigRecovery;
			memset(&backupConfigRecovery, 0, sizeof(SOCKET_MSG_BACKUP_CONFIG_RECOVERY));
			if(GetMsgControlInstance()->ParseBackupConfigRecovery(pNormalMsg, &backupConfigRecovery, nDataSize) < 0)
			{
				rl_log_err("%s: ParseBackupConfigRecovery failed.", __FUNCTION__);
				return -1;
			}
			DOWNLOAD_SERVER_INFO downloadInfo;
			memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
			rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), backupConfigRecovery.szURL);
			rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), backupConfigRecovery.szMD5);
			GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_BACKUP_CONFIG_RECOVERY, 0, 0, &downloadInfo, sizeof(downloadInfo));		
		}
		break;
	case MSG_TO_DEVICE_BACKUP_CONFIG:
		{
			DCLIENT_BACKUP_CONFIG backupConfig;
			memset(&backupConfig, 0, sizeof(DCLIENT_BACKUP_CONFIG));
			if(GetMsgControlInstance()->ParseBackupConfig(pNormalMsg, &backupConfig, nDataSize) < 0)
			{
				rl_log_err("%s: ParseBackupConfig failed.", __FUNCTION__);
				return -1;
			}
			//ipc_send(IPC_ID_PHONE, MSG_D2P_BACKUP_CONFIG, 0, 0, &backupConfig, sizeof(DCLIENT_BACKUP_CONFIG));
			GetDeviceControlInstance()->OnBackUpConfig(backupConfig);
			
		}
		break;
	case MSG_TO_DEVICE_REG_END_USER:
		{
			DCLIENT_REG_END_USER regEndUser;
			memset(&regEndUser, 0, sizeof(DCLIENT_REG_END_USER));
			if(GetMsgControlInstance()->ParseRegEndUser(pNormalMsg, &regEndUser, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRegEndUser failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REG_END_USER, 0, 0, &regEndUser, sizeof(DCLIENT_REG_END_USER));
			
		}
		break;
	case MSG_FROM_DEVICE_REQUEST_RTSP_MONITOR:
		{
			DCLIENT_REQUEST_RTSP_MONITOR rtspMonitor;
			memset(&rtspMonitor, 0, sizeof(DCLIENT_REQUEST_RTSP_MONITOR));
			if(GetMsgControlInstance()->ParseRequestRtspMonitor(pNormalMsg, &rtspMonitor, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestRtspMonitor failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_RTSP_MONITOR, 0, 0, &rtspMonitor, sizeof(DCLIENT_REQUEST_RTSP_MONITOR));
		}
		break;
	case MSG_FROM_DEVICE_RTSP_MONITOR_STOP:
		{
			DCLIENT_RTSP_MONITOR_STOP rtspMonitorStop;
			memset(&rtspMonitorStop, 0, sizeof(DCLIENT_RTSP_MONITOR_STOP));
			if(GetMsgControlInstance()->ParseRtspMonitorStop(pNormalMsg, &rtspMonitorStop, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRtspMonitorStop failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_RTSP_MONITOR_STOP, 0, 0, &rtspMonitorStop, sizeof(DCLIENT_RTSP_MONITOR_STOP));
		}
		break;
	case MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY:
		{
			DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE relayStatus;
			if(GetMsgControlInstance()->ParseRequestKeepRelayOpenClose(pNormalMsg, &relayStatus, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestKeepRelayOpen failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_KEEP_OPEN_CLOSE_RELAY, 0, DCLIENT_KEEP_RELAY_OPEN, &relayStatus, sizeof(DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE));
		}
		break;
	case MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY:
		{
			DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE relayStatus;
			if(GetMsgControlInstance()->ParseRequestKeepRelayOpenClose(pNormalMsg, &relayStatus, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestKeepRelayClose failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_KEEP_OPEN_CLOSE_RELAY, 0, DCLIENT_KEEP_RELAY_CLOSE, &relayStatus, sizeof(DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE));
		}
		break;
	case MSG_TO_DEVICE_NOTIFY_ATTENDANCE_SERVICE:
		{
			DCLIENT_NOTIFY_ATTENDANCE_SERVICE attendanceSrv;
			if(GetMsgControlInstance()->ParseNotifyAttendanceSrv(pNormalMsg, &attendanceSrv, nDataSize) < 0)
			{
				rl_log_err("%s: ParseNotifyAttendanceSrv failed.", __FUNCTION__);
				return -1;
			}
			CHAR szLastAttendanceSrv[IP_SIZE] = {0};
			GetSettingHandleInstance()->GetAttendanceSrv(szLastAttendanceSrv, sizeof(szLastAttendanceSrv));
			if(rl_strlen(szLastAttendanceSrv) == 0 && IsValidIPAddr(attendanceSrv.szIP))
			{
				ipc_send(IPC_ID_PHONE, MSG_D2P_NOTIFY_ATTENDANCE_START, 0, 0, NULL, 0);
			}
			if(rl_strlen(attendanceSrv.szIP) == 0)
			{
				ipc_send(IPC_ID_PHONE, MSG_D2P_NOTIFY_ATTENDANCE_STOP, 0, 0, NULL, 0);
			}
			GetSettingHandleInstance()->SetAttendanceSrv(attendanceSrv.szIP);
		}
		break;
	case MSG_TO_DEVICE_REQUEST_PERSONEL_DATA:
		{
			DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA personelData;
			if(GetMsgControlInstance()->ParseRequestPersonelData(pNormalMsg, &personelData, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestPersonelData failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnRequestPersonelData(&personelData);	
			//回复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, nMsgID, nCRC, 0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_TO_DEVICE_SYNC_PERSONEL_DATA:
		{
			DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA personelData;
			if(GetMsgControlInstance()->ParseSyncPersonelData(pNormalMsg, &personelData, nDataSize) < 0)
			{
				rl_log_err("%s: ParseSyncPersonelData failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnSyncPersonelData(&personelData);	
			//回复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, nMsgID, nCRC, 0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_TO_DEVICE_REQUEST_FINGER_PRINT:
		{
			DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT fingerPrint;
			if(GetMsgControlInstance()->ParseRequestFingerPrint(pNormalMsg, &fingerPrint, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestFingerPrint failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnRequestFingerPrint(&fingerPrint);	
			//回复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, nMsgID, nCRC, 0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_TO_DEVICE_SYNC_FINGER_PRINT:
		{
			DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT fingerPrint;
			if(GetMsgControlInstance()->ParseSyncFingerPrint(pNormalMsg, &fingerPrint, nDataSize) < 0)
			{
				rl_log_err("%s: ParseSyncFingerPrint failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnSyncFingerPrint(&fingerPrint);	
			//回复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, nMsgID, nCRC, 0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_TO_DEVICE_REGISTER_FACE:
		{
			DCLIENT_FACE_INFO faceInfo;
			if(GetMsgControlInstance()->ParseRegisterFace(pNormalMsg, &faceInfo, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRegisterFace failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnRegisterFace(&faceInfo);		
		}
		break;
	case MSG_TO_DEVICE_MODIFY_FACE:
		{
			DCLIENT_FACE_INFO faceInfo;
			if(GetMsgControlInstance()->ParseModifyFace(pNormalMsg, &faceInfo, nDataSize) < 0)
			{
				rl_log_err("%s: ParseModifyFace failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnModifyFace(&faceInfo);		
		}
		break;
	case MSG_TO_DEVICE_DELETE_FACE:
		{
			DCLIENT_FACE_INFO faceInfo;
			if(GetMsgControlInstance()->ParseDeleteFace(pNormalMsg, &faceInfo, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDeleteFace failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnDeleteFace(&faceInfo);		
		}
		break;
	case MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN:
		{
			SOCKET_MSG_GSFACE_HTTPAPI httpAPI;
			if(GetMsgControlInstance()->ParseGSFaceHttpApiLogin(pNormalMsg, &httpAPI, nDataSize) < 0)
			{
				rl_log_err("%s: ParseGSFaceHttpApiLogin failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnGSFaceHttpApiLogin(&httpAPI);
		}
		break;
	case MSG_TO_DEVICE_OPENDOOR_ACK:
		{
			DCLIENT_OPENDOOR_ACK openDoorACK;
			if(GetMsgControlInstance()->ParseOpenDoorACK(pNormalMsg, &openDoorACK, nDataSize) < 0)
			{
				rl_log_err("%s: ParseOpenDoorACK failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnOpenDoorACK(&openDoorACK);			
		}
		break;
	case MSG_TO_DEVICE_REMOTE_ACCESS_WEB:
		{
			DCLIENT_REMOTE_ACCESS_WEB remoteAccessWeb;
			if(GetMsgControlInstance()->ParseRemoteAccessWeb(pNormalMsg, &remoteAccessWeb, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRemoteAccessWebMsg failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnRemoteAccessWeb(&remoteAccessWeb);
		}
		break;
	case MSG_TO_DEVICE_REQUEST_CONNECTION:
		{			
			//CONNECTCONTROL未初始化或者连接模式为云不接受SDMC的REQUEST_CONNECTION
			if(!GetConnectControlInstance()->HasInit())
			{
				rl_log_warn("MSG_TO_DEVICE_REQUEST_CONNECTION: ignore before init.");
				return -1;
			}
			//当前连接着云
			if(GetConnectControlInstance()->IsConnectSucceed() && 
				(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_CLOUD))
			{
				rl_log_warn("MSG_TO_DEVICE_REQUEST_CONNECTION: ignore when cloud mode and connected.");
				return -1;
			}
			
			SOCKET_MSG_REQ_CONN reqConnMsg;
			if(GetMsgControlInstance()->ParseReqConnMsg(pNormalMsg, &reqConnMsg, nDataSize) < 0)
			{
				rl_log_err("MSG_TO_DEVICE_REQUEST_CONNECTION: parse failed.");
				return -1;
			}
			//当前处于云模式，且FactoryMode不为1
			if(reqConnMsg.nFactoryMode == 0 && (GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_CLOUD))
			{
				rl_log_warn("MSG_TO_DEVICE_REQUEST_CONNECTION: ignore when cloud mode and factory mode is 0.");
				return -1;
			}

			rl_log_info("MSG_TO_DEVICE_REQUEST_CONNECTION: set SDMC mode.");
			
			GetConnectControlInstance()->SetConnectMode(DOORSETTING_CONNECT_SERVER_MODE_SDMC);
			GetConnectControlInstance()->SetConnectOriFrom(TCP_CONNECT_ORI_FROM_SDMC);
			GetConnectControlInstance()->RestartConnection(reqConnMsg.szIPAddr, reqConnMsg.nPort, reqConnMsg.bForceConnect);
			GetConnectControlInstance()->SetHeartBeatExpire(reqConnMsg.nHeartBeatPeriod);
			GetConnectControlInstance()->SetHeartBeatFlag(TRUE);
			GetSettingHandleInstance()->SetSDMCServer(reqConnMsg.szIPAddr, reqConnMsg.nPort);
		}
		break;
#endif

	case MSG_TO_DEVICE_REQUEST_STATUS:
		{			
			SOCKET_MSG_REQ_STATUS reqStatusMsg;
			if(GetMsgControlInstance()->ParseReqStatusMsg(pNormalMsg, &reqStatusMsg, nDataSize) < 0)
			{
				return -1;
			}
			
			SOCKET_MSG socketMsg;
			if(GetMsgControlInstance()->BuildReportStatusMsg(&socketMsg) < 0)
			{
				return -1;
			}

			if(pRecvMsg->nTransport == TRANSPORT_TYPE_UDP)
			{
#if RL_SUPPORT_ETHERNET_TRANSPORT
				socketMsg.nPort = pRecvMsg->nPort;
				memcpy(socketMsg.byRemoteMac, pRecvMsg->byRemoteMac, sizeof(pRecvMsg->byRemoteMac));
				rl_strcpy_s(socketMsg.szRemoteAddr, sizeof(socketMsg.szRemoteAddr), pRecvMsg->szRemoteAddr);
				if(GetEthernetControlInstance()->Send(&socketMsg) < 0)
				{
					return -1;
				}
#else				
				if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}			
#endif		
			}
			else
			{
				if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
		}
		break;

#if 0
	case MSG_TO_DEVICE_REMOTE_CONTROL:
		{
			SOCKET_MSG_REMOTE_CONTROL remoteControlMsg;
			if(GetMsgControlInstance()->ParseRemoteControlMsg(pNormalMsg, &remoteControlMsg, nDataSize) < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->GetConnectMode() != DOORSETTING_CONNECT_SERVER_MODE_CLOUD)//if the mode is CLOUD, use the other ACK
			{
	            SOCKET_MSG socketMsg;
				if (GetMsgControlInstance()->BuildACKMsg(&socketMsg, nMsgID, nCRC, 0,"") < 0)
				{
					return -1;
				}
				if(GetConnectControlInstance()->IsConnectSucceed())
				{
					if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
					{
						return -1;
					}
				}
				else
				{
					if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
					{
						return -1;
					}
				}
			}

			OnRemoteControlMsg(&remoteControlMsg);
		}
		break;
	case MSG_TO_DEVICE_REQUEST_CONFIG:
		{
			SOCKET_MSG_CONFIG reqConfigMsg;
			if(GetMsgControlInstance()->ParseReqConfigMsg(pNormalMsg, &reqConfigMsg, nDataSize) < 0)
			{
				return -1;
			}
			
			SOCKET_MSG socketMsg;
			if(GetMsgControlInstance()->BuildReportConfigMsg(&socketMsg, &reqConfigMsg.module) < 0)
			{
				return -1;
			}

			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;

	case MSG_TO_DEVICE_REQUEST_CONFIG_UDP:
		{
			SOCKET_MSG_CONFIG reqConfigMsg;
			if(GetMsgControlInstance()->ParseReqConfigMsg(pNormalMsg, &reqConfigMsg, nDataSize) < 0)
			{
				return -1;
			}
			
			SOCKET_MSG socketMsg;
			if(GetMsgControlInstance()->BuildReportConfigMsg(&socketMsg, &reqConfigMsg.module) < 0)
			{
				return -1;
			}
			
			if(pRecvMsg->nTransport == TRANSPORT_TYPE_UDP)
			{
#if RL_SUPPORT_ETHERNET_TRANSPORT
				socketMsg.nPort = pRecvMsg->nPort;
				memcpy(socketMsg.byRemoteMac, pRecvMsg->byRemoteMac, sizeof(pRecvMsg->byRemoteMac));
				rl_strcpy_s(socketMsg.szRemoteAddr, sizeof(socketMsg.szRemoteAddr), pRecvMsg->szRemoteAddr);
				
				if(GetEthernetControlInstance()->Send(&socketMsg) < 0)
				{
					return -1;
				}
#else				
				if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}			
#endif				
			}
			else
			{
				if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
			
		}
		break;

	case MSG_TO_DEVICE_UPDATE_CONFIG:
		{
			SOCKET_MSG_CONFIG updateConfigMsg;
			if(GetMsgControlInstance()->ParseUpdateConfigMsg(pNormalMsg, &updateConfigMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseUpdateConfigMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//
			SOCKET_MSG socketMsg;
		#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_GM8138 || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
			//安卓室内机改为直接发个ipc给autop，用来兼容一些特殊的非配置库的配置项
			ipc_send(IPC_ID_AUTOP, MSG_D2A_UPDATE_CONFIG, 0, 0, NULL, 0);
		#else
			INT ret = GetMsgControlInstance()->RecvUpdateConfigMsg(&updateConfigMsg.module);
			if( ret< 0)
			{
				rl_log_err("%s: RecvUpdateConfigMsg failed.", __FUNCTION__);
			}
		#endif
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"") < 0)
			{
				return -1;
			}
			if(pRecvMsg->nTransport == TRANSPORT_TYPE_UDP)
			{
#if RL_SUPPORT_ETHERNET_TRANSPORT
				socketMsg.nPort = pRecvMsg->nPort;
				memcpy(socketMsg.byRemoteMac, pRecvMsg->byRemoteMac, sizeof(pRecvMsg->byRemoteMac));
				rl_strcpy_s(socketMsg.szRemoteAddr, sizeof(socketMsg.szRemoteAddr), pRecvMsg->szRemoteAddr);
				
				if(GetEthernetControlInstance()->Send(&socketMsg) < 0)
				{
					return -1;
				}
#else				
				if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}			
#endif						
			}
			else
			{
				if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
		}
		break;

	case MSG_TO_DEVICE_UPGRADE_START:
		{
			SOCKET_MSG_UPGRADE_START upgradeStartMsg;
			if(GetMsgControlInstance()->ParseUpgradeStartMsg(pNormalMsg, &upgradeStartMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseUpgradeStartMsg failed.", __FUNCTION__);
				return -1;
			}
			//
			SOCKET_MSG socketMsg;
			INT ret = GetUpgradeControlInstance()->RecvUpgradeStartMsg(&upgradeStartMsg);
			if( ret< 0)
			{
				rl_log_err("%s: RecvUpgradeStartMsg failed.", __FUNCTION__);
			}
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,ret,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;

	case MSG_TO_DEVICE_FILE_DATA:
		{
			SOCKET_MSG_FILEDATA *pFileDataMsg = (SOCKET_MSG_FILEDATA *)pRecvMsg->byData;
			UINT nDataOffset = DCLIENT_NTOHL(pFileDataMsg->nDataOffset);
			UINT uiDataSize = DCLIENT_NTOHS(pFileDataMsg->nDataSize);
			UCHAR *pFileData = pFileDataMsg->byData;
			
			if(GetUpgradeControlInstance()->OnRecvUpgradeData(pFileData, uiDataSize, nDataOffset) < 0)
			{
				rl_log_err("%s: OnRecvUpgradeData failed.", __FUNCTION__);
				return -1;
			}
		}
		break;

	case MSG_TO_DEVICE_FILE_END:
		{
			SOCKET_MSG_FILE_END fileEndMsg;
			memset(&fileEndMsg, 0, sizeof(SOCKET_MSG_FILE_END));
			if(GetMsgControlInstance()->ParseFileEndMsg(pNormalMsg, &fileEndMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseFileEndMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//
			SOCKET_MSG socketMsg;
			INT ret = GetUpgradeControlInstance()->RecvFileEndMsg(&fileEndMsg);
			if( ret< 0)
			{
				rl_log_err("%s: RecvFileEndMsg failed.", __FUNCTION__);
			}
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,ret,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
	case MSG_TO_DEVICE_ACK:
		{
			SOCKET_MSG_ACK ackMsg;
			if(GetMsgControlInstance()->ParseACKMsg(pNormalMsg, &ackMsg, nDataSize) < 0)
			{
				return -1;
			}
			INT remote_id ;
			USHORT remote_crc;
			remote_id = strtol(ackMsg.szMsgID, NULL, 16);
			remote_crc = strtol(ackMsg.szMsgCRC, NULL, 16);
			rl_log_info("%s: MSG_TO_DEVICE_ACK MsgID = 0x%X, MsgCRC = %X, RESULT = %s, INFO = %s", __FUNCTION__, remote_id, remote_crc, ackMsg.szResult, ackMsg.szInfo);
			switch (remote_id&0x07ff)
			{
				case MSG_FROM_DEVICE_CHECK_KEY:
					{
						GetDoorControlInstance()->CheckKeyForResult(ackMsg.szResult,ackMsg.szInfo);
	                }
					break;
				case MSG_FROM_DEVICE_TEXT_MSG:
				case MSG_TO_DEVICE_SEND_TEXT_MESSAGE:
					{
						GetTextMessageControlInstance()->OnTextMsgAck(&ackMsg);
					}
					break;
				case MSG_FROM_DEVICE_ALARM:
				case MSG_TO_DEVICE_ALARM_SEND:
					{
						GetAlarmControlInstance()->OnAlarmMsgAck(&ackMsg);
					}
					break;
				case MSG_FROM_DEVICE_REPORT_ACTIVITY:
					{
						DCLIENT_REPORT_ACTIVITY_ACK ack;
						memset(&ack, 0, sizeof(DCLIENT_REPORT_ACTIVITY_ACK));
						ack.nSeq = ackMsg.nSequenceNum;
						ack.nResponse = rl_atoi(ackMsg.szResult);
						ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_ACTIVITY_ACK, 0, 0, &ack, sizeof(DCLIENT_REPORT_ACTIVITY_ACK));
					}
					break;
				case MSG_FROM_DEVICE_SYNC_ACTIVITY:
					{
						DCLIENT_SYNC_ACTIVITY_ACK ack;
						memset(&ack, 0, sizeof(DCLIENT_SYNC_ACTIVITY_ACK));
						ack.nSeq = ackMsg.nSequenceNum;
						ack.nResponse = rl_atoi(ackMsg.szResult);
						ipc_send(IPC_ID_PHONE, MSG_D2P_SYNC_ACTIVITY_ACK, 0, 0, &ack, sizeof(DCLIENT_SYNC_ACTIVITY_ACK));
					}
					break;
				default:
					break;
			}
		}
		break;	
	case MSG_TO_DEVICE_SEND_OWNER_MESSAGE:
		{
			SOCKET_MSG_OWNER_MESSAGE ownerMsg;
			if(GetMsgControlInstance()->ParseOwnerMsg(pNormalMsg, &ownerMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseFileOwnerMsg failed.", __FUNCTION__);
				return -1;
			}
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}


			//TEST
			//GetAlarmControlInstance()->SendAlarmMsg("NORMAL");

			//TODO:收到消息后的处理
			DCLIENT_TEXT_MSG txtMsg;
			memset(&txtMsg, 0, sizeof(DCLIENT_TEXT_MSG));
			rl_strcpy_s(txtMsg.title, sizeof(txtMsg.title), ownerMsg.szTitle);
			rl_strcpy_s(txtMsg.content, sizeof(txtMsg.content), ownerMsg.szContent);
			rl_strcpy_s(txtMsg.time, sizeof(txtMsg.time), ownerMsg.szTime);
			return ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_MSG, 0, 0, &txtMsg, sizeof(txtMsg));
		}
		break;
#endif			
	case MSG_TO_DEVICE_SEND_TEXT_MESSAGE:
		{
			SOCKET_MSG_TEXT_MESSAGE textMsg;
			if(GetMsgControlInstance()->ParseTextMsg(pNormalMsg, &textMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseFileOwnerMsg failed.", __FUNCTION__);
				return -1;
			}
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"", textMsg.nSequenceNum) < 0)
			{
				return -1;
			}
			if(nTransportType == TRANSPORT_TYPE_UDP)
			{
				if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
			else
			{
				if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
			
			//TEST
			//GetAlarmControlInstance()->SendAlarmMsg("NORMAL");

			//TODO:收到消息后的处理
			rl_log_info("TODO: Received Text Message.");
			DCLIENT_OWNER_MSG ownerTextMsg;
            memset(&ownerTextMsg, 0, sizeof(DCLIENT_OWNER_MSG));
            rl_strcpy_s(ownerTextMsg.title, sizeof(ownerTextMsg.title), textMsg.szTitle);
            rl_strcpy_s(ownerTextMsg.content, sizeof(ownerTextMsg.content), textMsg.szContent);
            rl_strcpy_s(ownerTextMsg.time, sizeof(ownerTextMsg.time), textMsg.szTime);
            GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_RECEIVE_OWNER_MESSAGE, 0, 0, &ownerTextMsg, sizeof(ownerTextMsg));

			// DCLIENT_TEXT_MSG txtMsg;
			// memset(&txtMsg, 0, sizeof(DCLIENT_TEXT_MSG));
			// txtMsg.nMsgID = textMsg.nMsgID;
			// txtMsg.nSequenceNum = textMsg.nSequenceNum;
			// txtMsg.nMultiFlag = textMsg.nMultiFlag;
			// rl_strcpy_s(txtMsg.title, sizeof(txtMsg.title), textMsg.szTitle);
			// rl_strcpy_s(txtMsg.content, sizeof(txtMsg.content), textMsg.szContent);
			// rl_strcpy_s(txtMsg.time, sizeof(txtMsg.time), textMsg.szTime);
			// rl_strcpy_s(txtMsg.from, sizeof(txtMsg.from), textMsg.szFrom);			
			// rl_strcpy_s(txtMsg.to, sizeof(txtMsg.to), textMsg.szTo);
			// return ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_MSG, 0, 0, &txtMsg, sizeof(txtMsg));
		}
		break;

	case MSG_TO_DEVICE_KEY_SEND:
		{
			SOCKET_MSG_KEY_SEND keySendMsg;
			if(GetMsgControlInstance()->ParseKeySendMsg(pNormalMsg, &keySendMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseKeySendMsg failed.", __FUNCTION__);
				return -1;
			}
			
#if 0//先不考虑答复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
#endif

			char szConfigMD5[DCLIENT_MD5_SIZE] = {0};			//配置文件的MD5值
			char szContactMD5[DCLIENT_MD5_SIZE] = {0};			//联系人文件的MD5值
			GetSettingHandleInstance()->GetConfigMD5(szConfigMD5, sizeof(DCLIENT_MD5_SIZE));
			GetSettingHandleInstance()->GetContactMD5(szContactMD5, sizeof(DCLIENT_MD5_SIZE));
			if(rl_strcmp(szConfigMD5, keySendMsg.szConfigMD5) != 0 && !rl_str_isempty(keySendMsg.szConfigUrl))
			{
				DOWNLOAD_SERVER_INFO downloadInfo;
				memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
				rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), keySendMsg.szConfigUrl);
				rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), keySendMsg.szConfigMD5);
				GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_CONFIG, 0, 0, &downloadInfo, sizeof(downloadInfo));
			}
			if(rl_strcmp(szContactMD5, keySendMsg.szContactMD5) != 0 && !rl_str_isempty(keySendMsg.szContactUrl))
			{
				DOWNLOAD_SERVER_INFO downloadInfo;
				memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
				rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), keySendMsg.szContactUrl);
				rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), keySendMsg.szContactMD5);
				GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_AKCS_CONTACT, 0, 0, &downloadInfo, sizeof(downloadInfo));
			}
		}
		break;

#if 0
	case MSG_TO_DEVICE_UPGRADE_SEND:
		{
			SOCKET_MSG_UPGRADE_SEND upgradeSendMsg;
			if(GetMsgControlInstance()->ParseUpgradeSendMsg(pNormalMsg, &upgradeSendMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseUpgradeSendMsg failed.", __FUNCTION__);
				return -1;
			}
			
#if 0//先不考虑答复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
#endif
			//获取软件版本号
			CHAR szFirmwareVer[DEVICE_SWVER_SIZE] = {0};
			GetSettingHandleInstance()->GetSWVer(szFirmwareVer, sizeof(szFirmwareVer));
			if((!rl_str_isempty(upgradeSendMsg.szFirmwareVer)) && (rl_strcmp(szFirmwareVer, upgradeSendMsg.szFirmwareVer) != 0))
			{
				return GetUpgradeControlInstance()->UpgradeFirmwareUrl(upgradeSendMsg.szFirmwareUrl);
			}
		}
		break;

	case MSG_TO_DEVICE_AD_SEND:
		{
			SOCKET_MSG_AD_SEND adSendMsg;
			if(GetMsgControlInstance()->ParseAdSendMsg(pNormalMsg, &adSendMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseAdSendMsg failed.", __FUNCTION__);
				return -1;
			}
			
#if 1//先不考虑答复ACK
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"") < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				return -1;
			}
#endif
			
			GetAdControlInstance()->OnRecvAdSend(&adSendMsg);			
		}
		break;
	case MSG_FROM_DEVICE_ALARM:
	case MSG_TO_DEVICE_ALARM_SEND:
		{
			//由于存在Alarm是由设备直接发过来和从SDMC转发过来两种情况，
			//两种情况下收到的信息是不一样的，在这里做个转换，将从
			//由设备收过来的MSG转换成跟由SDMC收过来的一样，这样phone端就不用做修改。
			SOCKET_MSG_ALARM_SEND alarmSendMsg;
			rl_memset(&alarmSendMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));
			if(nTransportType == TRANSPORT_TYPE_UDP)
			{
				SOCKET_MSG_ALARM alarmMsg;
				if(GetMsgControlInstance()->ParseAlarmMsg(pNormalMsg, &alarmMsg, nDataSize) < 0)
				{
					rl_log_err("%s: ParseAlarmMsg failed.", __FUNCTION__);
					return -1;
				}
								
				INT nType = -1;
				INT nExtension = -1;
				CHAR szDeviceID[DEVICE_ID_SIZE] = {0};
				if(rl_strchr(alarmMsg.szFrom, '_') == NULL)
				{
					sscanf(alarmMsg.szFrom, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
				}
				else
				{		
					sscanf(alarmMsg.szFrom, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
				}
				rl_strcpy_s(alarmSendMsg.szAddress, sizeof(alarmSendMsg.szAddress), szDeviceID);
				alarmSendMsg.nDeviceType = nType;
				alarmSendMsg.nExtension = nExtension;
				alarmSendMsg.nSequenceNum = alarmMsg.nSequenceNum;
				alarmSendMsg.id = alarmMsg.id;
				alarmSendMsg.unAlarmCustomize = alarmMsg.unAlarmCustomize;
				alarmSendMsg.unAlarmLocation = alarmMsg.unAlarmLocation;
				alarmSendMsg.unAlarmZone = alarmMsg.unAlarmZone;
				rl_strcpy_s(alarmSendMsg.szTime, sizeof(alarmSendMsg.szTime), alarmMsg.szTime);				
				rl_strcpy_s(alarmSendMsg.szProtocal, sizeof(alarmSendMsg.szProtocal), alarmMsg.szProtocal);				
				rl_strcpy_s(alarmSendMsg.szType, sizeof(alarmSendMsg.szType), alarmMsg.szType);
				
			}
			else
			{
				if(GetMsgControlInstance()->ParseAlarmSendMsg(pNormalMsg, &alarmSendMsg, nDataSize) < 0)
				{
					rl_log_err("%s: ParseAlarmSendMsg failed.", __FUNCTION__);
					return -1;
				}
			}
#if 1
			SOCKET_MSG socketMsg;
			if (GetMsgControlInstance()->BuildACKMsg(&socketMsg,nMsgID,nCRC,0,"", alarmSendMsg.nSequenceNum) < 0)
			{
				return -1;
			}
			if(nTransportType == TRANSPORT_TYPE_UDP)
			{
				if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
			else
			{
				if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
				{
					return -1;
				}
			}
#endif
			GetAlarmControlInstance()->OnRecvAlarmSend(&alarmSendMsg);	
		}
		break;
	
	case MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED:
		{
			SOCKET_MSG_ALARM_SEND alarmSendMsg;
			if(GetMsgControlInstance()->ParseAlarmSendMsg(pNormalMsg, &alarmSendMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseAlarmSendMsg failed.", __FUNCTION__);
				return -1;
			}
			GetAlarmControlInstance()->OnRecvAlarmSend(&alarmSendMsg);	
		}
		break;
#endif

	case MSG_TO_DEVICE_UPDATE_HEARTBEAT_PERIOD:
		SOCKET_MSG_HEARTBEAT_PERIOD heartBeatPeriodMsg;
		if(GetMsgControlInstance()->ParseHeartBeatPeriodMsg(pNormalMsg, &heartBeatPeriodMsg, nDataSize) < 0)
		{
			rl_log_err("%s: ParseHeartBeatPeriodMsg failed.", __FUNCTION__);
			return -1;
		}
		GetConnectControlInstance()->SetHeartBeatExpire(atoi(heartBeatPeriodMsg.szExpire));
		
		GetConnectControlInstance()->SetHeartBeatFlag(TRUE);
		break;

#if 0
#if RL_GLOBAL_SUPPORT_TMP_KEY
	case MSG_TO_DEVICE_CHECK_TMP_KEY_ACK:
		{
			SOCKET_MSG_CHECK_TMP_KEY tCheckTmpKey = {0};
			if(GetMsgControlInstance()->ParseCheckTmpKeyMsg(pNormalMsg, &tCheckTmpKey, nDataSize) < 0)
			{
				rl_log_err("%s: ParseCheckTmpKeyAck failed.", __FUNCTION__);
				return -1;
			}

			//TODO:收到消息后的处理
			rl_log_info("TODO: Received Check Tmp Key.");
			DCLIENT_CHECK_TMP_KEY tTmpKey;
			memset(&tTmpKey, 0, sizeof(DCLIENT_CHECK_TMP_KEY));
			rl_strcpy_s(tTmpKey.szMsgSeqCode, sizeof(tTmpKey.szMsgSeqCode), tCheckTmpKey.szMsgSeqCode);			
			rl_strcpy_s(tTmpKey.szRelay, sizeof(tTmpKey.szRelay), tCheckTmpKey.szRelay);	
			rl_strcpy_s(tTmpKey.szSecurityRelay, sizeof(tTmpKey.szSecurityRelay), tCheckTmpKey.szSecurityRelay);
			rl_strcpy_s(tTmpKey.szUnitAPT, sizeof(tTmpKey.szUnitAPT), tCheckTmpKey.szUnitAPT);
			rl_strcpy_s(tTmpKey.szPerID, sizeof(tTmpKey.szPerID), tCheckTmpKey.szPerID);
			tTmpKey.nResult = tCheckTmpKey.nResult;
			return ipc_send(IPC_ID_PHONE, MSG_D2P_CHECK_TMP_KEY_ACK, 0, 0, &tTmpKey, sizeof(tTmpKey));
		}
		break;
#endif	
#if RL_GLOBAL_SUPPORT_VRTSP	
	case MSG_TO_DEVICE_START_RTSP:
		{
			SOCKET_MSG_START_RTSP tStartRtspMsg;
			memset(&tStartRtspMsg, 0, sizeof(SOCKET_MSG_START_RTSP));
			if(GetMsgControlInstance()->ParseStartRtspMsg(pNormalMsg, &tStartRtspMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseStartRtspMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//发送IPC消息给VA
			VIDEO_RTSP rtsp;
			rl_strcpy_s(rtsp.remote_ip, sizeof(rtsp.remote_ip), tStartRtspMsg.szRemoteIP);
			rtsp.remote_port = tStartRtspMsg.nRemotePort;
			rtsp.expire = (tStartRtspMsg.nExpire == 0 ? 60:tStartRtspMsg.nExpire);
			rtsp.ssrc = tStartRtspMsg.nSSRC;
		#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID) || RL_SUPPORT_VRTSP_INDOOR || (RL_PLATFORMID == RL_PLATFORMID_HI3516DV300) || (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
			ipc_send(IPC_ID_PHONE, MSG_D2P_START_VRTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#else
			ipc_send(IPC_ID_VA, VOICE_MSG_START_RTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#endif
		}
		break;

	case MSG_TO_DEVICE_STOP_RTSP:
		{
			SOCKET_MSG_STOP_RTSP tStopRtspMsg;
			memset(&tStopRtspMsg, 0, sizeof(SOCKET_MSG_STOP_RTSP));
			if(GetMsgControlInstance()->ParseStopRtspMsg(pNormalMsg, &tStopRtspMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseStopRtspMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//发送IPC消息给VA
			VIDEO_RTSP rtsp;
			rl_strcpy_s(rtsp.remote_ip, sizeof(rtsp.remote_ip), tStopRtspMsg.szRemoteIP);
			rtsp.remote_port = tStopRtspMsg.nRemotePort;
		#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID) || RL_SUPPORT_VRTSP_INDOOR || (RL_PLATFORMID == RL_PLATFORMID_HI3516DV300) || (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
			ipc_send(IPC_ID_PHONE, MSG_D2P_STOP_VRTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#else
			ipc_send(IPC_ID_VA, VOICE_MSG_STOP_RTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#endif
		}
		break;
	case MSG_TO_DEVICE_KEEP_RTSP:
		{
			SOCKET_MSG_START_RTSP tKeepRtspMsg;
			memset(&tKeepRtspMsg, 0, sizeof(MSG_TO_DEVICE_START_RTSP));
			if(GetMsgControlInstance()->ParseStartRtspMsg(pNormalMsg, &tKeepRtspMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseStartRtspMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//发送IPC消息给VA
			VIDEO_RTSP rtsp;
			rl_strcpy_s(rtsp.remote_ip, sizeof(rtsp.remote_ip), tKeepRtspMsg.szRemoteIP);
			rtsp.remote_port = tKeepRtspMsg.nRemotePort;
			rtsp.expire = (tKeepRtspMsg.nExpire == 0 ? tKeepRtspMsg.nExpire : 60);

		#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID) || RL_SUPPORT_VRTSP_INDOOR || (RL_PLATFORMID == RL_PLATFORMID_HI3516DV300) || (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
			ipc_send(IPC_ID_PHONE, MSG_D2P_KEEP_VRTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#else
			ipc_send(IPC_ID_VA, VOICE_MSG_KEEP_RTSP, 0, 0, &rtsp, sizeof(VIDEO_RTSP));
		#endif
			rl_log_info("Send VOICE_MSG_KEEP_RTSP success.");
		}
		break;
#endif	
	case MSG_TO_DEVICE_CREATE_BIND_CODE_ACK:
		//解析出bindcode
		SOCKET_MSG_BIND_CODE_CREATE bindCodeCreate;
		if(GetMsgControlInstance()->ParseGetBindCodeMsg(pNormalMsg, &bindCodeCreate, nDataSize) < 0)
		{
			rl_log_err("%s: ParseGetBindCodeMsg failed.", __FUNCTION__);
			return -1;
		}
		GetBindCodeControlInstance()->OnGetBindCode(&bindCodeCreate);
		break;
	case MSG_TO_DEVICE_DELETE_BIND_CODE_ACK:
		SOCKET_MSG_BIND_CODE_CREATE bindCodeDelete;
		if(GetMsgControlInstance()->ParseDeleteBindCodeMsg(pNormalMsg, &bindCodeDelete, nDataSize) < 0)
		{
			rl_log_err("%s: ParseDeleteBindCodeMsg failed.", __FUNCTION__);
			return -1;
		}
		GetBindCodeControlInstance()->OnDeleteBindCode(&bindCodeDelete);
		break;
	case MSG_TO_DEVICE_BIND_CODE_LIST_ACK:
		SOCKET_MSG_BIND_CODE_LIST bindCodeList;
		if(GetMsgControlInstance()->ParseGetBindCodeListMsg(pNormalMsg, &bindCodeList, nDataSize) < 0)
		{
			rl_log_err("%s: ParseGetBindCodeListMsg failed.", __FUNCTION__);
			return -1;
		}
		GetBindCodeControlInstance()->OnGetBindCodeList(&bindCodeList);
		break;
	case MSG_TO_DEVICE_NOTIFY_BIND_CODE_CHANGE:
		{
			INT nSequenceNum = 0;
			if(GetMsgControlInstance()->ParseNotifyBindCodeChangeMsg(pNormalMsg, &nSequenceNum, nDataSize) < 0)
			{
				rl_log_err("%s: ParseNotifyBindCodeChangeMsg failed.", __FUNCTION__);
				return -1;
			}
			GetBindCodeControlInstance()->SendGetBindCodeListRequest(nSequenceNum);
		}
		break;
	case MSG_TO_DEVICE_NOTIFY_ALARM_DEAL:
		{
			SOCKET_MSG_ALARM_DEAL alarmDealMsg;
			if(GetMsgControlInstance()->ParseAlarmDealMsg(pNormalMsg, &alarmDealMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseAlarmDealMsg failed.", __FUNCTION__);
				return -1;
			}
			GetAlarmControlInstance()->OnRecvAlarmDealNotify(&alarmDealMsg);
		}
		break;
	case MSG_TO_DEVICE_NOTIFY_ALARM_ACK:
		{
			SOCKET_MSG_ALARM alarmMsg;
			if(GetMsgControlInstance()->ParseAlarmAckMsg(pNormalMsg, &alarmMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseAlarmAckMsg failed.", __FUNCTION__);
				return -1;
			}			
			GetAlarmControlInstance()->OnRecvAlarmAck(&alarmMsg);
		}
		break;
#if RL_SUPPORT_RECV_MOTION_ALERT	
	case MSG_TO_DEVICE_DOOR_MOTION_ALERT:
		{
			SOCKET_MSG_DOOR_MOTION_ALERT doorMotionAlert;
			if(GetMsgControlInstance()->ParseDoorMotionAlertMsg(pNormalMsg, &doorMotionAlert, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDoorMotionAlertMsg failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnDoorMotionAlert(&doorMotionAlert);
		}
		break;
#endif		
	case MSG_FROM_DEVICE_SEND_DISCOVER:
		{
			/* By Will, 2019.4.22, RESPONSE DISCOVER ACK ALWAYS SINCE PC TOOL WOULD DISCOVER DEVICES. 
			#if RL_SUPPORT_DISCOVER_AUTO_SWITCH
			if(GetConnectControlInstance()->IsConnectSucceed())
			{
				break;
			}
			#endif
			*/
			char szLocalIPAddr[IP_SIZE] = {0};
			GetConnectControlInstance()->GetLocalIPAddr(szLocalIPAddr, sizeof(szLocalIPAddr));
			if(rl_strcmp(pszRemoteAddr, szLocalIPAddr) == 0)
			{
				rl_log_info("%s:ignore mine muticast msg.", __FUNCTION__);
				return 0;
			}
			SOCKET_MSG_DISCOVER_SEND discoverMsg;
			if(GetMsgControlInstance()->ParseDiscoverMsg(pNormalMsg, &discoverMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDiscoverMsg failed.", __FUNCTION__);
				return -1;
			}
			GetDiscoverControlInstance()->OnDiscoverMsg(pRecvMsg, &discoverMsg, pszRemoteAddr, nRemotePort);
		}
		break;
	case MSG_FROM_DEVICE_ACK_DISCOVER:
		{
			/* By Jeffrey, 2019.5.9, NEED ALWAYS HANDLE ACKDISCOVER ,CAUSE OF THE SCAN DEVICECODE. 
			#if RL_SUPPORT_DISCOVER_AUTO_SWITCH
			if(GetConnectControlInstance()->IsConnectSucceed())
			{
				break;
			}
			#endif
			*/
			char szLocalIPAddr[IP_SIZE] = {0};
			GetConnectControlInstance()->GetLocalIPAddr(szLocalIPAddr, sizeof(szLocalIPAddr));
			if(rl_strcmp(pszRemoteAddr, szLocalIPAddr) == 0)
			{
				rl_log_info("%s:ignore mine muticast msg.", __FUNCTION__);
				return 0;
			}
			SOCKET_MSG_DISCOVER_ACK discoverAckMsg;
			if(GetMsgControlInstance()->ParseDiscoverAckMsg(pNormalMsg, &discoverAckMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDiscoverAckMsg failed.", __FUNCTION__);
				return -1;
			}
			//flag为0表示要通知phone端，如果为1，则直接在dclient处理,flag为2表示在局域网内找指定的某类机型
			if(rl_strcmp(discoverAckMsg.szFlag, "0") == 0 || atoi(discoverAckMsg.szFlag)== DISCOVER_MATCH_FLAG_MODEL ||
				atoi(discoverAckMsg.szFlag) == DISCOVER_MATCH_FLAG_TYPE || 
				atoi(discoverAckMsg.szFlag) == DISCOVER_MATCH_FLAG_NONE || 
				atoi(discoverAckMsg.szFlag) == DISCOVER_FLAG_AUTO)
			{
				GetDiscoverControlInstance()->OnDiscoverAckMsg(&discoverAckMsg, pszRemoteAddr, nRemotePort);
			}
			else
			{
				GetTextMessageControlInstance()->OnDiscoverAckMsg(&discoverAckMsg);
				GetAlarmControlInstance()->OnDiscoverAckMsg(&discoverAckMsg);
			}
		}
		break;
#if RL_SUPPORT_CLOUD_DEV_INFO		
	case MSG_TO_DEVICE_SEND_DEVICE_LIST:
		{
			rl_log_info("RECV MSG_TO_DEVICE_SEND_DEVICE_LIST");

			//此处不解析device list的内容，只将文件内容写到文件中，然后通知phone
			char *pszPayload = (char *)pNormalMsg->byData;
			char *pszDest = new char[nDataSize+1];
			memset(pszDest, 0, nDataSize+1);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
			GetMsgControlInstance()->AesDecryptByMac(pszPayload, pszDest, nDataSize);
#else
			rl_strcpy_s(pszDest, nDataSize, pszPayload);
#endif
			GetDeviceControlInstance()->OnDeviceListInfo(pszDest, nDataSize);
			delete[] pszDest;
		}
		break;
	case MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE:
		{
			GetDeviceControlInstance()->OnDeviceListChangeNotify();
		}
		break;
	case MSG_TO_DEVICE_APP_LOGOUT_SIP:
		{
			GetDeviceControlInstance()->OnLogoutSip();
		}
		break;
#endif	
#if RL_SUPPORT_ARMING
case MSG_TO_DEVICE_REQUEST_ARMING:
case MSG_TO_DEVICE_REQUEST_ARMING_2:
		{
			BOOL bP2PMode = FALSE;
			//if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
			if(nTransportType == TRANSPORT_TYPE_UDP)
			{
				bP2PMode = TRUE;
			}
			SOCKET_MSG_REQUEST_ARMING requestArmingMsg;
			if(GetMsgControlInstance()->ParseRequestArmingMsg(pNormalMsg, &requestArmingMsg, nDataSize, bP2PMode) < 0)
			{
				rl_log_err("%s: ParseRequestArmingMsg failed.", __FUNCTION__);
				return -1;
			}
			
			//发送ARMING消息给phone
			DCLIENT_REQUEST_ARMING dclientRequestArming;
			memset(&dclientRequestArming, 0, sizeof(DCLIENT_REQUEST_ARMING));
			dclientRequestArming.mode = requestArmingMsg.nMode;
			rl_strcpy_s(dclientRequestArming.action, sizeof(dclientRequestArming.action), requestArmingMsg.szAction);
			rl_strcpy_s(dclientRequestArming.from, sizeof(dclientRequestArming.from), requestArmingMsg.szFrom);
			rl_strcpy_s(dclientRequestArming.to, sizeof(dclientRequestArming.to), requestArmingMsg.szTo);
			rl_strcpy_s(dclientRequestArming.from_ip, sizeof(dclientRequestArming.from_ip), requestArmingMsg.szFromIP);
			rl_strcpy_s(dclientRequestArming.to_ip, sizeof(dclientRequestArming.to_ip), requestArmingMsg.szToIP);
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_ARMING, 0, 0, (void *)&dclientRequestArming, sizeof(DCLIENT_REQUEST_ARMING));
		}
		break;
#endif
#if RL_SUPPORT_ARMING_P2P
		case MSG_FROM_DEVICE_REPORT_ARMING:
			{
				SOCKET_MSG_REPORT_ARMING reportArmingMsg;
				BOOL bP2PMode = FALSE;
				//if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
				if(nTransportType == TRANSPORT_TYPE_UDP)
				{
					bP2PMode = TRUE;
				}
				if(GetMsgControlInstance()->ParseReportArmingMsg(pNormalMsg, &reportArmingMsg, nDataSize, bP2PMode) < 0)
				{
					rl_log_err("%s: ParseReportArmingMsg failed.", __FUNCTION__);
					return -1;
				}
				//发送ARMING消息给phone
				DCLIENT_REPORT_ARMING dclientReportArming;
				memset(&dclientReportArming, 0, sizeof(DCLIENT_REPORT_ARMING));
				dclientReportArming.mode = reportArmingMsg.nMode;
				dclientReportArming.nSync = reportArmingMsg.nSync;
				dclientReportArming.nActionType= reportArmingMsg.nActionType;
				dclientReportArming.nSequenceNum= reportArmingMsg.nSequenceNum;
				rl_strcpy_s(dclientReportArming.from, sizeof(dclientReportArming.from), reportArmingMsg.szFrom);
				rl_strcpy_s(dclientReportArming.to, sizeof(dclientReportArming.to), reportArmingMsg.szTo);
				rl_strcpy_s(dclientReportArming.from_ip, sizeof(dclientReportArming.from_ip), reportArmingMsg.szFromIP);
				rl_strcpy_s(dclientReportArming.to_ip, sizeof(dclientReportArming.to_ip), reportArmingMsg.szToIP);
				ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_ARMING, 0, 0, (void *)&dclientReportArming, sizeof(DCLIENT_REPORT_ARMING));				
			}
		break;
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
	case MSG_TO_DEVICE_PUSH_NOANSWER_FWD_NUMBER:
		{
			SOCKET_MSG_PUSH_FORWARD_NUMBER pushForwardNumber;
			if(GetMsgControlInstance()->ParsePushForwardNumberMsg(pNormalMsg, &pushForwardNumber, nDataSize) < 0)
			{
				rl_log_err("%s: ParsePushForwardNumberMsg failed.", __FUNCTION__);
				return -1;
			}

			return GetSettingControlInstance()->RecvPushForwardNumber(&pushForwardNumber);
		}
		break;
	case MSG_TO_DEVICE_REPORT_NOANSWER_FWD_NUMBER:
		{
			SOCKET_MSG_REPORT_FORWARD_NUMBER reportForwardNumberMsg;
			if(GetMsgControlInstance()->ParseReportForwardNumberMsg(pNormalMsg, &reportForwardNumberMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReportForwardNumberMsg failed.", __FUNCTION__);
				return -1;
			}
			
			return GetSettingControlInstance()->RecvReportForwardNumber(&reportForwardNumberMsg);
		}
		break;	
#endif
	case MSG_TO_DEVICE_SCAN:
		{
			SOCKET_MSG bootupMsg;
			if(GetMsgControlInstance()->BuildBootupMsg(&bootupMsg) < 0)
			{
				return -1;
			}
	
			rl_log_debug("bootupMsg.nSize = %d", bootupMsg.nSize);
			if(GetConnectControlInstance()->SendMulticastMsg(bootupMsg.byData, bootupMsg.nSize) < 0)
			{
				return -1;
			}
		}
		break;
#endif

	case MSG_TO_DEVICE_CONTACT_URL:
		{
			SOCKET_MSG_CONTACT_URL contactUrl;
			if(GetMsgControlInstance()->ParseContactUrlMsg(pNormalMsg, &contactUrl, nDataSize) < 0)
			{
				rl_log_err("%s: ParseContactUrlMsg failed.", __FUNCTION__);
				return -1;
			}	
			DOWNLOAD_SERVER_INFO downloadInfo;
			memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
			rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), contactUrl.szContactUrl);
			rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), "");
			GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_AKCS_CONTACT, 0, 0, &downloadInfo, sizeof(DOWNLOAD_SERVER_INFO));
		}
		break;

#if 0
#if RL_SUPPORT_DEVICE_MAINTENANCE
	case MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS:
		{
			SOCKET_MSG_RECONNECT_RPS rpsServer;
			if(GetMsgControlInstance()->ParseReconnectRPSMsg(pNormalMsg, &rpsServer, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReconnectRPSMsg failed.", __FUNCTION__);
				return -1;
			}
			GetConnectControlInstance()->SetHasForceRpsServer(rpsServer.szRPSServer);
		}
		break;
	case MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY:
		{
			SOCKET_MSG_RECONNECT_GATEWAY gatewayServer;
			if(GetMsgControlInstance()->ParseReconnectGateWayMsg(pNormalMsg, &gatewayServer, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReconnectGateWayMsg failed.", __FUNCTION__);
				return -1;
			}
			GetConnectControlInstance()->SetHasForceGatewayServer(gatewayServer.szGateWayServer);
		}
		break;
	case MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESS_SERVER:
		{
			SOCKET_MSG_RECONNECT_ACCESS_SERVER accessServer;
			if(GetMsgControlInstance()->ParseReconnectAccessMsg(pNormalMsg, &accessServer, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReconnectAccessMsg failed.", __FUNCTION__);
				return -1;
			}
			GetConnectControlInstance()->SetHasForceAccessServer(accessServer.szAccessServer);
		}
		break;
#endif
	case MSG_TO_DEVICE_CHECK_DTMF_ACK:
		{
			SOCKET_MSG_CHECK_DTMF_ACK checkDtmfAck;
			if(GetMsgControlInstance()->ParseCheckDtmfAckMsg(pNormalMsg, &checkDtmfAck, nDataSize) < 0)
			{
				rl_log_err("%s: ParseCheckDtmfAckMsg failed.", __FUNCTION__);
				return -1;
			}
#if RL_SUPPORT_CLOUD_DEV_INFO
			GetDeviceControlInstance()->OnCheckDtmfAck(&checkDtmfAck);
#endif
		}
		break;
	case MSG_TO_DEVICE_DEVICE_CODE:
		{
			SOCKET_MSG_DEVICE_CODE deviceCode;
			if(GetMsgControlInstance()->ParseDeviceCodeMsg(pNormalMsg, &deviceCode, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDeviceCodeMsg failed.", __FUNCTION__);
				return -1;
			}			
#if RL_SUPPORT_CLOUD_DEV_INFO
			GetDeviceControlInstance()->OnDeviceCode(&deviceCode);
#endif
		}
		break;
	case MSG_FROM_DEVICE_REPORT_DEVICE_CODE:
		{
			DCLIENT_REPORT_DEVICE_CODE reportDeviceCode;
			rl_memset(&reportDeviceCode, 0 ,sizeof(DCLIENT_REPORT_DEVICE_CODE));
			rl_memcpy(&reportDeviceCode, pNormalMsg->byData, sizeof(DCLIENT_REPORT_DEVICE_CODE));	
#if RL_SUPPORT_CLOUD_DEV_INFO		
			GetDeviceControlInstance()->OnReportDeviceCode(&reportDeviceCode);
#endif
		}
		break;

	case MSG_DEVICE_TO_DEVICE_REPORT_NETWORK_INFO:
		{
			DCLIENT_REPORT_NETWORK_INFO reportNetworkInfo;
			rl_memset(&reportNetworkInfo, 0 ,sizeof(DCLIENT_REPORT_NETWORK_INFO));
			rl_memcpy(&reportNetworkInfo, pNormalMsg->byData, sizeof(DCLIENT_REPORT_NETWORK_INFO));		
#if RL_SUPPORT_CLOUD_DEV_INFO	
			GetDeviceControlInstance()->OnReportNetworkInfo(&reportNetworkInfo);
#endif
		}
		break;

	case MSG_TO_DEVICE_CLEAR_DEVICE_CODE:
		{
#if RL_SUPPORT_CLOUD_DEV_INFO
			GetDeviceControlInstance()->OnClearDeviceCode();
#endif
		}
		break;
#if RL_SUPPORT_DEVICE_MAINTENANCE		
	case MSG_TO_DEVICE_CLI_COMMAND:
		{
			SOCKET_MSG_CLI_COMMAND cliCommandMsg;			
			if(GetMsgControlInstance()->ParseCliCommandMsg(pNormalMsg, &cliCommandMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseCliCommandMsg failed.", __FUNCTION__);
				return -1;
			}
			GetMaintenanceControlInstance()->OnDeviceCliCommand(&cliCommandMsg);
		}
		break;
#endif
	case MSG_FROM_DEVICE_COMMON_FORWARD:
	case MSG_TO_DEVICE_COMMON_FORWARD:
		{
			SOCKET_MSG_COMMON_TRANSFER commonTransferMsg;
			if(GetMsgControlInstance()->ParseCommonTransferMsg(pNormalMsg, &commonTransferMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseCommonTransferMsg failed.", __FUNCTION__);
				return -1;
			}
			OnCommonTransferMsg(&commonTransferMsg);
		}
		break;
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
	case MSG_TO_DEVICE_DOWNLOAD_FACE_PIC:
		{
			SOCKET_MSG_DOWNUPLOAD_FACE_PIC downloadFacePicMsg;
			if(GetMsgControlInstance()->ParseDownUploadFacePicMsg(pNormalMsg, &downloadFacePicMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseDownloadFacePicMsg failed.", __FUNCTION__);
				return -1;
			}
			char szDownloadFacePicMD5[MD5_SIZE]={0};
			GetSettingHandleInstance()->GetDownLoadFacePicMD5(szDownloadFacePicMD5, sizeof(szDownloadFacePicMD5));
			if(rl_strcmp(szDownloadFacePicMD5, downloadFacePicMsg.szMD5) != 0 && !rl_str_isempty(downloadFacePicMsg.szMD5))
			{	
				DOWNLOAD_SERVER_INFO downloadInfo;
				memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
				rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), downloadFacePicMsg.szUrl);
				rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), downloadFacePicMsg.szMD5);
				GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_DOWNLOAD_FACEPIC_XML, 0, 0, &downloadInfo, sizeof(DOWNLOAD_SERVER_INFO));
			}else
			{
				if(rl_str_isempty(downloadFacePicMsg.szMD5))
				{
					//GetSettingHandleInstance()->SetDownLoadFacePicMD5("");
					ipc_send(IPC_ID_PHONE, MSG_D2P_CLEAR_FACE_DATA, 1, 0, NULL, 0);
				}
			}
			
		}
		break;
	case MSG_TO_DEVICE_UPLOAD_FACE_PIC:
		{
			SOCKET_MSG_DOWNUPLOAD_FACE_PIC uploadFacePicMsg;
			if(GetMsgControlInstance()->ParseDownUploadFacePicMsg(pNormalMsg, &uploadFacePicMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseUploadFacePicMsg failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_UPLOAD_DEVICE_FACEID, 0, 0, (void *)uploadFacePicMsg.szUrl, rl_strlen((char *)uploadFacePicMsg.szUrl) + 1);
		}
		break;
#endif
#endif

	case MSG_TO_DEVICE_HEARBEAT_ACK:
		{
			GetConnectControlInstance()->SetHeartBeatAckFlag(TRUE);
			GetConnectControlInstance()->SetHeartBeatNewModeFlag(TRUE);
		}
		break;

#if 0
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	case MSG_DEVICE_TO_DEVICE_REQUEST_CONFIG:
		{
			SOCKET_MSG_CONFIG_FROM_DEVICE requestConfigMsg;
			if(GetMsgControlInstance()->ParseReqCfgFromDeviceMsg(pNormalMsg, &requestConfigMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReqCfgFromDeviceMsg failed.", __FUNCTION__);
				return -1;
			}
			GetDeviceControlInstance()->OnReqCfgFromDevice(&requestConfigMsg);
		}
		break;
	case MSG_FROM_DEVICE_REPORT_CONFIG:
		{
			SOCKET_MSG_CONFIG_FROM_DEVICE reportConfigMsg;
			if(GetMsgControlInstance()->ParseReqConfigMsg(pNormalMsg, &reportConfigMsg, nDataSize, TRUE) < 0)
			{
				return -1;
			}
			DCLIENT_CONFIG_MODULE configModule;
			rl_memset(&configModule, 0, sizeof(configModule));
			for(int i=0; i<DCLIENT_CONFIG_MODULE_ITEM_NUM; i++)
			{
				rl_strcpy_s(configModule.szItem[i], sizeof(configModule.szItem[i]), reportConfigMsg.module.szItem[i]);
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_CONFIG_FORM_DEVICE, 0, 0, &configModule, sizeof(DCLIENT_CONFIG_MODULE));
		}
		break;
#endif
	#if RL_SUPPORT_CONTROL4
	case MSG_CONTROL4_REMOTE_ACTION:
		{
			GetControlFourControlInstance()->OnControlFourActionMsg(pNormalMsg, pszRemoteAddr, nRemotePort);
		}
		break;
	case MSG_CONTROL4_SEND_CONTACT_INFO:
		{
			GetControlFourControlInstance()->OnControlFourContactMsg(pNormalMsg, pszRemoteAddr, nRemotePort);
		}
		break;
#endif
	case MSG_TO_DEVICE_MANAGE_ALARM:
		{				
			SOCKET_MSG_MANAGE_ALARM_MSG manageAlarmMsg;
			if(GetMsgControlInstance()->ParseManageAlarmMsg(pNormalMsg, &manageAlarmMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseManageAlarmMsg failed.", __FUNCTION__);
				return -1;
			}		
			GetDeviceControlInstance()->OnManageAlarmMsg(&manageAlarmMsg);
		}
		break;
	case MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE:
		{
			SOCKET_MSG_MAINTENANCE_SERVER_CHANGE maintenanceSrvChange;
			if(GetMsgControlInstance()->ParseMaintenaceServerChange(pNormalMsg, &maintenanceSrvChange, nDataSize) < 0)
			{
				rl_log_err("%s: ParseMaintenaceServerChange failed.", __FUNCTION__);
				return -1;
			}		
			GetDeviceControlInstance()->OnMaintenaceServerChange(&maintenanceSrvChange);
		}
		break;
	case MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER:
		{
			DCLIENT_SENSOR_TRIGGER sensorTriggerMsg;
			if(GetMsgControlInstance()->ParseRequestSensorTrigger(pNormalMsg, &sensorTriggerMsg, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestSensorTrigger failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_SENSOR_TRIGGER, 0, 0, &sensorTriggerMsg, sizeof(DCLIENT_SENSOR_TRIGGER));
		}
		break;
	case MSG_DEVICE_TO_DEVICE_REQUEST_ALL_TRIGGER:
		{
			SOCKET_MSG_REQUEST_ALL_TRIGGER requestAllTrigger;
			if(GetMsgControlInstance()->ParseRequestAllTrigger(pNormalMsg, &requestAllTrigger, nDataSize) < 0)
			{
				rl_log_err("%s: ParseRequestAllTrigger failed.", __FUNCTION__);
				return -1;
			}		
			GetDeviceControlInstance()->OnRequestAllTrigger(&requestAllTrigger);
			
		}
		break;
	case MSG_DEVICE_TO_DEVICE_REPORT_ALL_TRIGGER:
		{
			SOCKET_MSG_REPORT_ALL_TRIGGER reporttAllTrigger;
			if(GetMsgControlInstance()->ParseReportAllTrigger(pNormalMsg, &reporttAllTrigger, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReportAllTrigger failed.", __FUNCTION__);
				return -1;
			}		
			GetDeviceControlInstance()->OnReportAllTrigger(&reporttAllTrigger);
		}
		break;
#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
	case MSG_FROM_DEVICE_REPORT_DOOR_STATUS:
		{
			DCLIENT_REPORT_DOORSTATUS doorStatus;
			if(GetMsgControlInstance()->ParseReportDoorStatus(pNormalMsg, &doorStatus, nDataSize) < 0)
			{
				rl_log_err("%s: ParseReportDoorStatus failed.", __FUNCTION__);
				return -1;
			}		
			GetDeviceControlInstance()->OnRecvReportDoorStatus(&doorStatus);
		}
		break;
#endif	
#endif

	case MSG_TO_DEVICE_SERVER_HEARTBEAT:
		{
			GetDeviceControlInstance()->OnServerHeartBeatMsg();
		}
		break;

#if 0
	case MSG_TO_DEVICE_NOTIFY_VISITOR_AUTH:
		{
			DCLIENT_REPORT_VISITOR_AUTH_INFO reportVisitorAuth;
			if(GetMsgControlInstance()->ParseNotifyVisitorAuthMsg(pNormalMsg, &reportVisitorAuth, nDataSize) < 0)
			{
				rl_log_err("%s: ParseNotifyVisitorAuthMsg failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_NOTIFY_VISITOR_AUTH, 0, 0, &reportVisitorAuth, sizeof(DCLIENT_REPORT_VISITOR_AUTH_INFO));
		}
		break;
	case MSG_TO_DEVICE_FACE_DATA_FORWARD:
		{
			DCLIENT_FACE_DATA_FORWARD faceDataForward;
			if(GetMsgControlInstance()->ParseFaceDataForwardMsg(pNormalMsg, &faceDataForward, nDataSize) < 0)
			{
				rl_log_err("%s: ParseFaceDataForwardMsg failed.", __FUNCTION__);
				return -1;
			}
			GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_FACE_DATA, 0, 0, faceDataForward.szUrl, strlen(faceDataForward.szUrl) + 1);
			
		}
		break;
	case MSG_TO_DEVICE_SEND_TEMP_KEY:
		{
			DCLIENT_SEND_TEMP_KEY sendTempKey;
			if(GetMsgControlInstance()->ParseSendTempKeyMsg(pNormalMsg, &sendTempKey, nDataSize) < 0)
			{
				rl_log_err("%s: ParseSendTempKeyMsg failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_SEND_TEMP_KEY, 0, 0, &sendTempKey, sizeof(DCLIENT_SEND_TEMP_KEY));
		}
		break;
	case MSG_TO_DEVICE_SEND_OSS_STS:
		{
			rl_log_debug("On MSG_TO_DEVICE_SEND_OSS_STS");
			DCLIENT_SEND_OSS_STS sendOssSts;
			if(GetMsgControlInstance()->ParseSendOssStsMsg(pNormalMsg, &sendOssSts, nDataSize) < 0)
			{
				rl_log_err("%s: ParseSendOssStsMsg failed.", __FUNCTION__);
				return -1;
			}
			ipc_send(IPC_ID_PHONE, MSG_D2P_SEND_OSS_STS, 0, 0, &sendOssSts, sizeof(DCLIENT_SEND_OSS_STS));
		}
		break;
#endif

	default:
		break;
	}
			
	return 0;	
}

//处理REMOTE_CONTROL消息
int CControl::OnRemoteControlMsg(SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg)
{
	if(pRemoteControlMsg == NULL)
	{
		return -1;
	}

	if(rl_strcmp(pRemoteControlMsg->szType, SOCKET_MSG_TYPE_NAME_REBOOT) == 0)
	{
		//REBOOT, 需要先发送广播通知所有进程退出
		if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_CLOUD)
		{
			SOCKET_MSG socketMsg;
			SOCKET_MSG_AKCS_ACK akcsACKMsg;
			memset(&akcsACKMsg, 0, sizeof(SOCKET_MSG_AKCS_ACK));
			rl_strcpy_s(akcsACKMsg.szTraceID, sizeof(akcsACKMsg.szTraceID), pRemoteControlMsg->szItem[0]);
			rl_strcpy_s(akcsACKMsg.szType, sizeof(akcsACKMsg.szType), "Reboot");
			akcsACKMsg.nMsgID = 0x000A;
			if (GetMsgControlInstance()->BuildAKCSACKMsg(&socketMsg, &akcsACKMsg) < 0)
			{
				return -1;
			}
			if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
			{
				rl_log_err("Send TCP MSG:Reboot ACK failed.");
				return -1;
			}
			rl_log_info("Send TCP MSG:Reboot ACK success.");
		}
		rl_log_info("Device will reboot.");
		//GetUpgradeControlInstance()->Reboot();
	}
	else if (rl_strcmp(pRemoteControlMsg->szType, SOCKET_MSG_TYPE_NAME_MAKECALL) == 0)
	{
		rl_log_info("Device will make %s call from %s to %s.",pRemoteControlMsg->szMode,pRemoteControlMsg->szFrom,pRemoteControlMsg->szTo);
		//GetCallControlInstance()->MakeCall(pRemoteControlMsg->szFrom,pRemoteControlMsg->szTo,pRemoteControlMsg->szMode);
	}
	else if (rl_strcmp(pRemoteControlMsg->szType, SOCKET_MSG_TYPE_NAME_HANGUP) == 0)
	{
		rl_log_info("Device will hangup.");
		//GetCallControlInstance()->HangUp();
	}
	else if (rl_strcmp(pRemoteControlMsg->szType, SOCKET_MSG_TYPE_NAME_RESET_TO_FACTORY) == 0)
	{
		rl_log_info("Device will reset to factory.");
		//CSystemInterface::GetInstance()->ResetToFactory();
	}
	else if(rl_strcmp(pRemoteControlMsg->szType, SOCKET_MSG_TYPE_NAME_OPENDOOR) == 0)
	{
		rl_log_info("%s: open door.", __FUNCTION__);
		DCLIENT_REMOTE_CONTROL_OPENDOOR openDoor;
		rl_memset(&openDoor, 0, sizeof(DCLIENT_REMOTE_CONTROL_OPENDOOR));
		rl_strcpy_s(openDoor.szUserID, sizeof(openDoor.szUserID), pRemoteControlMsg->szItem[0]);		
		rl_strcpy_s(openDoor.szRelay, sizeof(openDoor.szRelay), pRemoteControlMsg->szItem[1]);
		rl_strcpy_s(openDoor.szTraceID, sizeof(openDoor.szTraceID), pRemoteControlMsg->szItem[2]);
		ipc_send(IPC_ID_PHONE, MSG_D2P_REMOTE_CONTROL_OPENDOOR, 0, 0, (void *)&openDoor, sizeof(DCLIENT_REMOTE_CONTROL_OPENDOOR));
	}
	else if(rl_strcmp(pRemoteControlMsg->szType, "OpenDoorSecurityRelay") == 0)
	{
		rl_log_info("%s: OpenDoorSecurityRelay.", __FUNCTION__);
		DCLIENT_REMOTE_CONTROL_OPENDOOR openDoor;
		rl_memset(&openDoor, 0, sizeof(DCLIENT_REMOTE_CONTROL_OPENDOOR));
		rl_strcpy_s(openDoor.szUserID, sizeof(openDoor.szUserID), pRemoteControlMsg->szItem[0]);		
		rl_strcpy_s(openDoor.szRelay, sizeof(openDoor.szRelay), pRemoteControlMsg->szItem[1]);
		rl_strcpy_s(openDoor.szTraceID, sizeof(openDoor.szTraceID), pRemoteControlMsg->szItem[2]);
		ipc_send(IPC_ID_PHONE, MSG_D2P_REMOTE_CONTROL_OPENDOOR, 1, 0, (void *)&openDoor, sizeof(DCLIENT_REMOTE_CONTROL_OPENDOOR));
	}
	else if(rl_strcmp(pRemoteControlMsg->szType, "RebootDclient") == 0)
	{
		rl_log_info("%s: this is not bug.RebootDclient.", __FUNCTION__);
		exit(0);
	}
	return 0;
}

int CControl::OnCommonTransferMsg(SOCKET_MSG_COMMON_TRANSFER *pCommonTransferMsg)
{
	if(pCommonTransferMsg == NULL)
	{
		return -1;
	}
	if(rl_strcmp(pCommonTransferMsg->szMsgType, SOCKET_MSG_TYPE_NAME_SEND_DTMF_SET) == 0)
	{
		DCLIENT_SET_DTMF dtmfSetMsg;
		memset(&dtmfSetMsg, 0, sizeof(DCLIENT_SET_DTMF));
		memcpy(&dtmfSetMsg, pCommonTransferMsg->szCommonBuf, sizeof(dtmfSetMsg));
		rl_strcpy_s(dtmfSetMsg.szFrom, sizeof(dtmfSetMsg.szFrom), pCommonTransferMsg->szFrom);
		rl_strcpy_s(dtmfSetMsg.szTo, sizeof(dtmfSetMsg.szTo), pCommonTransferMsg->szTo);
		ipc_send(IPC_ID_PHONE, MSG_D2P_SET_DTMF, 0, 0, (void *)&dtmfSetMsg, sizeof(DCLIENT_SET_DTMF));	
	}
	return 0;
}

//定时器消息处理
int CControl::OnTimer(UINT nIDEvent)
{
	if(nIDEvent == TIMER_ID_BASE)
	{
		GetConnectControlInstance()->ProcessBaseTimer();
		GetTextMessageControlInstance()->ProcessBaseTimer();
		GetAlarmControlInstance()->ProcessBaseTimer();
		GetDiscoverControlInstance()->ProcessBaseTimer();
	}

	return 0;
}

//Configurations be changed, it would be called when received IPC broadcast.
void CControl::OnConfigChanged(UINT nCfgId)
{
	//KILLSELF if the connection mode is changed
	int nLastMode = GetConnectControlInstance()->GetConnectMode();
	if(nLastMode >= 0)
	{
		int nNewMode = GetSettingHandleInstance()->GetConnectMode();
		if(nNewMode != nLastMode)
		{
			rl_log_err("%s: Server mode is changed from %d to %d. DCLIENT will restart.", __FUNCTION__, nLastMode, nNewMode);
			exit(0);
		}
	}
}

void CControl::OnRebootDclient()
{
	rl_log_err("%s: This is no bug, config change need DCLIENT restart.", __FUNCTION__);
	exit(0);
}

/*@function
*******************************************************************
功  能:  是否已经反初始化.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.29
******************************************************************/
bool CControl::IsUnInit()
{
	return m_bUnInit;
}
