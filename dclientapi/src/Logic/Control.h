#ifndef __CONTROL_H__
#define __CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include <map>
#include <string>
using namespace std;

class CControl
{
public:
	CControl();
	~CControl();
	
	static CControl *GetInstance();
	
	//初始化
	int Init();
	void UnInit();	//反初始化

	//运行
	int Run();

	//处理消息
	int ProcessMsg();
	//增加一个新的消息
	int AddMsg(unsigned int id, unsigned int wParam, unsigned int lParam, void *lpData, int nDataLen);
	//删除所有消息
	int DelAllMsg();

	//消息处理句柄
	int OnMessage(unsigned int msg, unsigned int wParam, unsigned int lParam, void *lpData);

	int OnSocketMsg(SOCKET_MSG *pRecvMsg);

	//处理REMOTE_CONTROL消息
	int OnRemoteControlMsg(SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg);

	int OnCommonTransferMsg(SOCKET_MSG_COMMON_TRANSFER *pCommonTransferMsg);

	//控制消息处理句柄
	int OnCtrl(UINT msg, UINT wParam, UINT lParam, void *lpData);

	//定时器消息处理
	int OnTimer(UINT nIDEvent);

	//Configurations be changed, it would be called when received IPC broadcast.
	void OnConfigChanged(UINT nCfgId);

	void OnRebootDclient();

	bool IsUnInit();

private:
	virtual void* TimerLoop();

private:
	void Lock();
	void Unlock();
	void SetWaitEvent();
	void ResetWaitEvent();
	void WaitForEvent();
	void SetUninitFlag(bool bUnInit);

	map<string, map<string, string>> ParseConfigFile(const char* pstrConfigFile);
	string TrimBom(const string& strLine);

private:
	static CControl *instance;
	pthread_t m_tidTimer;
	pthread_t m_tidProcMsg;

	void *m_msgHeader;
	void *m_lock;
	void *m_wait;

	bool m_bUnInit;
	bool m_bRuning;
};

CControl *GetControlInstance();

#endif
