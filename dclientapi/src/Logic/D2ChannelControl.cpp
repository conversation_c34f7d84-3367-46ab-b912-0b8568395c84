
#include "Utility.h"
#include "Lock.h"
#include "WaitEvent.h"
#include "D2ChannelControl.h"
#include "SettingControl.h"
#include "ConnectControl.h"
#include "SettingHandle.h"
#include "AES256.h"
#include "SettingHandle.h"
#include "DclientDefine.h"

void *D2ChannelThread(void *arg)
{
	CD2ChannelControl *pD2ChannelControl = (CD2ChannelControl *)arg;
	pthread_detach(pthread_self());
	if(pD2ChannelControl == NULL)
	{		
		return NULL;
	}
	while(true)
	{
		if(pD2ChannelControl->GetEnable2Channel())
		{
			pD2ChannelControl->D2ChannelHttpsRequest();
			sleep(pD2ChannelControl->m_nHttpExpire);			
		}
		sleep(1);
	}
	return NULL;
}

void *ProcessD2ChannelThread(void *arg)
{
	CD2ChannelControl *pD2ChannelControl = (CD2ChannelControl*)arg;
	pthread_detach(pthread_self());
	if(pD2ChannelControl == NULL)
	{		
		return NULL;
	}
	while(true)
	{
		pD2ChannelControl->ProcessMsg();
	}
	return NULL;
}

CD2ChannelControl *GetD2ChannelControlInstance()
{
	return CD2ChannelControl::GetInstance();
}

CD2ChannelControl::CD2ChannelControl()
{
	m_bEnable2Channel = FALSE;
	m_bEnableMtceAlarm = FALSE;
	m_nHttpExpire = 3600;
	memset(m_szHttpsUrl, 0, sizeof(m_szHttpsUrl));
	memset(m_szMAC, 0, sizeof(m_szMAC));
	m_msgHeader = NULL;
	m_lock = new CLock();
	m_wait = new CWaitEvent();
	m_bClearCfgFlag = FALSE;
}

CD2ChannelControl::~CD2ChannelControl()
{
	DelAllMsg();

	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
	if(NULL != m_wait)
	{
		delete (CWaitEvent *)m_wait;
		m_wait = NULL;
	}
}

int CD2ChannelControl::Init()
{
	char szTmpMac[MAC_SIZE] = {0};
	GetSettingHandleInstance()->GetMAC(szTmpMac, sizeof(szTmpMac));
	StrtokString(szTmpMac, m_szMAC, sizeof(szTmpMac), ":");
	srand((unsigned int)time(0));
	pthread_create(&m_tidD2Channel, NULL, D2ChannelThread, this);
	pthread_create(&m_tidProcMsg, NULL, ProcessD2ChannelThread, this);	
	return 0;
}

int CD2ChannelControl::ProcessBaseTimer()
{
	static int i=0;
	static int nSipStatus = DCLIENT_SIP_STATUS_NORMAL;
	static int nDclientMode = DOORSETTING_CONNECT_SERVER_MODE_CLOUD;
	static int nDclientConnStatus = TCP_CONNECT_STATUS_CONNECT;
	BOOL bOneSecondFlag = FALSE;
	BOOL bTenMinuteFlag = FALSE;
	BOOL bOneMinuteFlag = FALSE;
	if(++i % 1 == 0)
	{
		bOneSecondFlag = TRUE;
		if(i % 60 == 0)
		{
			bOneMinuteFlag = TRUE;
			if(i % 600 == 0)	
			{				
				bTenMinuteFlag = TRUE;							
			}
		}
	}	
	if(nSipStatus != DCLIENT_SIP_STATUS_NORMAL && bOneMinuteFlag)
	{
		nSipStatus = CheckSIPStatus();
		if(nSipStatus == DCLIENT_SIP_STATUS_EMPTY)
		{
			char szTmp[URL_SIZE] = {0};
			rl_sprintf_s(szTmp, sizeof(szTmp), "SIP account is empty.");
			GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_SIP_ACCOUNT_EMPTY, 0, 0, szTmp, rl_strlen(szTmp)+1);
		}
		else if(nSipStatus == DCLIENT_SIP_STATUS_REG_FAILED)
		{
			char szTmp[URL_SIZE] = {0};
			rl_sprintf_s(szTmp, sizeof(szTmp), "SIP reg failed");
			GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_SIP_REG_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
		}
		nSipStatus = DCLIENT_SIP_STATUS_NORMAL;
	}
	if(nDclientMode != DOORSETTING_CONNECT_SERVER_MODE_CLOUD && bOneMinuteFlag)
	{
		nDclientMode = CheckDclientMode();
		if(nDclientMode != DOORSETTING_CONNECT_SERVER_MODE_CLOUD)
		{
			char szTmp[URL_SIZE] = {0};
			rl_sprintf_s(szTmp, sizeof(szTmp), "DCLIENT mode err,the mode is:%d", nDclientMode);
			GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_DCLIENT_MODE_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
		}
		nDclientMode = DOORSETTING_CONNECT_SERVER_MODE_CLOUD;
	}
	if(nDclientConnStatus != TCP_CONNECT_STATUS_CONNECT && bOneMinuteFlag)
	{
		nDclientConnStatus = CheckDclientConnStatus();
		if(nDclientConnStatus != TCP_CONNECT_STATUS_CONNECT)
		{
			char szTmp[URL_SIZE] = {0};
			rl_sprintf_s(szTmp, sizeof(szTmp), "DCLIENT conn err.");
			GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_DCLIENT_CONN_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
		}
		nDclientConnStatus = TCP_CONNECT_STATUS_CONNECT;
	}
	if(bTenMinuteFlag)
	{
		nSipStatus = CheckSIPStatus();
		nDclientMode = CheckDclientMode();
		nDclientConnStatus = CheckDclientConnStatus();
	}
	return 0;
}


//上锁消息缓冲区
void CD2ChannelControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁消息缓冲区
void CD2ChannelControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}

//设置事件
void CD2ChannelControl::SetWaitEvent()
{
	((CWaitEvent *)m_wait)->Set();
}

//清除事件
void CD2ChannelControl::ResetWaitEvent()
{
	((CWaitEvent *)m_wait)->Reset();
}

//等待事件触发
void CD2ChannelControl::WaitForEvent()
{
	((CWaitEvent *)m_wait)->Wait();
}


//处理消息
int CD2ChannelControl::ProcessMsg()
{
	WaitForEvent();
	Lock();
	MESSAGE *tmpNode = NULL;

	while(m_msgHeader != NULL)
	{
		tmpNode = (MESSAGE *)m_msgHeader;
		m_msgHeader = ((MESSAGE *)m_msgHeader)->next;
		Unlock();
		OnMessage(tmpNode->id, tmpNode->wParam, tmpNode->lParam, tmpNode->lpData);
		Lock();
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}
		delete(tmpNode);
	}

	m_msgHeader = NULL;

	ResetWaitEvent();

	Unlock();

	return 0;
}

//增加一个新的消息
int CD2ChannelControl::AddMsg(UINT id, UINT wParam, UINT lParam, void *lpData, int nDataLen)
{
	Lock();
	MESSAGE *curNode = NULL;
	MESSAGE *newNode = new MESSAGE();
	if(NULL == newNode)
	{
		Unlock();
		return -1;
	}

	memset(newNode, 0, sizeof(MESSAGE));

	newNode->id = id;
	newNode->wParam = wParam;
	newNode->lParam = lParam;
	if((lpData != NULL) && (nDataLen > 0))
	{
		newNode->lpData = new char[nDataLen];
		memcpy(newNode->lpData, lpData, nDataLen);
	}

	if(m_msgHeader == NULL)
	{
		m_msgHeader = newNode;
	}
	else
	{
		curNode = (MESSAGE *)m_msgHeader;
		while((curNode != NULL) && (curNode->next != NULL))
		{
			curNode = curNode->next;
		}
		curNode->next = newNode;
	}
	SetWaitEvent();
	Unlock();

	return 0;
}


//删除所有消息
int CD2ChannelControl::DelAllMsg()
{
	Lock();

	MESSAGE *curNode = NULL;
	MESSAGE *tmpNode = NULL;

	curNode = (MESSAGE *)m_msgHeader;

	while(curNode != NULL)
	{
		tmpNode = curNode;
		curNode = curNode->next;
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}

		delete tmpNode;
	}

	m_msgHeader = NULL;

	Unlock();

	return 0;
}

//消息处理句柄
VOID CD2ChannelControl::OnMessage(UINT msg, UINT wParam, UINT lParam, void *lpData)
{
	DCLIENT_MAINTENANCE_ALARM_REPORT alarmReport;
	rl_memset(&alarmReport, 0, sizeof(DCLIENT_MAINTENANCE_ALARM_REPORT));
	time_t timeStamp = time(NULL);
	rl_sprintf_s(alarmReport.szTimeStamp, sizeof(alarmReport.szTimeStamp), "%d", timeStamp);
	rl_strcpy_s(alarmReport.szInfo, sizeof(alarmReport.szInfo), (char*)lpData);	
	int nRand = (1 + rand()%0xFFFFFFF0);
	rl_sprintf_s(alarmReport.szTraceID, sizeof(alarmReport.szTraceID), "%.10d", nRand);
	rl_strcpy_s(alarmReport.szModule, sizeof(alarmReport.szModule), DCLIENT2_ALARM_MODULE_DCLIENT);	
			
	int nMsgType = msg;
	switch(nMsgType)
	{
		case DCLIENT2_ALARM_MSG_PRIKEY_DW_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_RPIKEY_DW_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);			
		}
		break;
		case DCLIENT2_ALARM_MSG_RFCARD_DW_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_RFCARD_DW_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_CONFIG_DW_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_CONFIG_DW_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_CONTACT_DW_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_CONTACT_DW_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_CONFIG_PA_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_CONFIG_PA_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_SIP_ACCOUNT_EMPTY:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_SIP_ACCOUNT_EMPTY;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_SIP_REG_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_SIP_REG_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		case DCLIENT2_ALARM_MSG_DCLIENT_MODE_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_DCLIENT_MODE_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			if(GetConnectControlInstance()->GetConnectOriFrom() != TCP_CONNECT_ORI_FROM_SDMC && 
				GetConnectControlInstance()->GetTcpConnectStatus() == TCP_CONNECT_STATUS_CONNECT)
			{
				D2MaintenanceAlarmHttpRequest(&alarmReport, TRUE);	//如果设备TCP连接，但是连接地址不是从SDMC那边获取的，则强制上报
			}
			else
			{
				D2MaintenanceAlarmHttpRequest(&alarmReport);	
			}
		}
		break;
		case DCLIENT2_ALARM_MSG_DCLIENT_CONN_FAILED:
		{
			alarmReport.nCode = DCLIENT2_ALARM_CODE_DCLIENT_CONN_FAILED;
			alarmReport.nLevel = DCLIENT2_ALARM_LEVEL_FAULT;
			D2MaintenanceAlarmHttpRequest(&alarmReport);	
		}
		break;
		default:
			break;
	}
}

int CD2ChannelControl::SetEnable2Channel(bool bFlag)
{
	m_bEnable2Channel = bFlag;
	return 0;
}

int CD2ChannelControl::GetEnable2Channel()
{
	return m_bEnable2Channel;
}

int CD2ChannelControl::SetEnableMtceAlarm(bool bFlag)
{
	m_bEnableMtceAlarm = bFlag;
	return 0;
}

int CD2ChannelControl::GetEnableMtceAlarm()
{
	return m_bEnableMtceAlarm;
}

int CD2ChannelControl::SetHttpExpire(int nExpire)
{
	if(nExpire <= 0 || nExpire > 5*3600)
	{
		nExpire = 3600;
	}
	m_nHttpExpire = nExpire;
	return 0;
}

int CD2ChannelControl::D2ChannelHttpsRequest()
{
	if(rl_str_isempty(m_szHttpsUrl))
	{
		rl_sprintf_s(m_szHttpsUrl, sizeof(m_szHttpsUrl), "%s%s", DCLIENT_2_CHANNEL_DEFAULT_HTTPS_URL, m_szMAC);
	}
	//CHAR szRecvBuf[1024] = {0};
	DCLIENT_CURL_HTTP_REQUEST curlHttpRequest;
	memset(&curlHttpRequest, 0, sizeof(DCLIENT_CURL_HTTP_REQUEST));
	curlHttpRequest.nAuthMethod = HTTP_AUTH_METHOD_DIGEST;
	curlHttpRequest.nRequestMethod = HTTP_REQUEST_METHOD_GET;
	//curlHttpRequest.pRecvBuf = szRecvBuf;
	curlHttpRequest.pUrl = m_szHttpsUrl;
	
	//获取用户和密码
	char szPasswd[VALUE_SIZE] = {0};	
	rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", m_szMAC, AES_KEY_V1_MASK);
	//对密码进行MD5计算
	CHAR szPasswdMD5[MD5_SIZE] = {0};
	MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
	CHAR szUser[VALUE_SIZE] = {0};
	rl_strcpy_s(szUser, sizeof(szUser), "dclient");
	curlHttpRequest.pAuthUser = szUser;
	curlHttpRequest.pAuthPassword = szPasswdMD5;
	if((SendRequestUrlByCurl(curlHttpRequest) < 0))
	{
		rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
		//释放内存
		RL_FREE(curlHttpRequest.pRecvBuf);
		RL_FREE(curlHttpRequest.pHeadRecvBuf);
		return -1;
	}
	DCLIENT_D2CHANNEL_RESPONSE d2ChannelResponse;
	memset(&d2ChannelResponse, 0, sizeof(DCLIENT_D2CHANNEL_RESPONSE));
	ParseD2ChannelHttpsResponse(curlHttpRequest.pRecvBuf, &d2ChannelResponse);
	OnD2ChannelHttpsResponse(&d2ChannelResponse);
	//释放内存
	RL_FREE(curlHttpRequest.pRecvBuf);
	RL_FREE(curlHttpRequest.pHeadRecvBuf);
	return 0;
}

int CD2ChannelControl::ParseD2ChannelHttpsResponse(char *pszResponseData, DCLIENT_D2CHANNEL_RESPONSE *pD2ChannelResponse)
{
	if(pszResponseData == NULL || pD2ChannelResponse == NULL)
	{
		return -1;
	}	
	cJSON *root = NULL;   
	cJSON *array = NULL;    
	cJSON *item = NULL;
	root = cJSON_Parse(pszResponseData); 
	if (!root) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		return -1;
	}
	item=cJSON_GetObjectItem(root, "cmd");
	if(item)
	{
		rl_strcpy_s(pD2ChannelResponse->szCmd, sizeof(pD2ChannelResponse->szCmd), item->valuestring);
	}
	array = cJSON_GetObjectItem(root, "param");
	if(array)
	{
		int nArraySize = cJSON_GetArraySize(array);
		pD2ChannelResponse->nParamCount = nArraySize;
		for(int i=0; i<nArraySize; i++)
		{
			if(i >= DCLIENT_D2CHANNEL_PARAM_COUNT_MAX)
			{
				break;
			}
			item=cJSON_GetArrayItem(array, i);
			if(item)
			{
				rl_strcpy_s(pD2ChannelResponse->szParam[i], sizeof(pD2ChannelResponse->szParam[i]), item->valuestring);
			}
		}
	}
	cJSON_Delete(root);
	return 0;
}

int CD2ChannelControl::OnD2ChannelHttpsResponse(DCLIENT_D2CHANNEL_RESPONSE *pD2ChannelResponse)
{
	if(pD2ChannelResponse == NULL)
	{
		return -1;
	}	
	if(!rl_strcmp(pD2ChannelResponse->szCmd, DCLIENT_2_CHANNEL_CMD_KEEPALIVE))
	{
		SetHttpExpire(rl_atoi(pD2ChannelResponse->szParam[0]));
	}
	else if(!rl_strcmp(pD2ChannelResponse->szCmd, DCLIENT_2_CHANNEL_CMD_MAINTENANCE))
	{
		SetHttpExpire(rl_atoi(pD2ChannelResponse->szParam[0]));
	}
	else if(!rl_strcmp(pD2ChannelResponse->szCmd, DCLIENT_2_CHANNEL_CMD_CONSOLE))
	{
#if 0
		FILE *fp = NULL;
		if(!rl_str_isempty(pD2ChannelResponse->szParam[0]))
		{
			fp = popen(pD2ChannelResponse->szParam[0], "r");
			if(!fp)
			{
				rl_log_err(" %s  %s failed",__FUNCTION__,pD2ChannelResponse->szParam[0]);
				return -1;
			}
			pclose(fp);	
		}
#endif
		if(!rl_strcmp(pD2ChannelResponse->szParam[0], "reboot"))
		{
			//CSystemInterface::GetInstance()->Reboot();
		}
		else if(!rl_strcmp(pD2ChannelResponse->szParam[0], "reset"))
		{
			//CSystemInterface::GetInstance()->ResetToFactory();
		}
		else if(!rl_strcmp(pD2ChannelResponse->szParam[0], "killself"))
		{
			exit(0);
		}
	}
	else if(!rl_strcmp(pD2ChannelResponse->szCmd, DCLIENT_2_CHANNEL_CMD_CONFIG))
	{
		CONFIG_MODULE configModule;
		memset(&configModule, 0, sizeof(configModule));
		for(int i=0; i<pD2ChannelResponse->nParamCount; i++)
		{
			rl_strcpy_s(configModule.szItem[i], sizeof(configModule.szItem[i]), pD2ChannelResponse->szParam[i]);
		}
		//更新配置
		if(GetSettingControlInstance()->SetConfigModule(&configModule) < 0)
		{
			rl_log_err("%s: SetConfigModule failed.", __FUNCTION__);
			return -1;
		}
	}
	return 0;
}

int CD2ChannelControl::D2MaintenanceAlarmHttpRequest(DCLIENT_MAINTENANCE_ALARM_REPORT *pData, BOOL bForce)
{
	if(pData == NULL)
	{
		return -1;
	}
	if(!bForce && (!m_bEnableMtceAlarm || GetSettingHandleInstance()->GetMaintenanceAlarm() == 0))//看开关是否打开，或者是否强制要上报
	{
		rl_log_err("Alarm channel not open.");
		return -1;
	}
	rl_log_info("%s: code=%d,trancseid=%s,info=%s",__FUNCTION__, pData->nCode, pData->szTraceID, pData->szInfo);
	char szHttpsUrl[URL_SIZE] = {0};
	rl_sprintf_s(szHttpsUrl, sizeof(szHttpsUrl), "%s", DCLIENT_NETWORK_MANAGE_ALARM_HTTPS_URL);
	char szHeadBuf[VALUE_SIZE] = {0};
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"api-version: 1.0");
	
	//构建post的JSON数据
	char szPostBuf[1024] = {0};		
	cJSON *root;
    cJSON *param;
	cJSON *param1;
    root = cJSON_CreateObject();//创建一个json对象
    param = cJSON_CreateArray();//创建一个数组
    param1 = cJSON_CreateObject();	
	cJSON_AddNumberToObject(param1, "code", pData->nCode);
	cJSON_AddStringToObject(param1, "alarm_time", pData->szTimeStamp);
	cJSON_AddStringToObject(param1, "module", pData->szModule);
	cJSON_AddNumberToObject(param1, "level", pData->nLevel);
	cJSON_AddStringToObject(param1, "trace_id", pData->szTraceID);
	cJSON_AddStringToObject(param1, "log", pData->szInfo);
	cJSON_AddItemToArray(param, param1);
	
	cJSON_AddStringToObject(root, "id", m_szMAC);
	cJSON_AddNumberToObject(root, "type", DCLIENT2_ALARM_TYPE);
	char szAccessIP[VALUE_SIZE] = {0};
	GetSettingHandleInstance()->GetCloudServerIp(szAccessIP, sizeof(szAccessIP));
	cJSON_AddStringToObject(root, "server_ip", szAccessIP);
	char szFirmware[VALUE_SIZE] = {0};
	GetSettingHandleInstance()->GetSWVer(szFirmware, sizeof(szFirmware));
	cJSON_AddStringToObject(root, "firmware", szFirmware);
	cJSON_AddItemToObject(root, "param", param);
	rl_sprintf_s(szPostBuf, sizeof(szPostBuf), "%s", cJSON_Print(root));
	cJSON_Delete(root);
	
	//char szRecvBuf[1024] = {0};
	DCLIENT_CURL_HTTP_REQUEST curlHttpRequest;
	memset(&curlHttpRequest, 0, sizeof(DCLIENT_CURL_HTTP_REQUEST));
	curlHttpRequest.nAuthMethod = HTTP_AUTH_METHOD_DIGEST;
	curlHttpRequest.nRequestMethod = HTTP_REQUEST_METHOD_POST;
	curlHttpRequest.nContentType = HTTP_REQUEST_CONTENT_TYPE_JSON;
	//curlHttpRequest.pRecvBuf = szRecvBuf;
	curlHttpRequest.pUrl = szHttpsUrl;
	curlHttpRequest.pHeadData = szHeadBuf;
	curlHttpRequest.pPostData = szPostBuf;
	//获取用户和密码
	char szPasswd[VALUE_SIZE] = {0};	
	rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", m_szMAC, AES_KEY_V1_MASK);
	//对密码进行MD5计算
	CHAR szPasswdMD5[MD5_SIZE] = {0};
	MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
	CHAR szUser[VALUE_SIZE] = {0};
	rl_strcpy_s(szUser, sizeof(szUser), "dclient");
	curlHttpRequest.pAuthUser = szUser;
	curlHttpRequest.pAuthPassword = szPasswdMD5;
	if((SendRequestUrlByCurl(curlHttpRequest) < 0))
	{
		rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
		//释放内存
		RL_FREE(curlHttpRequest.pRecvBuf);
		RL_FREE(curlHttpRequest.pHeadRecvBuf);
		return -1;
	}
	//释放内存
	RL_FREE(curlHttpRequest.pRecvBuf);
	RL_FREE(curlHttpRequest.pHeadRecvBuf);
	return 0;
}

int CD2ChannelControl::SendWakeWord(DCLIENT_SEND_WAKE_WORD *lpData)
{
	if(lpData == NULL)
	{
		return -1;
	}
	if(!GetEnable2Channel())
	{
		return -1;
	}
	char szHttpsUrl[URL_SIZE] = {0};
	rl_sprintf_s(szHttpsUrl, sizeof(szHttpsUrl), "%s%s", DCLIENT_MAINTENANCE_URL, REPORT_ALBERT_RECORD_API);
	//char szRecvBuf[1024] = {0};
	DCLIENT_CURL_HTTP_REQUEST curlHttpRequest;
	memset(&curlHttpRequest, 0, sizeof(DCLIENT_CURL_HTTP_REQUEST));
	curlHttpRequest.nAuthMethod = HTTP_AUTH_METHOD_DIGEST;
	curlHttpRequest.nRequestMethod = HTTP_REQUEST_METHOD_POST;
	curlHttpRequest.nContentType = HTTP_REQUEST_CONTENT_TYPE_JSON;
	//curlHttpRequest.pRecvBuf = szRecvBuf;
	curlHttpRequest.pUrl = szHttpsUrl;
	curlHttpRequest.pPostData = lpData->szWakeWord;
	//获取用户和密码
	char szPasswd[VALUE_SIZE] = {0};	
	rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", m_szMAC, AES_KEY_V1_MASK);
	//对密码进行MD5计算
	CHAR szPasswdMD5[MD5_SIZE] = {0};
	MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
	CHAR szUser[VALUE_SIZE] = {0};
	rl_strcpy_s(szUser, sizeof(szUser), "dclient");
	curlHttpRequest.pAuthUser = szUser;
	curlHttpRequest.pAuthPassword = szPasswdMD5;
	if((SendRequestUrlByCurl(curlHttpRequest) < 0))
	{
		rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
		//释放内存
		RL_FREE(curlHttpRequest.pRecvBuf);
		RL_FREE(curlHttpRequest.pHeadRecvBuf);
		return -1;
	}
	//通知Phone，已经发送成功
#ifdef RL_GLOBAL_SUPPORT_ALBERT
	ipc_send(IPC_ID_ALBERT, MSG_D2P_SEND_WAKE_WORD_ACK, 0, lpData->nSeq, NULL, 0);
#else
	ipc_send(IPC_ID_PHONE, MSG_D2P_SEND_WAKE_WORD_ACK, 0, lpData->nSeq, NULL, 0);
#endif
	//释放内存
	RL_FREE(curlHttpRequest.pRecvBuf);
	RL_FREE(curlHttpRequest.pHeadRecvBuf);
	return 0;
}

int CD2ChannelControl::CheckSIPStatus()
{
	int nSipStatus = GetSettingHandleInstance()->GetDeviceSipRegStatus();
	char szSipUser[VALUE_SIZE] = {0};
	GetSettingHandleInstance()->GetDeviceSipUser(szSipUser, sizeof(szSipUser));
	if(!m_bClearCfgFlag)
	{
		if(rl_str_isempty(szSipUser))
		{
			return 0;
		}
		else if(nSipStatus == 0)
		{
			//return DCLIENT_SIP_STATUS_REG_FAILED;
			return 0;
		}
	}
	else
	{
		return 0;
	}	
	return 0;
}

int CD2ChannelControl::CheckDclientMode()
{
	return GetSettingHandleInstance()->GetConnectMode();
}

int CD2ChannelControl::CheckDclientConnStatus()
{
	return GetConnectControlInstance()->GetTcpConnectStatus();
}

VOID CD2ChannelControl::SetClearCfgFlag(BOOL bFlag)
{
	m_bClearCfgFlag = bFlag;
}

CD2ChannelControl *CD2ChannelControl::instance = NULL;

CD2ChannelControl *CD2ChannelControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CD2ChannelControl();
	}

	return instance;
}


