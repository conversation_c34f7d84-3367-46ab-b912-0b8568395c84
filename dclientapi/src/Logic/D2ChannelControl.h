#ifndef __D2CHANNEL_CONTROL_H__
#define __D2CHANNEL_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "dclient_ipc.h"
#include "cJSON.h"

#define DCLIENT_2_CHANNEL_DEFAULT_HTTPS_URL	"https://maintenance.akuvox.com:8483/dclient2channel?mac="
#define DCLIENT_NETWORK_MANAGE_ALARM_HTTPS_URL	"https://maintenance.akuvox.com:8483/reportAlarm"

#define DCLIENT_MAINTENANCE_URL	"https://maintenance.akuvox.com:8483"
#define REPORT_ALBERT_RECORD_API "/reportAlbertRecord"


#define DCLIENT_2_CHANNEL_CMD_KEEPALIVE	 "keepalive"
#define DCLIENT_2_CHANNEL_CMD_MAINTENANCE	 "maintenance"
#define DCLIENT_2_CHANNEL_CMD_CONSOLE	 "console"
#define DCLIENT_2_CHANNEL_CMD_CONFIG	 "config"

#define DCLIENT2_ALARM_MSG_SIP_REG_FAILED		0x0101
#define DCLIENT2_ALARM_MSG_SIP_ACCOUNT_EMPTY	0x0102

#define DCLIENT2_ALARM_MSG_PRIKEY_DW_FAILED		0x0201
#define DCLIENT2_ALARM_MSG_RFCARD_DW_FAILED		0x0202
#define DCLIENT2_ALARM_MSG_CONTACT_DW_FAILED	0x0203
#define DCLIENT2_ALARM_MSG_CONFIG_DW_FAILED		0x0204
#define DCLIENT2_ALARM_MSG_PRIKEY_PA_FAILED		0x0211
#define DCLIENT2_ALARM_MSG_RFCARD_PA_FAILED		0x0212
#define DCLIENT2_ALARM_MSG_CONTACT_PA_FAILED	0x0213
#define DCLIENT2_ALARM_MSG_CONFIG_PA_FAILED		0x0214

#define DCLIENT2_ALARM_MSG_DCLIENT_CONN_FAILED	0x0301
#define DCLIENT2_ALARM_MSG_DCLIENT_MODE_FAILED	0x0302

#define DCLIENT2_ALARM_MSG_PROCESS_ABNORMAL		0x0401
#define DCLIENT2_ALARM_MSG_CALL_FAILED			0x0403
#define DCLIENT2_ALARM_MSG_OPENDOOR_FAILED		0x0404

#define DCLIENT2_ALARM_CODE_SIP_REG_FAILED		101
#define DCLIENT2_ALARM_CODE_SIP_ACCOUNT_EMPTY	102
#define DCLIENT2_ALARM_CODE_RPIKEY_DW_FAILED	201
#define DCLIENT2_ALARM_CODE_RFCARD_DW_FAILED	202
#define DCLIENT2_ALARM_CODE_CONTACT_DW_FAILED	203
#define DCLIENT2_ALARM_CODE_CONFIG_DW_FAILED	204
#define DCLIENT2_ALARM_CODE_RPIKEY_PA_FAILED	211
#define DCLIENT2_ALARM_CODE_RFCARD_PA_FAILED	212
#define DCLIENT2_ALARM_CODE_CONTACT_PA_FAILED	213
#define DCLIENT2_ALARM_CODE_CONFIG_PA_FAILED	214
#define DCLIENT2_ALARM_CODE_DCLIENT_CONN_FAILED	301
#define DCLIENT2_ALARM_CODE_DCLIENT_MODE_FAILED	302


#define DCLIENT2_ALARM_LEVEL_FAULT	0
#define DCLIENT2_ALARM_LEVEL_ERROR	1
#define DCLIENT2_ALARM_LEVEL_WARN	2
#define DCLIENT2_ALARM_LEVEL_INFO	3

#define DCLIENT2_ALARM_MODULE_DCLIENT	"DCLIENT"
#define DCLIENT2_ALARM_MODULE_PHONE		"PHONE"

#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX ||  DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID)
#define DCLIENT2_ALARM_TYPE		2
#else
#define DCLIENT2_ALARM_TYPE		1
#endif

typedef enum
{
	DCLIENT_SIP_STATUS_NORMAL = 0,
	DCLIENT_SIP_STATUS_EMPTY,
	DCLIENT_SIP_STATUS_REG_FAILED,
}DCLIENT_SIP_STATUS;
	
typedef struct DCLIENT_D2CHANNEL_RESPONSE_T
{
#define DCLIENT_D2CHANNEL_CMD_SIZE	64
#define DCLIENT_D2CHANNEL_PARAM_COUNT_MAX	5
#define DCLIENT_D2CHANNEL_PARAM_VALUE_SIZE	256
	INT nParamCount;
	CHAR szCmd[DCLIENT_D2CHANNEL_CMD_SIZE];
	CHAR szParam[DCLIENT_D2CHANNEL_PARAM_COUNT_MAX][DCLIENT_D2CHANNEL_PARAM_VALUE_SIZE];
}DCLIENT_D2CHANNEL_RESPONSE;

class CD2ChannelControl
{
public:
	CD2ChannelControl();
	~CD2ChannelControl();	
	
	static CD2ChannelControl *GetInstance();
	int SetEnable2Channel(bool bFlag);
	int GetEnable2Channel();
	int Init();
	int ProcessMsg();
	int ProcessBaseTimer();
	int AddMsg(unsigned int id, unsigned int wParam, unsigned int lParam, void *lpData, int nDataLen);
	int DelAllMsg();
	VOID OnMessage(UINT msg, UINT wParam, UINT lParam, void *lpData);
	int D2ChannelHttpsRequest();
	int SetHttpExpire(int nExpire);
	int ParseD2ChannelHttpsResponse(char *pszResponseData, DCLIENT_D2CHANNEL_RESPONSE *pD2ChannelResponse);
	int OnD2ChannelHttpsResponse(DCLIENT_D2CHANNEL_RESPONSE *pD2ChannelResponse);
	int D2MaintenanceAlarmHttpRequest(DCLIENT_MAINTENANCE_ALARM_REPORT *pData, BOOL bForce=FALSE);
	int SetEnableMtceAlarm(bool bEnable);
	int GetEnableMtceAlarm();
	VOID SetClearCfgFlag(BOOL bFlag);
	int CheckSIPStatus();
	int CheckDclientMode();
	int CheckDclientConnStatus();
	int SendWakeWord(DCLIENT_SEND_WAKE_WORD *lpData);
private:
	static CD2ChannelControl *instance;
	void Lock();
	void Unlock();
	void SetWaitEvent();
	void ResetWaitEvent();
	void WaitForEvent();		

private:
	pthread_t m_tidD2Channel;
	pthread_t m_tidProcMsg;
	void *m_msgHeader;
	void *m_lock;
	void *m_wait;
	bool m_bClearCfgFlag;
public:
	bool m_bEnable2Channel;
	int m_nHttpExpire;
	bool m_bEnableMtceAlarm;
	CHAR m_szHttpsUrl[URL_SIZE];
	CHAR m_szMAC[MAC_SIZE];
	

};

CD2ChannelControl *GetD2ChannelControlInstance();

#endif

