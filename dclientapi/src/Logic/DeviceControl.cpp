#include "MsgControl.h"
#include "ConnectControl.h"
#include "DeviceControl.h"
#include "SettingHandle.h"
#include "dclient_ipc.h"
#include "DclientDefine.h"
#include "Utility.h"
#include "AES256.h" 
#include "DownloadControl.h"
#include "Def.h"

#define CONTENT_TYPE	"content-type"
#define CONTENT_TYPE_FORM	"application/x-www-form-urlencoded"
#define X_AUTH_TOKEN		"x-auth-token"

#define APACHE_VERSION	"apache-v3.0"
#define ROUTE_INTERFACE_PERSONAL_REGISTER	"personregisterfrom3"
#define ROUTE_INTERFACE_PERSONAL_LOGIN		"personloginfrom3"
#define ROUTE_INTERFACE_ADD_PERSONAL_USER	"addpersonaluserfrom3"
#define ROUTE_INTERFACE_GET_PERSONAL_USER	"getpersonaluserfrom3"
#define ROUTE_INTERFACE_DEL_PERSONAL_USER	"deletepersonaluserfrom3"
#define ROUTE_INTERFACE_ADD_PERSONAL_DEVICE	"addpersonaldevicefrom3"
#define ROUTE_INTERFACE_CHECK_PERSONAL_EMAIL "personcheckemailfrom3"
#define ROUTE_INTERFACE_PERSONAL_USER_CONF 	 "personalusercnf"
#define ROUTE_INTERFACE_PERSONAL_ALLAIND 	"personalallaind"
#define ROUTE_INTERFACE_PERSONAL_SET_MOTION "personalsetmotion"
#define ROUTE_INTERFACE_PERSONAL_SET_CALL  "personalsetcall"

#define MAX_RECEIVE_BUF_SIZE 1024
 
#if RL_SUPPORT_CLOUD_DEV_INFO
#define DCLIENT_DEVICE_INFO_PATH		"/tmp/DeviceInfo.xml"


void *ProcessBackUpThread(void *pData)
{
	pthread_detach(pthread_self());
	return NULL;
}

void *ProcessBackUpRecoveryThread(void *pData)
{
	pthread_detach(pthread_self());
	return NULL;
}

CDeviceControl *GetDeviceControlInstance()
{
	return CDeviceControl::GetInstance();
}

CDeviceControl::CDeviceControl()
{
	memset(m_szDeviceToken, 0 ,sizeof(m_szDeviceToken));
	memset(m_szAuthCode, 0 ,sizeof(m_szAuthCode));
}

CDeviceControl::~CDeviceControl()
{
	
}

int CDeviceControl::Init()
{
#if RL_SUPPORT_AUTH_CODE
	for(int i=0; i<3; i++)
	{
		if(rl_str_isempty(m_szAuthCode))
		{
			GetSettingHandleInstance()->GetAuthKey(m_szAuthCode, sizeof(m_szAuthCode));
			if(rl_str_isempty(m_szAuthCode) || (rl_strcasecmp(m_szAuthCode, "null") == 0))
			{
				CHAR szPasswd[AUTH_CODE_SIZE] = {0};
				CreateRandomPassWord(szPasswd, sizeof(szPasswd), AUTH_KEY_DIGITS);
				GetSettingHandleInstance()->SetAuthKey(szPasswd);
			}
		}
		else
		{
			break;
		}
	}	
#endif
	return 0;
}

#if RL_SUPPORT_AUTH_CODE
int CDeviceControl::GetAuthKey(CHAR *pszAuthKey, int nSize)
{
	if(pszAuthKey != NULL && !rl_str_isempty(m_szAuthCode))
	{
		rl_strcpy_s(pszAuthKey, nSize, m_szAuthCode);
		return 0;
	}
	return -1;
}

int CDeviceControl::SetAuthKey(CHAR *pszAuthKey)
{
	if(pszAuthKey != NULL)
	{
		rl_strcpy_s(m_szAuthCode, sizeof(m_szAuthCode), pszAuthKey);
	}
	return 0;
}
#endif

int CDeviceControl::OnDeviceListChangeNotify()
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if(GetMsgControlInstance()->BuildRequestDeviceListMsg(&socketMsg) < 0)
	{
		rl_log_err("%s: BuildRequestDeviceListMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendRequestDeviceListMsg success.");
	return 0;
}

int CDeviceControl::OnDeviceListInfo(char *pszBuf, int nSize)
{
	if(pszBuf == NULL || nSize <= 0)
	{
		rl_log_err("OnDeviceListInfo fail pszBuf is NULL");
		return -1;
	}
	FILE *fp = fopen(DCLIENT_DEVICE_INFO_PATH,"wb+");
	if(fp != NULL)
	{
		if(nSize != (int)fwrite(pszBuf, 1, nSize, fp))
		{
			rl_log_err("write file error");	
			fclose(fp);
			return -1;
		}
		fclose(fp);
		fp = NULL;
	}
	else
	{
		rl_log_err("open file error\n");		
		return -1;
	}
	char cmd[512] = {0};
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	rl_sprintf_s(cmd, sizeof(cmd), "busybox chmod 777 %s", DCLIENT_DEVICE_INFO_PATH);
#else
	rl_sprintf_s(cmd, sizeof(cmd), "chmod 777 %s", DCLIENT_DEVICE_INFO_PATH);
#endif
	rl_system_100ms_ex(cmd, 2, 1);

	char szContactMD5[MD5_SIZE] = {0};
	GetSettingHandleInstance()->GetContactMD5(szContactMD5, sizeof(szContactMD5));
	DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
	memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
	rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), (char*)DCLIENT_DEVICE_INFO_PATH);
	rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), szContactMD5);
	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DEVICE_LIST_INFO, 0, 0, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
	
}

int CDeviceControl::OnLogoutSip()
{
	return 0;
}

int CDeviceControl::CheckDtmf(DCLIENT_CHECK_DTMF * pCheckDtmfMsg)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if(GetMsgControlInstance()->BuildCheckDtmfMsg(&socketMsg, pCheckDtmfMsg) < 0)
	{
		rl_log_err("%s: BuildCheckDtmfMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendCheckDtmfMsg success.");
	return 0;
}

int CDeviceControl::OnCheckDtmfAck(SOCKET_MSG_CHECK_DTMF_ACK *pSocketCheckDtmfAck)
{
	if(pSocketCheckDtmfAck == NULL)
	{
		return -1;
	}
	DCLIENT_CHECK_DTMF_ACK checkDtmfAckMsg;
	memset(&checkDtmfAckMsg, 0 ,sizeof(DCLIENT_CHECK_DTMF_ACK));
	checkDtmfAckMsg.nResult = pSocketCheckDtmfAck->nResult;
	checkDtmfAckMsg.nSequenceNum = pSocketCheckDtmfAck->nSequenceNum;
	rl_strcpy_s(checkDtmfAckMsg.szRemoteSip, sizeof(checkDtmfAckMsg.szRemoteSip), pSocketCheckDtmfAck->szRemoteSip);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_CHECK_DTMF_ACK, 0, 0, &checkDtmfAckMsg, sizeof(DCLIENT_CHECK_DTMF_ACK));
}

int CDeviceControl::OnDeviceCode(SOCKET_MSG_DEVICE_CODE *pSocketDeviceCode)
{
	if(pSocketDeviceCode == NULL)
	{
		return -1;
	}
	DCLIENT_DEVICE_CODE deviceCode;
	memset(&deviceCode, 0 ,sizeof(DCLIENT_DEVICE_CODE));
	rl_strcpy_s(deviceCode.szDeviceCode, sizeof(deviceCode.szDeviceCode), pSocketDeviceCode->szDeviceCode);
	GetSettingHandleInstance()->SetDeviceCode(deviceCode.szDeviceCode);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DEVICE_CODE, 0, 0, &deviceCode, sizeof(DCLIENT_DEVICE_CODE));
}

int CDeviceControl::ReportDeviceCode(DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCode)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildReportDeviceCodeMsg(&socketMsg, pReportDeviceCode) < 0)
	{
		rl_log_err("%s: BuildReportDeviceCodeMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg szToIP(%s).", __FUNCTION__, pReportDeviceCode->szToIP);
	if(GetConnectControlInstance()->SendUdpMsg(pReportDeviceCode->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;

}

int CDeviceControl::OnReportDeviceCode(DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCode)
{
	if(pReportDeviceCode == NULL)
	{
		return -1;
	}	
	DCLIENT_REPORT_DEVICE_CODE reportDeviceCode;
	rl_memset(&reportDeviceCode, 0 ,sizeof(DCLIENT_REPORT_DEVICE_CODE));
	rl_memcpy(&reportDeviceCode, pReportDeviceCode, sizeof(DCLIENT_REPORT_DEVICE_CODE));
	return ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_DEVICE_CODE, 0, 0, &reportDeviceCode, sizeof(DCLIENT_REPORT_DEVICE_CODE));
}

int CDeviceControl::OnClearDeviceCode()
{
	return ipc_send(IPC_ID_PHONE, MSG_D2P_CLEAR_DEVICE_CODE, 0, 0, NULL, 0);
}

int CDeviceControl::ReportNetworkInfo(DCLIENT_REPORT_NETWORK_INFO* pNetWorkInfo)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildReportNetworkInfoMsg(&socketMsg, pNetWorkInfo) < 0)
	{
		rl_log_err("%s: BuildReportNetworkInfoMsg failed.", __FUNCTION__);
		return -1;
	}

	if(GetConnectControlInstance()->SendUdpMsg(pNetWorkInfo->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnReportNetworkInfo(DCLIENT_REPORT_NETWORK_INFO *pReportNetworkInfo)
{
	if(pReportNetworkInfo == NULL)
	{
		return -1;
	}	
	DCLIENT_REPORT_NETWORK_INFO reportNetworkInfo;
	rl_memset(&reportNetworkInfo, 0 ,sizeof(DCLIENT_REPORT_NETWORK_INFO));
	rl_memcpy(&reportNetworkInfo, pReportNetworkInfo, sizeof(DCLIENT_REPORT_NETWORK_INFO));
	return ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_NETWORK_INFO, 0, 0, &reportNetworkInfo, sizeof(DCLIENT_REPORT_NETWORK_INFO));
}
#if RL_SUPPORT_RECV_MOTION_ALERT
int CDeviceControl::OnDoorMotionAlert(SOCKET_MSG_DOOR_MOTION_ALERT *pDoorMotionAlert)
{
	if(pDoorMotionAlert == NULL)
	{
		return -1;
	}	
	DCLIENT_DOOR_MOTION_ALERT doorMotionAlert;
	rl_memset(&doorMotionAlert, 0 ,sizeof(DCLIENT_DOOR_MOTION_ALERT));
	rl_strcpy_s(doorMotionAlert.szMAC, sizeof(doorMotionAlert.szMAC), pDoorMotionAlert->szMAC);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DOOR_MOTION_ALERT, 0, 0, &doorMotionAlert, sizeof(DCLIENT_REPORT_DEVICE_CODE));
}
#endif

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CDeviceControl::RequestConfigFromDevice(DCLIENT_REQUEST_CONFIG *requestCfgMsg)
{
	if(requestCfgMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildRequestCfgFromDeviceMsg(&socketMsg, requestCfgMsg) < 0)
	{
		rl_log_err("%s: BuildRequestCfgFromDeviceMsg failed.", __FUNCTION__);
		return -1;
	}

	if(GetConnectControlInstance()->SendUdpMsg(requestCfgMsg->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnReqCfgFromDevice(SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg)
{
	if(pRequestConfigMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildReportConfigMsg(&socketMsg, &(pRequestConfigMsg->module), TRUE);
	if(GetConnectControlInstance()->SendUdpMsg(pRequestConfigMsg->szFromIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;
}
#endif

int CDeviceControl::OnReportCallCapture(DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture)
{
	if(pReportCallCapture == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildReportCallCaptureMsg(&socketMsg, pReportCallCapture);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendCallCaptureMsg success.");
	return 0;
}

int CDeviceControl::OnReportTrigger(DCLIENT_REPORT_TRIGGER *pReportTrigger)
{
	if(pReportTrigger == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildReportTriggerMsg(&socketMsg, pReportTrigger);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendReportTriggerMsg success.");
	return 0;
}

int CDeviceControl::OnRequestAllTrigger(DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger)
{
	if(pRequestAllTrigger == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildRequestAllTriggerMsg(&socketMsg, pRequestAllTrigger);
	if(GetConnectControlInstance()->SendUdpMsg(pRequestAllTrigger->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRequestAllTrigger(SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger)
{
	if(pRequestAllTrigger == NULL)
	{
		return -1;
	}
	DCLIENT_ALL_TRIGGER_STATUS allTriggerStatus;
	memset(&allTriggerStatus, 0, sizeof(DCLIENT_ALL_TRIGGER_STATUS));
	rl_strcpy_s(allTriggerStatus.szFromIP, sizeof(allTriggerStatus.szFromIP), pRequestAllTrigger->szFromIP);	
	rl_strcpy_s(allTriggerStatus.szToIP, sizeof(allTriggerStatus.szToIP), pRequestAllTrigger->szToIP);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_ALL_TRIGGER_STATUS, 0, 0, &allTriggerStatus, sizeof(DCLIENT_ALL_TRIGGER_STATUS));
}

int CDeviceControl::OnReportAllTrigger(DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger)
{
	if(pRequestAllTrigger == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildReportAllTriggerMsg(&socketMsg, pRequestAllTrigger);
	if(GetConnectControlInstance()->SendUdpMsg(pRequestAllTrigger->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_err("%s: SendUDPMsg Success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnReportAllTrigger(SOCKET_MSG_REPORT_ALL_TRIGGER *pReporttAllTrigger)
{
	if(pReporttAllTrigger == NULL)
	{
		return -1;
	}
	DCLIENT_ALL_TRIGGER_STATUS allTriggerStatus;
	memset(&allTriggerStatus, 0, sizeof(DCLIENT_ALL_TRIGGER_STATUS));
	rl_strcpy_s(allTriggerStatus.szFromIP, sizeof(allTriggerStatus.szFromIP), pReporttAllTrigger->szFromIP);	
	rl_strcpy_s(allTriggerStatus.szToIP, sizeof(allTriggerStatus.szToIP), pReporttAllTrigger->szToIP);
	for(int i=0; i<ARMING_ZOOM_NUM_MAX*3; i++)
	{
		allTriggerStatus.bAlarmChException[i] = pReporttAllTrigger->szAlarmChException[i] == '1' ? 1 : 0;
	}
	return ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_ALL_TRIGGER_STATUS, 0, 0, &allTriggerStatus, sizeof(DCLIENT_ALL_TRIGGER_STATUS));
}

int CDeviceControl::OnManageAlarmMsg(SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg)
{
	if(pManageAlarmMsg == NULL)
	{
		return -1;
	}	
	DCLIENT_MANAGE_ALARM_MSG manageAlarmMsg;
	rl_memset(&manageAlarmMsg, 0 ,sizeof(DCLIENT_MANAGE_ALARM_MSG));
	
	manageAlarmMsg.nID = pManageAlarmMsg->nID;	
	manageAlarmMsg.nAlarmCode = pManageAlarmMsg->unAlarmCode;
	manageAlarmMsg.unAlarmCustomize= pManageAlarmMsg->unAlarmCustomize;
	manageAlarmMsg.unAlarmLocation= pManageAlarmMsg->unAlarmLocation;
	manageAlarmMsg.unAlarmZone= pManageAlarmMsg->unAlarmZone;
	rl_strcpy_s(manageAlarmMsg.szType, sizeof(manageAlarmMsg.szType), pManageAlarmMsg->szType);
	rl_strcpy_s(manageAlarmMsg.szAlarmMsg, sizeof(manageAlarmMsg.szAlarmMsg), pManageAlarmMsg->szAlarmMsg);
	rl_strcpy_s(manageAlarmMsg.szFromName, sizeof(manageAlarmMsg.szFromName), pManageAlarmMsg->szFromName);
	rl_strcpy_s(manageAlarmMsg.szApt, sizeof(manageAlarmMsg.szApt), pManageAlarmMsg->szApt);
	rl_strcpy_s(manageAlarmMsg.szTime, sizeof(manageAlarmMsg.szTime), pManageAlarmMsg->szTime);
	rl_strcpy_s(manageAlarmMsg.szMAC, sizeof(manageAlarmMsg.szMAC), pManageAlarmMsg->szMAC);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_MANAGE_ALARM, 0, 0, &manageAlarmMsg, sizeof(DCLIENT_MANAGE_ALARM_MSG));
}

int CDeviceControl::OnManageBroadcastMsg(DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg)
{
	if(pManageBroadcastMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	
	GetMsgControlInstance()->BuildManageBroadcastMsg(&socketMsg, pManageBroadcastMsg);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendManageBroadcastMsg success.");
	return 0;
}

int CDeviceControl::OnMaintenaceServerChange(SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange)
{
	if(pMaintenanceSrvChange == NULL)
	{
		return -1;
	}
	CHAR szToken[CLOUDSERVER_TOKEN_SIZE] = {0};
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	CHAR szGateServerIp[IP_SIZE] = {0};
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nGateServerPort = 0;
	INT nSize = 0;
	GetSettingHandleInstance()->GetCloudServerToken(szToken, sizeof(szToken));
	GetSettingHandleInstance()->GetCloudServerGateServer(szGateServerIp, sizeof(szGateServerIp));
	nGateServerPort = GetSettingHandleInstance()->GetCloudServerGatePort();
	BOOL bAccessFlag = FALSE;
	if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "access") == 0)
	{
		bAccessFlag = TRUE;
	}
	if(rl_str_isempty(szToken))//如果token为空，则重新登录
	{
		CHAR szTmpMac[MAC_SIZE] = {0};
		CHAR szMAC[MAC_SIZE] = {0};
		GetSettingHandleInstance()->GetMAC(szTmpMac, sizeof(szTmpMac));
		StrtokString(szTmpMac, szMAC, sizeof(szTmpMac), ":");	
		char szPasswd[VALUE_SIZE] = {0};	
		rl_sprintf_s(szPasswd, sizeof(szPasswd), "%s%s", szMAC, AES_KEY_V1_MASK);
		//对密码进行MD5计算
		CHAR szPasswdMD5[MD5_SIZE] = {0};
		MD5Encrypt(szPasswd, szPasswdMD5, sizeof(szPasswdMD5));	
		std::string strKSEncryptMac = AKKSEncryptMAC(szMAC, rl_strlen(szMAC));
		rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/login?user=%s&passwd=%s", szGateServerIp, nGateServerPort, strKSEncryptMac.c_str(), szPasswdMD5);
	}
	else
	{
		if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "ftp") == 0)
		{		
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/ftpserver?token=%s", szGateServerIp, nGateServerPort, szToken);
		}
		else if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "pbx") == 0)
		{
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/pbxserver?token=%s", szGateServerIp, nGateServerPort, szToken);		
		}
		else if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "rtsp") == 0)
		{
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/rtspserver?token=%s", szGateServerIp, nGateServerPort, szToken);
		}
		else if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "access") == 0)
		{
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/accessserver?token=%s", szGateServerIp, nGateServerPort, szToken);			
		}
		else if(rl_strcmp(pMaintenanceSrvChange->szSrvType, "web") == 0)
		{
			rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s:%d/web_server", szGateServerIp, nGateServerPort);
		}
	}
	char szHeadBuf[BUF_SIZE] = {0};
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"dev-version: %d", GATE_PLAT_FORM_VER_NEW);
	rl_log_info("%s : url = %s, %s", __FUNCTION__, szUrl, szHeadBuf);
	if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
	{
		rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
		//释放内存
		RL_FREE(pszRecvBuf);
		RL_FREE(pszHeadRecvBuf);
		return -1;
	}
	//解析出逻辑接入服务器的ip和port
	CLOUD_SERVER_INFO cloudServerInfo;
	rl_memset(&cloudServerInfo, 0, sizeof(cloudServerInfo));
	cloudServerInfo.nResult = -1;//默认当做解析失败，解析成功为0
	int nPlatformVer = GetConnectControlInstance()->OnAccessReqHttpRecv(cloudServerInfo, pszHeadRecvBuf, pszRecvBuf);
	if(cloudServerInfo.nResult == 0)//通知phone
	{
		if(bAccessFlag)//如果是access变化，则直接dclient处理
		{
			CHAR szAccessServerIp[VALUE_SIZE] = {0};	
			CHAR szAccessPort[INT_SIZE] = {0};
			INT nAccessServerPort = -1;
			if(sscanf(cloudServerInfo.szAccessSrv, "%[^:]:%[0-9]", szAccessServerIp, szAccessPort) == 2)
			{
				if((!rl_str_isempty(szAccessServerIp)) && (!rl_str_isempty(szAccessPort)))
				{
					nAccessServerPort = atoi(szAccessPort);

					//将access服务器的ip和端口写进配置文件里面去
					GetSettingHandleInstance()->SetCloudServerIp(szAccessServerIp);
					GetSettingHandleInstance()->SetCloudServerPort(nAccessServerPort);
					if(!rl_str_isempty(szAccessServerIp))
					{
						GetSettingHandleInstance()->SetCloudServerEnable(1);
					}
					if(!rl_str_isempty(cloudServerInfo.szToken))
					{
						GetSettingHandleInstance()->SetCloudServerToken(cloudServerInfo.szToken);
					}
					GetConnectControlInstance()->RestartConnection(szAccessServerIp, nAccessServerPort, FALSE);
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					
					rl_log_info("%s: AccessSrv:%s", __FUNCTION__, cloudServerInfo.szAccessSrv);
					return 0;
				}
			}
		}
		else
		{
			if(!rl_str_isempty(cloudServerInfo.szToken))
			{
				GetSettingHandleInstance()->SetCloudServerToken(cloudServerInfo.szToken);
			}
			if(!rl_str_isempty(cloudServerInfo.szFtpSrv))
			{
				GetSettingHandleInstance()->SetFtpSrv(cloudServerInfo.szFtpSrv);
			}
			if(!rl_str_isempty(cloudServerInfo.szVrtspSrv))
			{
				GetSettingHandleInstance()->SetVrtspSrv(cloudServerInfo.szVrtspSrv);
			}
			if(!rl_str_isempty(cloudServerInfo.szPBXSrv))
			{
				GetSettingHandleInstance()->SetPBXSrv(cloudServerInfo.szPBXSrv);
			}
			if(!rl_str_isempty(cloudServerInfo.szWebSrv))
			{
				GetSettingHandleInstance()->SetWebSrv(cloudServerInfo.szWebSrv);
			}
		}	
		rl_log_info("%s: websrv:%s, rtspsrv:%s, ftpsrv:%s, pbxsrv:%s", __FUNCTION__, cloudServerInfo.szWebSrv, cloudServerInfo.szVrtspSrv, cloudServerInfo.szFtpSrv, cloudServerInfo.szPBXSrv);
	}
	else if(cloudServerInfo.nResult == -1)//token 超时
	{	
		GetSettingHandleInstance()->SetCloudServerToken("");
		GetConnectControlInstance()->GetAccessServer();
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return 0;
}

int CDeviceControl::OnReportHealth(DCLIENT_REPORT_HEALTH *pReportHealth)
{
	if(pReportHealth == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	GetMsgControlInstance()->BuildReportHealthMsg(&socketMsg, pReportHealth);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("SendReportHealthMsg success.");
	return 0;
}

int CDeviceControl::OnResponseSensorTriggerMsg(DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg)
{
	if(pSensorTriggerMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	
	GetMsgControlInstance()->BuildResponseSensorTriggerMsg(&socketMsg, pSensorTriggerMsg);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("OnResponseSensorTriggerMsg success.");
	return 0;
}

int CDeviceControl::OnUploadVideoNotifyMsg(DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg)
{
	if(pUploadVideoNotifyMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	
	GetMsgControlInstance()->BuildUploadVideoNotifyMsg(&socketMsg, pUploadVideoNotifyMsg);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("%s success.",__FUNCTION__);
	return 0;
}

int CDeviceControl::OnUploadCaptureNotifyMsg(DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg)
{
	if(pUploadCaptureNotifyMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	
	GetMsgControlInstance()->BuildUploadCaptureNotifyMsg(&socketMsg, pUploadCaptureNotifyMsg);
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_debug("%s success.",__FUNCTION__);
	return 0;
}

int CDeviceControl::OnServerHeartBeatMsg()
{
	SOCKET_MSG socketMsg;
	if(GetMsgControlInstance()->BuildHeartBeatAckMsg(&socketMsg) < 0)
	{
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		return -1;
	}
	rl_log_info("send HeartBeatAckMsg success.");
	return 0;
}


#if RL_SUPPORT_DEVICE_HTTP_REGISTER
int CDeviceControl::PersonalRegister(DCLIENT_PERSONAL_REGISTER* pPersonalRegister)
{
	if(pPersonalRegister == NULL)
	{
		return -1;
	}
	//CHAR szRecvBuf[MAX_RECEIVE_BUF_SIZE] = {0};
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	URLEncode(pPersonalRegister->szName, rl_strlen(pPersonalRegister->szName), pPersonalRegister->szName, sizeof(pPersonalRegister->szName));	
	URLEncode(pPersonalRegister->szEmail, rl_strlen(pPersonalRegister->szEmail), pPersonalRegister->szEmail, sizeof(pPersonalRegister->szEmail));
	URLEncode(pPersonalRegister->szPasswd, rl_strlen(pPersonalRegister->szPasswd), pPersonalRegister->szPasswd, sizeof(pPersonalRegister->szPasswd));

	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "Name=%s&Email=%s&DeviceCode=%s&Passwd=%s",
					pPersonalRegister->szName, pPersonalRegister->szEmail, pPersonalRegister->szDeviceCode, pPersonalRegister->szPasswd);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_REGISTER);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"%s: %s", CONTENT_TYPE, CONTENT_TYPE_FORM);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);	
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{	
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_PERSONAL_REGISTER_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG));
}

int CDeviceControl::PersonalLogin(DCLIENT_PERSONAL_LOGIN* pPersonalLogin)
{
	DCLIENT_PERSONAL_LOGIN personalLogin;
	memset(&personalLogin, 0 ,sizeof(DCLIENT_PERSONAL_LOGIN));
	if(pPersonalLogin == NULL)
	{
		//自己获取MAC和Passwd	
		CHAR szTmpMac[MAC_SIZE] = {0};
		GetSettingHandleInstance()->GetMAC(szTmpMac, sizeof(szTmpMac));
		StrtokString(szTmpMac, personalLogin.szAccount, sizeof(szTmpMac), ":");		
		//获取PASSWD
		CHAR szPassWdKey[DCLIENT_ENCRY_PASSWD_SIZE] = {0};
		rl_sprintf_s(szPassWdKey, sizeof(szPassWdKey), "%s%s", AES_KEY_DEFAULT_MASK, personalLogin.szAccount);
		AesBase64Encrypt(personalLogin.szAccount, personalLogin.szPasswd, szPassWdKey);
	}
	else
	{
		rl_memcpy(&personalLogin, pPersonalLogin, sizeof(DCLIENT_PERSONAL_LOGIN));
	}
	//CHAR szRecvBuf[MAX_RECEIVE_BUF_SIZE] = {0};
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	URLEncode(personalLogin.szPasswd, rl_strlen(personalLogin.szPasswd), personalLogin.szPasswd, sizeof(personalLogin.szPasswd));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "MAC=%s&Passwd=%s",personalLogin.szAccount, personalLogin.szPasswd);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_LOGIN);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s", CONTENT_TYPE, CONTENT_TYPE_FORM);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			//释放内存
			RL_FREE(pszRecvBuf);
			RL_FREE(pszHeadRecvBuf);
			return -1;
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		DCLIENT_WEB_LOGIN_ACK_MSG webLoginAckMsg;
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		ParseHttpCommonResponseLoginAck(json, &webLoginAckMsg);
		FreeJson(json);	
		//将TOKEN 保存下来
		if(!rl_str_isempty(webLoginAckMsg.szToken))
		{
			rl_strcpy_s(m_szDeviceToken, sizeof(m_szDeviceToken), webLoginAckMsg.szToken);
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return 0;
}

int CDeviceControl::AddSlaveAccount(DCLIENT_ADD_SLAVE_ACCOUNT* pAddSlaveAccount)
{
	if(pAddSlaveAccount == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	URLEncode(pAddSlaveAccount->szName, rl_strlen(pAddSlaveAccount->szName), pAddSlaveAccount->szName, sizeof(pAddSlaveAccount->szName));	
	URLEncode(pAddSlaveAccount->szEmail, rl_strlen(pAddSlaveAccount->szEmail), pAddSlaveAccount->szEmail, sizeof(pAddSlaveAccount->szEmail));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "Name=%s&Email=%s", pAddSlaveAccount->szName, pAddSlaveAccount->szEmail);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_ADD_PERSONAL_USER);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{		
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	
		
		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);
			
			//再次发起请求
			memset(pszRecvBuf, 0 ,sizeof(pszRecvBuf));
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}	
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ADD_SLAVE_ACCOUNT_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG));	
}

int CDeviceControl::GetSlaveAccountList(DCLIENT_GET_SLAVE_ACCOUNT_LIST* pGetSlaveAccountList)
{
	if(pGetSlaveAccountList == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK accountListAckMsg;
	memset(&accountListAckMsg, 0 ,sizeof(DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK));	
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s?page=%d&row=%d", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_GET_PERSONAL_USER,
				  pGetSlaveAccountList->nPage, pGetSlaveAccountList->nRow);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2) {	
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			accountListAckMsg.nResult = -2;
			rl_strcpy_s(accountListAckMsg.szMsg, sizeof(accountListAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpPersonalUserListAck(json, &accountListAckMsg);
		FreeJson(json);	

		//如果Token超时，则重新登录获取Token
		if(accountListAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);		
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpPersonalUserListAck(json, &accountListAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&accountListAckMsg, 0 ,sizeof(DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK));
			accountListAckMsg.nResult = -1;
			rl_strcpy_s(accountListAckMsg.szMsg, sizeof(accountListAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_GET_SLAVE_ACCOUNT_LIST_ACK, 0, 0, &accountListAckMsg, sizeof(DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK));	
}

int CDeviceControl::DeleteSlaveAccount(DCLIENT_DEL_SLAVE_ACCOUNT* pDelSlaveAccount)
{
	if(pDelSlaveAccount == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	
	DCLIENT_WEB_ACK_MSG webAckMsg;	
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "ID=%d", pDelSlaveAccount->nID);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_DEL_PERSONAL_USER);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	

		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DELETE_SLAVE_ACCOUNT_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG)); 
}

int CDeviceControl::BindDeviceByDeviceCode(DCLIENT_BIND_DEVICE* pBindDevice)
{
	if(pBindDevice == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "DeviceCode=%s&Location=%s", pBindDevice->szDeviceCode, pBindDevice->szLocation);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_ADD_PERSONAL_DEVICE);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);	
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	
		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);		
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE, X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ADD_DEVICE_BY_DEVICECODE_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG)); 
}

int CDeviceControl::EmailExit(DCLIENT_EMAIL_EXIST* pEmailExit)
{
	if(pEmailExit == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "Email=%s", pEmailExit->szEmail);
	rl_sprintf_s(szUrl, sizeof(szUrl), "http://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_CHECK_PERSONAL_EMAIL);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),"%s: %s\r\n%s: %s", CONTENT_TYPE,X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}	
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	

		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);		
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf), "%s: %s\r\n%s: %s", CONTENT_TYPE,X_AUTH_TOKEN, CONTENT_TYPE_FORM, m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_EMAIL_EXIST_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG));	
}

int CDeviceControl::ParseHttpCommonResponseAck(cJSON *json, DCLIENT_WEB_ACK_MSG *pWebAckMsg)
{
	if(pWebAckMsg == NULL || json == NULL)
	{
		return -1;
	}
	memset(pWebAckMsg, 0, sizeof(DCLIENT_WEB_ACK_MSG));
	cJSON *pTmpJson = json;
	while(pTmpJson != NULL)
	{
		json = pTmpJson;	
		
		cJSON *pTmpJson1 = NULL;
		while(json != NULL)
		{
			switch(json->type & 255)
			{
				case cJSON_Number:
					if(json->string != NULL && rl_strcmp(json->string, "code") == 0)
					{
						pWebAckMsg->nResult = json->valueint;
					}	
					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}	
					break;
				case cJSON_String:		
					if(json->string != NULL && rl_strcmp(json->string, "msg") == 0)
					{	
						rl_strcpy(pWebAckMsg->szMsg, json->valuestring);
					}
					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}
					break;
				case cJSON_Object:
					{
						pTmpJson1 = json;
						json = json->child;
					}
					break;
				case cJSON_Array:
					{
						pTmpJson = json;
						json= json->child;
						pTmpJson1 = json;
					}
					break;
				default:
					json = json->next;
					break;
			}		
		}
		pTmpJson = pTmpJson->next;
	}
	return 0;
}

int CDeviceControl::ParseHttpCommonResponseLoginAck(cJSON *json, DCLIENT_WEB_LOGIN_ACK_MSG *pWebLoginAckMsg)
{
	if(pWebLoginAckMsg == NULL || json == NULL)
	{
		return -1;
	}
	
	memset(pWebLoginAckMsg, 0, sizeof(DCLIENT_WEB_LOGIN_ACK_MSG));
	cJSON *pTmpJson = json;
	while(pTmpJson != NULL)
	{
		json = pTmpJson;	
		
		cJSON *pTmpJson1 = NULL;
		while(json != NULL)
		{
			switch(json->type & 255)
			{
				case cJSON_Number:
					if(json->string != NULL && rl_strcmp(json->string, "code") == 0)
					{
						pWebLoginAckMsg->nResult = json->valueint;
					}
					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}
					break;
				case cJSON_String:	
					if(json->string != NULL && rl_strcmp(json->string, "msg") == 0)
					{	
						rl_strcpy(pWebLoginAckMsg->szMsg, json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "token") == 0)
					{	
						rl_strcpy(pWebLoginAckMsg->szToken, json->valuestring);
					}
					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}
					break;
				case cJSON_Object:
					{
						pTmpJson1 = json;
						json = json->child;
					}
					break;
				case cJSON_Array:
					{
						pTmpJson = json;
						json= json->child;
						pTmpJson1 = json;
					}
					break;
				default:
					json = json->next;
					break;
			}		
		}
		pTmpJson = pTmpJson->next;
	}
	return 0;
}

int CDeviceControl::ParseHttpPersonalUserListAck(cJSON *json, DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK *pPersonalUserListMsg)
{
	if(pPersonalUserListMsg == NULL || json == NULL)
	{
		return -1;
	}
	memset(pPersonalUserListMsg, 0, sizeof(DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK));
	int nCount = 0;
	cJSON *pTmpJson = json;

	while(pTmpJson != NULL)
	{
		json = pTmpJson;	
		
		cJSON *pTmpJson1 = NULL;
		while(json != NULL)
		{		
			switch(json->type & 255)
			{
				case cJSON_Number:
					if(json->string != NULL && rl_strcmp(json->string, "total") == 0)
					{
						pPersonalUserListMsg->nCount = json->valueint;
					}
					else if(json->string != NULL && rl_strcmp(json->string, "code") == 0)
					{
						pPersonalUserListMsg->nResult = json->valueint;
					}
					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}
					break;
				case cJSON_String:	
					if(json->string != NULL && rl_strcmp(json->string, "msg") == 0)
					{	
						rl_strcpy(pPersonalUserListMsg->szMsg, json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "total") == 0)
					{	
						pPersonalUserListMsg->nCount = atoi(json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "ID") == 0)
					{											
						pPersonalUserListMsg->stSlaveAccountList[nCount].nID = atoi(json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "Name") == 0)
					{						
						rl_strcpy(pPersonalUserListMsg->stSlaveAccountList[nCount].szName, json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "Email") == 0)
					{	
						rl_strcpy(pPersonalUserListMsg->stSlaveAccountList[nCount].szEmail, json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "SipAccount") == 0)
					{	
						rl_strcpy(pPersonalUserListMsg->stSlaveAccountList[nCount].szSip, json->valuestring);
					}
					else if(json->string != NULL && rl_strcmp(json->string, "Role") == 0)
					{	
						pPersonalUserListMsg->stSlaveAccountList[nCount++].nRole= atoi(json->valuestring);
					}

					if(json->next == NULL && pTmpJson1 != NULL)
					{
						json = pTmpJson1->next;
						pTmpJson1 = json;
					}
					else
					{
						json = json->next;
					}
					break;
				case cJSON_Object:
					{
						pTmpJson1 = json;
						json = json->child;
					}
					break;

				case cJSON_Array:
					{
						pTmpJson = json;
						json= json->child;
						pTmpJson1 = json;
					}
					break;
				default:
					json = json->next;
					break;
			}
		}
		pTmpJson = pTmpJson->next;
	}
	return 0;
}
#endif

#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR
int CDeviceControl::GetMotionAndRobinCallConfig()
{
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};	
	DCLIENT_MOTION_AND_ROBINCALL motionAndRobinCallMsg;
	memset(&motionAndRobinCallMsg, 0 ,sizeof(DCLIENT_MOTION_AND_ROBINCALL));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_USER_CONF);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
				CONTENT_TYPE_FORM,m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			motionAndRobinCallMsg.nResult = -2;
			rl_strcpy_s(motionAndRobinCallMsg.szMsg, sizeof(motionAndRobinCallMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{
		nRet = ParseHttpMotionAndRobinCallConf(pszRecvBuf, &motionAndRobinCallMsg);

		//如果Token超时，则重新登录获取Token
		if(motionAndRobinCallMsg.nResult == 2)
		{
			PersonalLogin(NULL);		
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
					CONTENT_TYPE_FORM,m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				nRet = ParseHttpMotionAndRobinCallConf(pszRecvBuf, &motionAndRobinCallMsg);
			}
		}
		if(nRet == 0)
		{
			//获取label和key的映射类表
			DCLIENT_ACK_APP_AND_INDOOR_LABEL labelAndKeyMapList;
			memset(&labelAndKeyMapList, 0 ,sizeof(DCLIENT_ACK_APP_AND_INDOOR_LABEL));
			GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
			rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_ALLAIND);
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
						CONTENT_TYPE_FORM,m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_GET, "", szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);
				}
				break;
			}
			
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				nRet = ParseHttpLabelAndKeyMapList(pszRecvBuf, &labelAndKeyMapList);
			}
			if(nRet == 0)//整合数据上报给phone
			{
				rl_memcpy(motionAndRobinCallMsg.stLabelDetail, labelAndKeyMapList.stLabelDetail, sizeof(motionAndRobinCallMsg.stLabelDetail));				
				return ipc_send(IPC_ID_PHONE, MSG_D2P_MOTION_AND_ROBINCALL_INFO, 0, 0, &motionAndRobinCallMsg, sizeof(DCLIENT_MOTION_AND_ROBINCALL));	
			}

		}

	}
	memset(&motionAndRobinCallMsg, 0, sizeof(motionAndRobinCallMsg));
	motionAndRobinCallMsg.nResult = -2;
	rl_strcpy_s(motionAndRobinCallMsg.szMsg, sizeof(motionAndRobinCallMsg.szMsg), (char*)"error");
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_MOTION_AND_ROBINCALL_INFO, 0, 0, &motionAndRobinCallMsg, sizeof(DCLIENT_MOTION_AND_ROBINCALL));		
}

int CDeviceControl::SetPersonalAccountMotion(DCLIENT_SET_PERSONAL_ACCOUNT_MOTION *pPersonalAccountMotion)
{
	if(pPersonalAccountMotion == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "EnableMotion=%d&MotionTime=%d", pPersonalAccountMotion->nEnableMotion, pPersonalAccountMotion->nMotionTime);
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_SET_MOTION);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
				CONTENT_TYPE_FORM,m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{		
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	
		
		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);
			
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
					CONTENT_TYPE_FORM,m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);	
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_SET_PERSONAL_ACCOUNT_MOTION_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG));	
}

int CDeviceControl::SetPersonalAccountRobinCall(DCLIENT_MOTION_AND_ROBINCALL *pPersonalAccountRobinCall)
{
	if(pPersonalAccountRobinCall == NULL)
	{
		return -1;
	}
	CHAR *pszRecvBuf = NULL;
	CHAR *pszHeadRecvBuf = NULL;
	INT nSize = 0;
	INT nRet = 0;
	CHAR szUrl[CFG_URL_MAX_SIZE] = {0};
	char szDataInfo[BUF_SIZE] = {0};
	char szHeadBuf[BUF_SIZE] = {0};
	CHAR szWebServer[VALUE_SIZE] = {0};
	DCLIENT_WEB_ACK_MSG webAckMsg;
	memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
	GetSettingHandleInstance()->GetWebSrv(szWebServer, sizeof(szWebServer));	

	//组合robincall的数据
	
	rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "EnableRobinCall=%d&RobinCallTime=%d", pPersonalAccountRobinCall->nEnableRobinCall, pPersonalAccountRobinCall->nRobinCallTime);
	char szTmpDataInfo[BUF_SIZE] = {0};
	rl_strcpy_s(szTmpDataInfo, sizeof(szTmpDataInfo), szDataInfo);
	for(int i=0; i<10; i++)
	{
		if(!rl_str_isempty(pPersonalAccountRobinCall->szRobinCall[i].szRobinCallKey))
		{				
			URLEncode(pPersonalAccountRobinCall->szRobinCall[i].szRobinCallKey, DCLIENT_ROBINCALL_KEY_SIZE, pPersonalAccountRobinCall->szRobinCall[i].szRobinCallKey, DCLIENT_ROBINCALL_KEY_SIZE);	
			rl_sprintf_s(szDataInfo, sizeof(szDataInfo), "%s&RobinCallVal[%d]=%s", szTmpDataInfo, i, pPersonalAccountRobinCall->szRobinCall[i].szRobinCallKey);
			rl_strcpy_s(szTmpDataInfo, sizeof(szTmpDataInfo), szDataInfo);
		}
	}
	rl_sprintf_s(szUrl, sizeof(szUrl), "https://%s/%s/%s", szWebServer, APACHE_VERSION, ROUTE_INTERFACE_PERSONAL_SET_CALL);
	rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
				CONTENT_TYPE_FORM,m_szDeviceToken);
	for(int i=0; i<3; i++)
	{
		if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
		{
			if(i < 2){
				usleep(200*1000);
				continue;
			}
			rl_log_err("%s failed: send url failed.",__FUNCTION__);
			webAckMsg.nResult = -2;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"error");
		}
		break;
	}
	//处理返回的数据
	if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
	{		
		cJSON * json = TransCharBufToJson(pszRecvBuf);
		nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
		FreeJson(json);	
		
		//如果Token超时，则重新登录获取Token
		if(webAckMsg.nResult == 2)
		{
			PersonalLogin(NULL);
			
			//再次发起请求
			rl_sprintf_s(szHeadBuf, sizeof(szHeadBuf),CONTENT_TYPE": %s\r\n"X_AUTH_TOKEN": %s",
					CONTENT_TYPE_FORM,m_szDeviceToken);
			for(int i=0; i<3; i++)
			{
				if((SendRequestUrlByCurl(szUrl, HTTP_REQUEST_METHOD_POST, szDataInfo, szHeadBuf, pszRecvBuf, nSize, pszHeadRecvBuf) < 0) || (nSize <= 0))
				{
					if(i < 2){
						usleep(200*1000);
						continue;
					}
					rl_log_err("%s failed: send url failed.",__FUNCTION__);
					//释放内存
					RL_FREE(pszRecvBuf);
					RL_FREE(pszHeadRecvBuf);
					return -1;
				}
				break;
			}
			if(!rl_str_isempty(pszRecvBuf) && nSize > 0)
			{
				json = TransCharBufToJson(pszRecvBuf);
				nRet = ParseHttpCommonResponseAck(json, &webAckMsg);
				FreeJson(json);	
			}
		}
		if(nRet == -1)//服务出错
		{
			memset(&webAckMsg, 0 ,sizeof(DCLIENT_WEB_ACK_MSG));
			webAckMsg.nResult = -1;
			rl_strcpy_s(webAckMsg.szMsg, sizeof(webAckMsg.szMsg), (char*)"server error");
		}
	}
	//释放内存
	RL_FREE(pszRecvBuf);
	RL_FREE(pszHeadRecvBuf);
	return ipc_send(IPC_ID_PHONE, MSG_D2P_SET_PERSONAL_ACCOUNT_CALLBIN_ACK, 0, 0, &webAckMsg, sizeof(DCLIENT_WEB_ACK_MSG));		
}
#endif

#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR
int CDeviceControl::ParseHttpMotionAndRobinCallConf(CHAR *pszJsonBuf, DCLIENT_MOTION_AND_ROBINCALL *pMotionAndRobinCallConf)
{
	if(pMotionAndRobinCallConf == NULL || pszJsonBuf == NULL)
	{
		return -1;
	}
	memset(pMotionAndRobinCallConf, 0, sizeof(DCLIENT_MOTION_AND_ROBINCALL));
	cJSON * root = NULL;   
	cJSON *object = NULL;    
	cJSON* item = NULL;
	CHAR szRobinCallBuf[512]={0};
	root = cJSON_Parse(pszJsonBuf); 
	if (!root) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		return -1;
	}
	
	item = cJSON_GetObjectItem(root, "code");
	if(item)
	{	
		pMotionAndRobinCallConf->nResult = item->valueint;
	}
	item = cJSON_GetObjectItem(root, "msg");
	if(item)
	{	
		rl_strcpy(pMotionAndRobinCallConf->szMsg, item->valuestring);
	}
	object = cJSON_GetObjectItem(root, "data");
	if(object)
	{
		item=cJSON_GetObjectItem(object,"EnableMotion");
		if(item)
		{
			pMotionAndRobinCallConf->nEnableMotion = rl_atoi(item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"MotionTime");
		if(item)
		{
			pMotionAndRobinCallConf->nMotionTime= rl_atoi(item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"EnableRobinCall");
		if(item)
		{
			pMotionAndRobinCallConf->nEnableRobinCall= rl_atoi(item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"RobinCallTime");
		if(item)
		{
			pMotionAndRobinCallConf->nRobinCallTime= rl_atoi(item->valuestring);
		}
		item=cJSON_GetObjectItem(object,"RobinCallVal");
		if(item)
		{
			rl_strcpy(szRobinCallBuf, item->valuestring);
		}
	}
	cJSON_Delete(root);
	
	root = cJSON_Parse(szRobinCallBuf); 
	if (!root) 
	{
		//root would be null when robin call was not configured on cloud.
		return 0;
	}

	for(int i=0; i<10; i++)
	{
		CHAR szIndex[INT_SIZE] = {0};
		INT nID = -1;
		INT nType = -1;
		rl_sprintf_s(szIndex, sizeof(szIndex), "%d", i);
		object = cJSON_GetObjectItem(root, szIndex);
		if(object)
		{
			item = cJSON_GetObjectItem(object, "ID");
			if(item)
			{
				nID = item->valueint;
			}
			item = cJSON_GetObjectItem(object, "Type");
			if(item)
			{
				nType = item->valueint;
			}
			if(nID>0 && nType>0)
			{
				rl_sprintf_s(pMotionAndRobinCallConf->szRobinCall[i].szRobinCallKey, DCLIENT_ROBINCALL_KEY_SIZE, "%d-%d", nType, nID);
			}
		}
	}
	cJSON_Delete(root);
	return 0;
}

int CDeviceControl::ParseHttpLabelAndKeyMapList(CHAR *pszJsonBuf, DCLIENT_ACK_APP_AND_INDOOR_LABEL *pLabelAndKeyMapList)
{
	if(pLabelAndKeyMapList == NULL || pszJsonBuf == NULL)
	{
		return -1;
	}
	memset(pLabelAndKeyMapList, 0, sizeof(DCLIENT_ACK_APP_AND_INDOOR_LABEL));
	cJSON *root = NULL;
	cJSON *object = NULL;    
	cJSON *arrayItem = NULL;    
	cJSON *item = NULL;
	
	root = cJSON_Parse(pszJsonBuf); 
	if (!root) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		return -1;
	}
	
	item = cJSON_GetObjectItem(root, "code");
	if(item)
	{	
		pLabelAndKeyMapList->nResult = item->valueint;
	}
	item = cJSON_GetObjectItem(root, "msg");
	if(item)
	{	
		rl_strcpy(pLabelAndKeyMapList->szMsg, item->valuestring);
	}
	arrayItem = cJSON_GetObjectItem(root, "data");
	if(arrayItem)
	{
		int nArraySize = cJSON_GetArraySize(arrayItem);
		for(int i=0 ;i<nArraySize; i++)
		{
			object = cJSON_GetArrayItem(arrayItem, i);
			if(object)
			{
				item = cJSON_GetObjectItem(object, "Label");
				if(item)
				{
					rl_strcpy(pLabelAndKeyMapList->stLabelDetail[i].szLabel, item->valuestring);
				}
				item = cJSON_GetObjectItem(object, "Key");
				if(item)
				{
					rl_strcpy(pLabelAndKeyMapList->stLabelDetail[i].szKey, item->valuestring);
				}
				
			}
		}
	}
	cJSON_Delete(root);
	return 0;
}
#endif


#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
int CDeviceControl::SendReportDoorStatus(DCLIENT_REPORT_DOORSTATUS *pDoorStatus)
{
	if(pDoorStatus == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportDoorStatusMsg(&socketMsg, pDoorStatus) < 0)
	{
		rl_log_err("%s: BuildReportDoorStatusMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendMulticastMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}
#endif

#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
int CDeviceControl::OnRecvReportDoorStatus(DCLIENT_REPORT_DOORSTATUS *pDoorStatus)
{
	if(pDoorStatus == NULL)
	{
		return -1;
	}

	return ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_DOOR_STATUS, 0, 0, pDoorStatus, sizeof(DCLIENT_REPORT_DOORSTATUS));
}
#endif

#if RL_SUPPORT_SEND_REPORT_GAS
int CDeviceControl::SendReportGas(DCLIENT_REPORT_GAS *pReportGas)
{
	if(pReportGas == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportGasMsg(&socketMsg, pReportGas) < 0)
	{
		rl_log_err("%s: BuildReportGasMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}
#endif

int CDeviceControl::SendReportVisitorInfo(DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo)
{
	if(pReportVisitorInfo == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportVisitorInfoMsg(&socketMsg, pReportVisitorInfo) < 0)
	{
		rl_log_err("%s: BuildReportVisitorInfoMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::SendReportVisitorAuthInfo(DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo)
{
	if(pReportVisitorAuthInfo == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportVisitorAuthInfoMsg(&socketMsg, pReportVisitorAuthInfo) < 0)
	{
		rl_log_err("%s: BuildReportVisitorAuthInfoMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::SendRequestOssSts(DCLIENT_REQUEST_OSS_STS *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestOssStsMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildRequestOssStsMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::RemoteControlOpenDoorResponse(DCLIENT_REMOTE_CONTROL_OPENDOOR *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	SOCKET_MSG_AKCS_ACK akcsACKMsg;
	memset(&akcsACKMsg, 0, sizeof(SOCKET_MSG_AKCS_ACK));
	rl_strcpy_s(akcsACKMsg.szTraceID, sizeof(akcsACKMsg.szTraceID), pData->szTraceID);
	rl_strcpy_s(akcsACKMsg.szType, sizeof(akcsACKMsg.szType), "OpenDoor");	
	rl_sprintf_s(akcsACKMsg.szResult, sizeof(akcsACKMsg.szResult), "%d", pData->nStatus);	
	akcsACKMsg.nMsgID = 0x000A;
	if (GetMsgControlInstance()->BuildAKCSACKMsg(&socketMsg, &akcsACKMsg) < 0)
	{
		rl_log_err("%s: BuildRCOpenDoorResponseMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRemoteAccessWeb(DCLIENT_REMOTE_ACCESS_WEB *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	char szSSHPassSrv[VALUE_SIZE] = {0};
	GetSettingHandleInstance()->GetSSHPassSrv(szSSHPassSrv, sizeof(szSSHPassSrv));
	char szCmd[DCLIENT_BUF_MAX_SIZE] = {0};
	rl_sprintf_s(szCmd, sizeof(szCmd), 
		"export LD_LIBRARY_PATH=/app/lib.ssh:/system/lib/:/app/lib/;export HOME=/tmp/;"
		"ssh -v -z %s "
		"-o StrictHostKeyChecking=no "
		"-o ExitOnForwardFailure=yes "
		"-o TCPKeepAlive=yes "
		"-o ServerAliveInterval=10 "
		"-o ServerAliveCountMax=3 "
		"-p %d -fCNR %d:localhost:80 %s@%s &", 
		pData->szPassword, pData->nSSHPort, pData->nPort, pData->szUserName, 
		rl_strlen(szSSHPassSrv) > 0 ? szSSHPassSrv : "remoteconfig.akuvox.com");
	if(rl_system_ex(szCmd, 10, 3) < 0) {
		rl_log_err("%s: exec(%s) failed.", __FUNCTION__, szCmd);
		return -1;
	}
	return 0;
}

int CDeviceControl::RequestOpenDoor(DCLIENT_REQUEST_OPENDOOR *pData, int nType)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestOpenDoor(&socketMsg, pData, nType) < 0)
	{
		rl_log_err("%s: BuildRequestOpenDoor failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnOpenDoorACK(DCLIENT_OPENDOOR_ACK *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_OPENDOOR_ACK, 0, 0, pData, sizeof(DCLIENT_OPENDOOR_ACK));
	return 0;
}

int CDeviceControl::OnRegisterFace(DCLIENT_FACE_INFO *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_REGISTER_FACE, 0, 0, pData, sizeof(DCLIENT_FACE_INFO));
	return 0;
}

int CDeviceControl::OnModifyFace(DCLIENT_FACE_INFO *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_MODIFY_FACE, 0, 0, pData, sizeof(DCLIENT_FACE_INFO));
	return 0;
}

int CDeviceControl::OnDeleteFace(DCLIENT_FACE_INFO *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_DELETE_FACE, 0, 0, pData, sizeof(DCLIENT_FACE_INFO));
	return 0;
}

int CDeviceControl::OnGSFaceHttpApiLogin(SOCKET_MSG_GSFACE_HTTPAPI *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	CHAR szHttpUrl[URL_SIZE] = {0};
	if(!rl_str_isempty(pData->szURL))
	{
		CHAR szTmpMac[MAC_SIZE] = {0};
		CHAR szMAC[MAC_SIZE] = {0};
		GetSettingHandleInstance()->GetMAC(szTmpMac, sizeof(szTmpMac));
		StrtokString(szTmpMac, szMAC, sizeof(szTmpMac), ":");
		rl_sprintf_s(szHttpUrl, sizeof(szHttpUrl), "%s?mac=%s", pData->szURL, szMAC);
	}
	//CHAR szRecvBuf[1024] = {0};
	DCLIENT_CURL_HTTP_REQUEST curlHttpRequest;
	memset(&curlHttpRequest, 0, sizeof(DCLIENT_CURL_HTTP_REQUEST));
	curlHttpRequest.nAuthMethod = HTTP_AUTH_METHOD_NONE;
	curlHttpRequest.nRequestMethod = HTTP_REQUEST_METHOD_GET;
	//curlHttpRequest.pRecvBuf = szRecvBuf;
	curlHttpRequest.pUrl = szHttpUrl;
	if((SendRequestUrlByCurl(curlHttpRequest) < 0))
	{
		rl_log_err("%s:SendRequestUrlByCurl failed", __FUNCTION__);
		//释放内存
		RL_FREE(curlHttpRequest.pRecvBuf);
		RL_FREE(curlHttpRequest.pHeadRecvBuf);
		return -1;
	}

	//解析出地址
	DOWNLOAD_SERVER_INFO downloadInfo;
	memset(&downloadInfo, 0, sizeof(DOWNLOAD_SERVER_INFO));
	cJSON *root = NULL;    
	cJSON *item = NULL;
	root = cJSON_Parse(curlHttpRequest.pRecvBuf); 
	if (!root) 
	{
		rl_log_err("Error before: [%s]\n",cJSON_GetErrorPtr());
		//释放内存
		RL_FREE(curlHttpRequest.pRecvBuf);
		RL_FREE(curlHttpRequest.pHeadRecvBuf);
		return -1;
	}
	item = cJSON_GetObjectItem(root, "md5");
	if(item)
	{	
		rl_strcpy_s(downloadInfo.szMD5, sizeof(downloadInfo.szMD5), item->valuestring);
	}
	item = cJSON_GetObjectItem(root, "facelist_path");
	if(item)
	{	
		rl_strcpy_s(downloadInfo.szUrl, sizeof(downloadInfo.szUrl), item->valuestring);
	}
	GetDownloadControlInstance()->AddMsg(DOWNLOAD_MSG_SYNC_FACE_PIC, 0, 0, &downloadInfo, sizeof(DOWNLOAD_SERVER_INFO));
	cJSON_Delete(root);
	//释放内存
	RL_FREE(curlHttpRequest.pRecvBuf);
	RL_FREE(curlHttpRequest.pHeadRecvBuf);
	return 0;
}

int CDeviceControl::OnRequestACInfo(DCLIENT_REQUEST_ACINFO *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestACInfoMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildRequestACInfoMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnSendDeliveryMsg(DCLIENT_SEND_DELIVERY *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildSendDeliveryMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildSendDeliveryMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRequestPersonelData(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_PERSONEL_DATA, 0, 0, pData, sizeof(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA));
	return 0;
}

int CDeviceControl::OnSyncPersonelData(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_SYNC_PERSONEL_DATA, 0, 0, pData, sizeof(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA));
	return 0;
}

int CDeviceControl::OnRequestFingerPrint(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_REQUEST_FINGER_PRINT, 0, 0, pData, sizeof(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT));
	return 0;
}

int CDeviceControl::OnSyncFingerPrint(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	ipc_send(IPC_ID_PHONE, MSG_D2P_SYNC_FINGER_PRINT, 0, 0, pData, sizeof(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT));
	return 0;
}

int CDeviceControl::SendSyncActivityMsg(DCLIENT_SYNC_ACTIVITY *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildSyncActivityMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildSyncActivityMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnReportRelayStatus(DCLIENT_REPORT_RELAY_STATUS *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportRelayStatusMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildReportRelayStatusMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnFlowOutOfLimit(DCLIENT_FLOW_OUT_OF_LIMIT *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildFlowOutOfLimitMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildFlowOutOfLimitMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnReportCallLog(DCLIENT_REPORT_CALLLOG *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildReportCallLogMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildReportCallLogMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnBackupConfigACK(DCLIENT_BACKUP_CONFIG_ACK *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildBackupConfigACKMsg(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildBackupConfigACKMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRequestRtspMonitor(DCLIENT_REQUEST_RTSP_MONITOR *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestRtspMonitor(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildRequestRtspMonitor failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendUdpMsg(pData->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUdpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRtspMonitorStop(DCLIENT_RTSP_MONITOR_STOP *pData)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRtspMonitorStop(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildRtspMonitorStop failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendUdpMsg(pData->szToIP, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendUdpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}


int CDeviceControl::OnRequestEndUserReg()
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestEndUserReg(&socketMsg) < 0)
	{
		rl_log_err("%s: BuildRequestEndUserReg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);

	return 0;
}

int CDeviceControl::OnBackUpConfig(DCLIENT_BACKUP_CONFIG& backupConfig)
{
	//创建新的线程上传数据
	DCLIENT_BACKUP_CONFIG *pBackupConfig = new DCLIENT_BACKUP_CONFIG;
	memset(pBackupConfig, 0, sizeof(DCLIENT_BACKUP_CONFIG));
	memcpy(pBackupConfig, &backupConfig, sizeof(DCLIENT_BACKUP_CONFIG));
	pthread_create(&m_tidBackupConfig, NULL, ProcessBackUpThread, pBackupConfig);
	return 0;	
}

int CDeviceControl::OnBackUpConfigRecovery(DCLIENT_BACKUP_CONFIG_RECOVERY& backupConfigRecovery)
{
	//创建新的线程上传数据
	DCLIENT_BACKUP_CONFIG_RECOVERY *pBackupConfigRecovery = new DCLIENT_BACKUP_CONFIG_RECOVERY;
	memset(pBackupConfigRecovery, 0, sizeof(DCLIENT_BACKUP_CONFIG_RECOVERY));
	memcpy(pBackupConfigRecovery, &backupConfigRecovery, sizeof(DCLIENT_BACKUP_CONFIG_RECOVERY));
	pthread_create(&m_tidBackupConfigRecovery, NULL, ProcessBackUpRecoveryThread, pBackupConfigRecovery);
	return 0;	
}

int CDeviceControl::OnReportKitDevice(DCLIENT_REPORT_KIT_DEVICE_LIST *pData, INT nAddMode)
{
	if(pData == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if(nAddMode == 1) //手动添加设备
	{
		if (GetMsgControlInstance()->BuildAddKitDevice(&socketMsg, pData) < 0)
		{
			rl_log_err("%s: BuildAddKitDevice failed.", __FUNCTION__);
			return -1;
		}
	}
	else //自动扫描设别
	{
		if (GetMsgControlInstance()->BuildReportKitDevice(&socketMsg, pData) < 0)
		{
			rl_log_err("%s: BuildReportKitDevice failed.", __FUNCTION__);
			return -1;
		}
	}

	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnRequestKitDevice()
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildRequestKitDevice(&socketMsg) < 0)
	{
		rl_log_err("%s: BuildRequestKitDevice failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::OnModifyDeviceLocation(KIT_DEVICE_BASE_INFO* pData)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildModifyDeviceLocation(&socketMsg, pData) < 0)
	{
		rl_log_err("%s: BuildModifyDeviceLocation failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::SendAlarm(DCLIENT_ALARM_MSG& alarmMsg, int nSendType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildAlarmMsg(&socketMsg, &alarmMsg) < 0)
	{
		rl_log_err("%s: BuildAlarmMsg failed.", __FUNCTION__);
		return -1;
	}
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		if(GetConnectControlInstance()->SendUdpMsg(alarmMsg.to_name, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDeviceControl::SendAlarmDeal(DCLIENT_ALARM_DEAL_INFO& alarmDeal, int nSendType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildAlarmDealMsg(&socketMsg, &alarmDeal) < 0)
	{
		rl_log_err("%s: BuildAlarmDealMsg failed.", __FUNCTION__);
		return -1;
	}
	if(nSendType == TRANSPORT_TYPE_UDP)
	{
		if(GetConnectControlInstance()->SendUdpMsg(alarmDeal.to_name, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUDPMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}

	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}

CDeviceControl *CDeviceControl::instance = NULL;

CDeviceControl *CDeviceControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CDeviceControl();
	}

	return instance;
}
#endif

