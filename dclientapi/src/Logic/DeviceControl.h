#ifndef __DEVICE_CONTROL_H__
#define __DEVICE_CONTROL_H__
#include "revision.h"
#include "Def.h"
#define AUTH_KEY_DIGITS		12

class CDeviceControl
{
public:
	CDeviceControl();
	~CDeviceControl();
	int Init();
	int OnDeviceListChangeNotify();
	int OnDeviceListInfo(char *pszBuf, int nSize);
	int OnLogoutSip();
	int OnCheckDtmfAck(SOCKET_MSG_CHECK_DTMF_ACK *pSocketCheckDtmfAck);
	int CheckDtmf(DCLIENT_CHECK_DTMF * pCheckDtmfMsg);
	int OnDeviceCode(SOCKET_MSG_DEVICE_CODE *pSocketDeviceCode);
	int OnReportDeviceCode(DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCode);
	int OnClearDeviceCode();
	int ReportDeviceCode(DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCode);
	int OnReportNetworkInfo(DCLIENT_REPORT_NETWORK_INFO *pReportNetworkInfo);
	int ReportNetworkInfo(DCLIENT_REPORT_NETWORK_INFO* pNetWorkInfo);
	int OnDoorMotionAlert(SOCKET_MSG_DOOR_MOTION_ALERT *pDoorMotionAlert);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int RequestConfigFromDevice(DCLIENT_REQUEST_CONFIG *requestCfgMsg);
	int OnReqCfgFromDevice(SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg);
#endif
	int OnReportCallCapture(DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture);
	int OnReportTrigger(DCLIENT_REPORT_TRIGGER *pReportTrigger);
#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR	
	int GetMotionAndRobinCallConfig();
	int SetPersonalAccountRobinCall(DCLIENT_MOTION_AND_ROBINCALL *pPersonalAccountRobinCall);
	int SetPersonalAccountMotion(DCLIENT_SET_PERSONAL_ACCOUNT_MOTION *pPersonalAccountMotion);
#endif	
	int OnManageAlarmMsg(SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg);
	int OnManageBroadcastMsg(DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg);
	int OnReportHealth(DCLIENT_REPORT_HEALTH *pReportHealth);
	int OnResponseSensorTriggerMsg(DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg);
	int OnUploadVideoNotifyMsg(DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg);
	int OnUploadCaptureNotifyMsg(DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg);
	int OnServerHeartBeatMsg();
#if RL_SUPPORT_DEVICE_HTTP_REGISTER
	int PersonalRegister(DCLIENT_PERSONAL_REGISTER* pPersonalRegister);
	int PersonalLogin(DCLIENT_PERSONAL_LOGIN* pPersonalLogin);
	int AddSlaveAccount(DCLIENT_ADD_SLAVE_ACCOUNT* pAddSlaveAccount);
	int GetSlaveAccountList(DCLIENT_GET_SLAVE_ACCOUNT_LIST* pGetSlaveAccountList);
	int DeleteSlaveAccount(DCLIENT_DEL_SLAVE_ACCOUNT* pDelSlaveAccount);
	int BindDeviceByDeviceCode(DCLIENT_BIND_DEVICE* pBindDevice);
	int EmailExit(DCLIENT_EMAIL_EXIST* pEmailExit);

	int ParseHttpCommonResponseAck(cJSON *json, DCLIENT_WEB_ACK_MSG *pWebAckMsg);
	int ParseHttpCommonResponseLoginAck(cJSON *json, DCLIENT_WEB_LOGIN_ACK_MSG *pWebLoginAckMsg);
	int ParseHttpPersonalUserListAck(cJSON *json, DLCIENT_SLAVE_ACCOUNT_LIST_MSG_ACK *pPersonalUserListMsg);
#endif
#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR
	int ParseHttpMotionAndRobinCallConf(CHAR *pszJsonBuf, DCLIENT_MOTION_AND_ROBINCALL *pMotionAndRobinCallMsg);
	int ParseHttpLabelAndKeyMapList(CHAR *pszJsonBuf, DCLIENT_ACK_APP_AND_INDOOR_LABEL *pLabelAndKeyMapList);
#endif
#if RL_SUPPORT_AUTH_CODE
	int GetAuthKey(CHAR *pszAuthKey, int nSize);
	int SetAuthKey(CHAR *pszAuthKey);
#endif
	int OnMaintenaceServerChange(SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange);
	int OnRequestAllTrigger(DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger);
	int OnReportAllTrigger(DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger);
	int OnRequestAllTrigger(SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger);
	int OnReportAllTrigger(SOCKET_MSG_REPORT_ALL_TRIGGER *pReporttAllTrigger);
#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
	int SendReportDoorStatus(DCLIENT_REPORT_DOORSTATUS *pDoorStatus);
#endif
#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
	int OnRecvReportDoorStatus(DCLIENT_REPORT_DOORSTATUS *pDoorStatus);
#endif
#if RL_SUPPORT_SEND_REPORT_GAS
	int SendReportGas(DCLIENT_REPORT_GAS *pReportGas);
#endif
	int SendReportVisitorInfo(DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo);
	int SendReportVisitorAuthInfo(DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo);
	int SendRequestOssSts(DCLIENT_REQUEST_OSS_STS *pDate);
	int RemoteControlOpenDoorResponse(DCLIENT_REMOTE_CONTROL_OPENDOOR *pData);
	int OnRemoteAccessWeb(DCLIENT_REMOTE_ACCESS_WEB *pData);	
	int RequestOpenDoor(DCLIENT_REQUEST_OPENDOOR *pData, int nType = 0);
	int OnOpenDoorACK(DCLIENT_OPENDOOR_ACK *pData);
	int OnRegisterFace(DCLIENT_FACE_INFO *pData);
	int OnModifyFace(DCLIENT_FACE_INFO *pData);
	int OnDeleteFace(DCLIENT_FACE_INFO *pData);
	int OnGSFaceHttpApiLogin(SOCKET_MSG_GSFACE_HTTPAPI *pData);
	int OnRequestACInfo(DCLIENT_REQUEST_ACINFO *pData);
	int OnSendDeliveryMsg(DCLIENT_SEND_DELIVERY *pData);
	int OnRequestPersonelData(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData);
	int OnSyncPersonelData(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData);
	int OnRequestFingerPrint(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData);
	int OnSyncFingerPrint(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData);
	int SendSyncActivityMsg(DCLIENT_SYNC_ACTIVITY *pData);
	int OnReportRelayStatus(DCLIENT_REPORT_RELAY_STATUS *pData);
	int OnFlowOutOfLimit(DCLIENT_FLOW_OUT_OF_LIMIT *pData);
	int OnReportCallLog(DCLIENT_REPORT_CALLLOG *pData);
	int OnBackupConfigACK(DCLIENT_BACKUP_CONFIG_ACK *pData);
	int OnRequestRtspMonitor(DCLIENT_REQUEST_RTSP_MONITOR *pData);
	int OnRtspMonitorStop(DCLIENT_RTSP_MONITOR_STOP *pData);
	int OnRequestEndUserReg();
	int OnBackUpConfig(DCLIENT_BACKUP_CONFIG& backupConfig);
	int OnBackUpConfigRecovery(DCLIENT_BACKUP_CONFIG_RECOVERY& backupConfigRecovery);
	int OnReportKitDevice(DCLIENT_REPORT_KIT_DEVICE_LIST *pData, INT nAddMode);
	int OnModifyDeviceLocation(KIT_DEVICE_BASE_INFO* pData);
	int OnRequestKitDevice();
	int SendAlarm(DCLIENT_ALARM_MSG& alarmMsg, int nSendType = TRANSPORT_TYPE_TCP);
	int SendAlarmDeal(DCLIENT_ALARM_DEAL_INFO& alarmDeal, int nSendType = TRANSPORT_TYPE_TCP);
	static CDeviceControl *GetInstance();
private:
	static CDeviceControl *instance;

private:
	CHAR m_szDeviceToken[DCLIENT_TOKEN_SIZE];
	CHAR m_szAuthCode[AUTH_CODE_SIZE];
	pthread_t m_tidBackupConfig;
	pthread_t m_tidBackupConfigRecovery;

};

CDeviceControl *GetDeviceControlInstance();

#endif

