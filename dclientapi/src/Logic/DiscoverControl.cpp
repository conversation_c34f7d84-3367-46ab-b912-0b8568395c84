#include "MsgControl.h"
#include "ConnectControl.h"
#include "DiscoverControl.h"
#include "SettingHandle.h"
#include "MsgHandle.h"
#include "dclient_ipc.h"
#include "Lock.h"
#include "DclientDefine.h"
#include "Utility.h"
#include "DClient.h"

#define DISCOVER_ADDR_FILE			"/tmp/discover.xml"

CDiscoverControl *GetDiscoverControlInstance()
{
	return CDiscoverControl::GetInstance();
}

CDiscoverControl::CDiscoverControl()
{
	memset(&m_timeLastSendDiscover, 0, sizeof(rl_time_val));
	memset(&m_timeLastReportAddr, 0, sizeof(rl_time_val));
	m_timeLastSendDiscover.sec = -10000; //如果系统获取不到时间时，memset可能与当前系统时间很接近，所以初始化这部分最好设置成-10000
	m_timeLastReportAddr.sec = -10000;

	m_bDeviceListUpdated = FALSE;
	m_nDiscoverSeqNum = 1;

	m_bHasReportAddrAfterDiscover = FALSE;
	m_bForceSendAutoDiscover = FALSE;
	m_bHasSendDiscover = FALSE;

	m_pDeviceHeader = NULL;

	m_lock = new CLock();
}

CDiscoverControl::~CDiscoverControl()
{
	if(m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
}

CDiscoverControl *CDiscoverControl::instance = NULL;

CDiscoverControl *CDiscoverControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CDiscoverControl();
	}

	return instance;
}


//处理定时器
int CDiscoverControl::ProcessBaseTimer()
{
	//初始化过并且没有连接上才处理定时器
	if(!GetConnectControlInstance()->HasInit() || (GetConnectControlInstance()->GetConnectMode() != DOORSETTING_CONNECT_SERVER_MODE_NONE)
		|| (GetSettingHandleInstance()->GetDiscoveryMode() == 0))
	{
		return 0;
	}
		
	rl_time_val curTime;
	rl_gettimeofday(&curTime);

	//判断当前是否要发送DISCOVER，距离上一次发送DISCOVER超过1小时
	UINT nSecondsFromLastDisvover = (UINT)rl_get_subtimeint(&m_timeLastSendDiscover, &curTime);
	if((nSecondsFromLastDisvover >= 3600) || m_bForceSendAutoDiscover)
	{
		//发送DISCOVER，发送之前要先清理一下update状态，这样可以确认设备列表是否发生变化，如果是强制刷新，那么清除掉整个表

		if(SendAutoDiscoverMsg() >= 0)//发送成功
		{
			if(m_bForceSendAutoDiscover)
			{
				DestoryDeviceList();
			}
			else
			{
				ClearDeviceUpdateStatus();
			}
		}

		m_timeLastSendDiscover = curTime;
		m_bHasReportAddrAfterDiscover = FALSE;
		m_bForceSendAutoDiscover = FALSE;
		m_bHasSendDiscover = TRUE;
		m_timeLastReportAddr = curTime;

		rl_log_info("Sent Discover");
	}
	//DISCOVER发出三秒后需要做第一次地址上报
	else if(m_bHasSendDiscover && (!m_bHasReportAddrAfterDiscover) && (nSecondsFromLastDisvover >= 3))
	{
		//遍历地址表，删除掉update=FALSE（3秒内未收到）的节点
		int nRemovedDevices = RemoveNonUpdatedDevices();
		if(nRemovedDevices > 0)
		{
			m_bDeviceListUpdated = TRUE;
		}

		m_bHasReportAddrAfterDiscover = TRUE;
	}

	//判断当前是否要上报地址表给phone, 如果地址改变了并且距离上一次上报超过3秒，则上报地址
	if(m_bDeviceListUpdated)
	{
		UINT nSecondsFromLastReport = (UINT)rl_get_subtimeint(&m_timeLastReportAddr, &curTime);
		if(nSecondsFromLastReport >= 3)
		{
			Lock();

			//上报ADDR, 生成addr.xml文件上报给phone
			if(GetMsgHandleInstance()->CreateAddressFile(DISCOVER_ADDR_FILE, m_pDeviceHeader) == 0)
			{
				m_timeLastReportAddr = curTime;
				DCLIENT_NOTIFY_HANDLE_FILE_INFO fileInfo;
				memset(&fileInfo, 0, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));
				rl_strcpy_s(fileInfo.szFilePath, sizeof(fileInfo.szFilePath), DISCOVER_ADDR_FILE);
				rl_strcpy_s(fileInfo.szMD5, sizeof(fileInfo.szMD5), "");
				ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_ADDRESS, 0, 1, &fileInfo, sizeof(DCLIENT_NOTIFY_HANDLE_FILE_INFO));

				rl_log_info("Reported Address succeeded.");

				//此时清除云和SDMC的地址簿MD5,避免从自组网连接云或SDMC时没有重新下载地址簿
				GetSettingHandleInstance()->SetContactMD5("");
				GetSettingHandleInstance()->SetAddrMD5("");
			}
			else
			{
				rl_log_info("Reported Address failed.");
			}

			Unlock();
			m_bDeviceListUpdated = FALSE;
		}
	}
	
	return 0;
}

//收到IPC消息MSG_P2D_AUTO_DISCOVER的处理
int CDiscoverControl::OnRecvAutoDiscover()
{
	m_bForceSendAutoDiscover = TRUE;

	return 0;
}

//组播自己的信息出去
int CDiscoverControl::MuticastDeviceInfo()
{
	if(!GetConnectControlInstance()->HasInit() || (GetConnectControlInstance()->GetConnectMode() != DOORSETTING_CONNECT_SERVER_MODE_NONE)
		|| (GetSettingHandleInstance()->GetDiscoveryMode() == 0))
	{
		return 0;
	}

	//发送DISCOVER ACK消息
	SOCKET_MSG_DISCOVER_ACK discoverAckMsg;
	memset(&discoverAckMsg, 0, sizeof(SOCKET_MSG_DISCOVER_ACK));

	CHAR szDeviceName[VALUE_SIZE] = {0};	
	CHAR szLocation[VALUE_SIZE] = {0};
	CHAR szDeviceCode[VALUE_SIZE] = {0};
	CHAR szMac[MAC_SIZE] = {0};

	GetSettingHandleInstance()->GetDeviceName(szDeviceName, sizeof(szDeviceName));
	GetSettingHandleInstance()->GetDeviceLocation(szLocation, sizeof(szLocation));
	GetSettingHandleInstance()->GetDeviceCode(szDeviceCode, sizeof(szDeviceCode));
	GetConnectControlInstance()->GetShortMac(szMac, sizeof(szMac));

	DCLIENT_DEVICE_INFO infoDevice;
	GetDClientInstance()->GetDeviceInfo(&infoDevice);
	rl_strcpy_s(discoverAckMsg.szIPAddr, sizeof(discoverAckMsg.szIPAddr), infoDevice.szIPAddr);
	rl_sprintf_s(discoverAckMsg.szFlag, sizeof(discoverAckMsg.szFlag), "%d", DISCOVER_FLAG_AUTO);
	rl_sprintf_s(discoverAckMsg.szDiscoverMethod, sizeof(discoverAckMsg.szDiscoverMethod), "%d", DISCOVER_METHOD_NORMAL);
	rl_strcpy_s(discoverAckMsg.szDeviceName, sizeof(discoverAckMsg.szDeviceName), !rl_str_isempty(szLocation) ? szLocation : szDeviceName);
	rl_strcpy_s(discoverAckMsg.szDeviceCode, sizeof(discoverAckMsg.szDeviceCode), szDeviceCode);
	rl_sprintf_s(discoverAckMsg.szRTSP, sizeof(discoverAckMsg.szRTSP), "rtsp://%s/live/ch00_0", infoDevice.szIPAddr);
	rl_strcpy_s(discoverAckMsg.szMac, sizeof(discoverAckMsg.szMac), szMac);
	rl_strcpy_s(discoverAckMsg.szModel, sizeof(discoverAckMsg.szModel), infoDevice.szModelName);
	discoverAckMsg.nSequenceNum = 0;
	
	return SendDiscoverAckMsg(NULL, &discoverAckMsg, SOCKET_MULTICAST_ADDR, SOCKET_MULTICAST_PORT);
}

//发送自动DISCOVER消息
int CDiscoverControl::SendAutoDiscoverMsg()
{
	//解析出type、deviceid和extension
	CHAR szDeviceID[DEVICE_ID_SIZE] = {0};
	CHAR szDeviceExtension[VALUE_SIZE]={0};
	CHAR szDeviceType[INT_SIZE]={0};

	GetSettingHandleInstance()->GetDeviceID(szDeviceID, sizeof(szDeviceID));
	GetSettingHandleInstance()->GetDeviceExtension(szDeviceExtension, sizeof(szDeviceExtension));
	GetSettingHandleInstance()->GetDeviceType(szDeviceType, sizeof(szDeviceType));
	INT nType = rl_atoi(szDeviceType);
	INT nExtension = rl_atoi(szDeviceExtension);

	
	DCLIENT_DISCOVER_SEND discoverMsg;
	rl_memset(&discoverMsg, 0, sizeof(DCLIENT_DISCOVER_SEND));
	discoverMsg.nType = nType;
	discoverMsg.nExtension = nExtension;
	discoverMsg.nFlag = DISCOVER_FLAG_AUTO;
	discoverMsg.nSequenceNum = m_nDiscoverSeqNum++;
	rl_strcpy_s(discoverMsg.szDeviceID, sizeof(discoverMsg.szDeviceID), szDeviceID);

	return SendDiscoverMsg(&discoverMsg);
}

int CDiscoverControl::SendDiscoverMsg(DCLIENT_DISCOVER_SEND *pDiscoverMsg)
{
	if(pDiscoverMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildDiscoverMsg(&socketMsg, pDiscoverMsg) < 0)
	{
		rl_log_err("%s: BuildDiscoverMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendMulticastMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		return -1;
	}
	rl_log_info("%s:success.", __FUNCTION__);
	return 0;
}

int CDiscoverControl::SendDiscoverMsg(CHAR *pDiscoverMsg, INT nFlag, INT nSequenceNum)
{
	if(pDiscoverMsg == NULL)
	{
		return -1;
	}
	//解析出type、deviceid和extension
	INT nType = -1;
	INT nExtension = -1;
	CHAR szDeviceID[DEVICE_ID_SIZE] = {0};
	if(rl_strchr(pDiscoverMsg, '_') == NULL)
	{
		sscanf(pDiscoverMsg, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
	}
	else
	{		
		sscanf(pDiscoverMsg, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
	}
	DCLIENT_DISCOVER_SEND discoverMsg;
	rl_memset(&discoverMsg, 0, sizeof(DCLIENT_DISCOVER_SEND));
	discoverMsg.nType = nType;
	discoverMsg.nExtension = nExtension;
	discoverMsg.nFlag = nFlag;
	discoverMsg.nSequenceNum = nSequenceNum;
	rl_strcpy_s(discoverMsg.szDeviceID, sizeof(discoverMsg.szDeviceID), szDeviceID);
	SendDiscoverMsg(&discoverMsg);
	return 0;
}


int CDiscoverControl::SendDiscoverAckMsg(SOCKET_MSG *pRecvMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, char *pszRemoteAddr, INT nRemotePort)
{
	if(pDiscoverAckMsg == NULL)
	{
		return -1;
	}
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildDiscoverAckMsg(&socketMsg, pDiscoverAckMsg) < 0)
	{
		rl_log_err("%s: BuildDiscoverAckMsg failed.", __FUNCTION__);
		return -1;
	}
#if RL_SUPPORT_ETHERNET_TRANSPORT
	if(pRecvMsg != NULL) 
	{		
		socketMsg.nPort = pRecvMsg->nPort;
		memcpy(socketMsg.byRemoteMac, pRecvMsg->byRemoteMac, sizeof(pRecvMsg->byRemoteMac));
		rl_strcpy_s(socketMsg.szRemoteAddr, sizeof(socketMsg.szRemoteAddr), pRecvMsg->szRemoteAddr);
		
		GetEthernetControlInstance()->Send(&socketMsg);
	}
	else
	{
		if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
		{
			return -1;
		}
	}
#else
	if(GetConnectControlInstance()->SendUdpMsg(pszRemoteAddr, nRemotePort, socketMsg.byData, socketMsg.nSize) < 0)
	{
		return -1;
	}
#endif
	rl_log_info("%s: Send DiscoverAckMsg Success.", __FUNCTION__);
	return 0;
}

int CDiscoverControl::OnDiscoverMsg(SOCKET_MSG *pRecvMsg, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, char *pszRemoteAddr, INT nRemotePort)
{
	if(pDiscoverMsg == NULL)
	{
		return -1;
	}

	//自己的DISCOVER不要收
	char szLocalIPAddr[IP_SIZE] = {0};
	GetConnectControlInstance()->GetLocalIPAddr(szLocalIPAddr, sizeof(szLocalIPAddr));
	if(rl_strcmp(pszRemoteAddr, szLocalIPAddr) == 0)
	{
		rl_log_info("%s:ignore mine muticast msg.", __FUNCTION__);
		return 0;
	}
	BOOL bMatch = FALSE;
	CHAR szDeviceID[DEVICE_ID_SIZE]={0};
	CHAR szDeviceType[INT_SIZE]={0};
	CHAR szDeviceExtension[INT_SIZE]={0};
	CHAR szIP[IP_SIZE]={0};
	CHAR szFW[DEVICE_SWVER_SIZE]={0};
	SOCKET_MSG_DISCOVER_ACK discoverAckMsg;
	memset(&discoverAckMsg, 0, sizeof(discoverAckMsg));

	GetSettingHandleInstance()->GetDeviceID(szDeviceID, sizeof(szDeviceID));
	GetSettingHandleInstance()->GetDeviceExtension(szDeviceExtension, sizeof(szDeviceExtension));
	GetSettingHandleInstance()->GetDeviceType(szDeviceType, sizeof(szDeviceType));

	rl_log_info("%s:from=%s:%d, discover=(%s_%s_%s), mine=(%s_%s_%s)", __FUNCTION__, 
		pszRemoteAddr, nRemotePort, 
		pDiscoverMsg->szType, pDiscoverMsg->szDeviceID, pDiscoverMsg->szExtension,
		szDeviceType, szDeviceID, szDeviceExtension);


	//type一样或者为NULL，且Extension一样为NULL，且DeviceID一样
	if(((atoi(pDiscoverMsg->szType) == -1 || rl_strcmp(pDiscoverMsg->szType, szDeviceType) == 0) 
		&& (atoi(pDiscoverMsg->szExtension) == -1 || rl_strcmp(pDiscoverMsg->szExtension, szDeviceExtension) == 0)
		&& (rl_strcmp(pDiscoverMsg->szDeviceID, szDeviceID) == 0))
		|| ((atoi(pDiscoverMsg->szType) == SDMC_DEVICE_TYPE_MANAGEMENT && atoi(szDeviceType) == SDMC_DEVICE_TYPE_MANAGEMENT && 
			rl_strcmp(pDiscoverMsg->szDeviceID, "0.0.0.0.0") == 0)))
	{		
		bMatch = TRUE;
	}
	else if(atoi(pDiscoverMsg->szFlag) == DISCOVER_MATCH_FLAG_MODEL)
	{
		int nModel = atoi(pDiscoverMsg->szType);
		if((nModel & RL_MODEL_R2X) != 0 && ((RL_MODELID >= 20 &&  RL_MODELID < 30) || (RL_MODELID >= 220 &&  RL_MODELID < 300)) ||
			(nModel & RL_MODEL_R4X) != 0 && (RL_MODELID >= 40 &&  RL_MODELID < 50) ||
			(nModel & RL_MODEL_IT8X) != 0 && (RL_MODELID >= 80 &&  RL_MODELID < 90))
		{
			bMatch = TRUE;
		}
	}
	else if(atoi(pDiscoverMsg->szFlag) == DISCOVER_MATCH_FLAG_TYPE)//忽略节点，只匹配类型
	{
		if((atoi(pDiscoverMsg->szType) == -1 || rl_strcmp(pDiscoverMsg->szType, szDeviceType) == 0))
		{
			bMatch = TRUE;
		}
	}
	else if((atoi(pDiscoverMsg->szFlag) == DISCOVER_MATCH_FLAG_NONE) || (atoi(pDiscoverMsg->szFlag) == DISCOVER_FLAG_AUTO))
	{
		bMatch = TRUE;
	}
	if(bMatch)
	{
		if(atoi(pDiscoverMsg->szDiscoverMethod) == DISCOVER_METHOD_REPORT_IP)
		{
			CHAR szDiscoverReportIP[IP_SIZE] = {0};
			rl_strcpy_s(szDiscoverReportIP, sizeof(szDiscoverReportIP), pszRemoteAddr);
			return ipc_send(IPC_ID_PHONE, MSG_D2P_DISCOVER_REPORT_IP, 0, 0, szDiscoverReportIP, sizeof(szDiscoverReportIP));
		}
		
		CHAR szDeviceName[VALUE_SIZE] = {0};	
		CHAR szLocation[VALUE_SIZE] = {0};
		CHAR szDeviceCode[VALUE_SIZE] = {0};
		CHAR szMac[MAC_SIZE] = {0};
		CHAR szModel[VALUE_SIZE] = {0};
		GetSettingHandleInstance()->GetDeviceName(szDeviceName, sizeof(szDeviceName));
		GetSettingHandleInstance()->GetDeviceLocation(szLocation, sizeof(szLocation));
		GetSettingHandleInstance()->GetDeviceCode(szDeviceCode, sizeof(szDeviceCode));
		GetSettingHandleInstance()->GetLanIPAddr(szIP,sizeof(szIP));		
		GetSettingHandleInstance()->GetSWVer(szFW, sizeof(szFW));
		GetSettingHandleInstance()->GetModel(szModel, sizeof(szModel));
		GetConnectControlInstance()->GetShortMac(szMac, sizeof(szMac));
		rl_strcpy_s(discoverAckMsg.szDeviceID, sizeof(discoverAckMsg.szDeviceID), szDeviceID);
		rl_strcpy_s(discoverAckMsg.szExtension, sizeof(discoverAckMsg.szExtension), szDeviceExtension);
		rl_strcpy_s(discoverAckMsg.szType, sizeof(discoverAckMsg.szType), szDeviceType);
		rl_strcpy_s(discoverAckMsg.szIPAddr, sizeof(discoverAckMsg.szIPAddr), szIP);
		rl_strcpy_s(discoverAckMsg.szSWVer, sizeof(discoverAckMsg.szSWVer), szFW);		
		rl_strcpy_s(discoverAckMsg.szFlag, sizeof(discoverAckMsg.szFlag), pDiscoverMsg->szFlag);
		rl_strcpy_s(discoverAckMsg.szDiscoverMethod, sizeof(discoverAckMsg.szDiscoverMethod), pDiscoverMsg->szDiscoverMethod);
		rl_strcpy_s(discoverAckMsg.szDeviceName, sizeof(discoverAckMsg.szDeviceName), !rl_str_isempty(szLocation) ? szLocation : szDeviceName);
		rl_strcpy_s(discoverAckMsg.szDeviceCode, sizeof(discoverAckMsg.szDeviceCode), szDeviceCode);
		rl_sprintf_s(discoverAckMsg.szRTSP, sizeof(discoverAckMsg.szRTSP), "rtsp://%s/live/ch00_0", szIP);
		rl_strcpy_s(discoverAckMsg.szMac, sizeof(discoverAckMsg.szMac), szMac);
		rl_strcpy_s(discoverAckMsg.szModel, sizeof(discoverAckMsg.szModel), szModel);
		discoverAckMsg.nSequenceNum = pDiscoverMsg->nSequenceNum;
		
		//IP地址不合法则返回
		if(!IsValidIPAddr(szIP))
		{
			return -1;
		}

		SendDiscoverAckMsg(pRecvMsg, &discoverAckMsg, pszRemoteAddr, nRemotePort);
	}
	return 0;
}

int CDiscoverControl::OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, char *pszRemoteAddr, INT nRemotePort)
{
	if(pDiscoverAckMsg == NULL)
	{
		return -1;
	}

	rl_log_info("%s", __FUNCTION__);

	int nFlag = atoi(pDiscoverAckMsg->szFlag);

	//如果是AUTO的DISCOVER直接先处理掉
	if(nFlag == DISCOVER_FLAG_AUTO)
	{
		char szLocalIPAddr[IP_SIZE] = {0};
		GetConnectControlInstance()->GetLocalIPAddr(szLocalIPAddr, sizeof(szLocalIPAddr));
		if(rl_strcmp(pszRemoteAddr, szLocalIPAddr) == 0)
		{
			rl_log_info("%s:ignore mine muticast msg.", __FUNCTION__);
			return 0;
		}

		DISCOVER_DEVICE_ADDR deviceAddr;
		memset(&deviceAddr, 0, sizeof(DISCOVER_DEVICE_ADDR));

		deviceAddr.nType = rl_atoi(pDiscoverAckMsg->szType);
		deviceAddr.nExtension = rl_atoi(pDiscoverAckMsg->szExtension);
		rl_strcpy_s(deviceAddr.szDeviceID, sizeof(deviceAddr.szDeviceID), pDiscoverAckMsg->szDeviceID);
		rl_strcpy_s(deviceAddr.szName, sizeof(deviceAddr.szName), pDiscoverAckMsg->szDeviceName);
		rl_strcpy_s(deviceAddr.szIP, sizeof(deviceAddr.szIP), pDiscoverAckMsg->szIPAddr);
		rl_strcpy_s(deviceAddr.szMac, sizeof(deviceAddr.szMac), pDiscoverAckMsg->szMac);
		AddDeviceToList(&deviceAddr);
		return 0;	
	}


	DCLIENT_DISCOVER_ACK discoverAckMsg;
	memset(&discoverAckMsg, 0, sizeof(discoverAckMsg));
	discoverAckMsg.nType = atoi(pDiscoverAckMsg->szType);
	discoverAckMsg.nExtension = atoi(pDiscoverAckMsg->szExtension);
	discoverAckMsg.nDeviceMethod = atoi(pDiscoverAckMsg->szDiscoverMethod);
	rl_strcpy_s(discoverAckMsg.szDeviceID, sizeof(discoverAckMsg.szDeviceID), pDiscoverAckMsg->szDeviceID);
	rl_strcpy_s(discoverAckMsg.szIP, sizeof(discoverAckMsg.szIP), pDiscoverAckMsg->szIPAddr);
	rl_strcpy_s(discoverAckMsg.szFW, sizeof(discoverAckMsg.szFW), pDiscoverAckMsg->szSWVer);
	rl_strcpy_s(discoverAckMsg.szDeviceName, sizeof(discoverAckMsg.szDeviceName), pDiscoverAckMsg->szDeviceName);
	rl_strcpy_s(discoverAckMsg.szRTSP, sizeof(discoverAckMsg.szRTSP), pDiscoverAckMsg->szRTSP);
	rl_strcpy_s(discoverAckMsg.szDeviceCode, sizeof(discoverAckMsg.szDeviceCode), pDiscoverAckMsg->szDeviceCode);
	rl_strcpy_s(discoverAckMsg.szMAC, sizeof(discoverAckMsg.szMAC), pDiscoverAckMsg->szMac);
	discoverAckMsg.nSequenceNum = pDiscoverAckMsg->nSequenceNum;	

	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_DISCOVER_ACK, 0, 0, &discoverAckMsg, sizeof(discoverAckMsg));

}

//销毁设备地址表
void CDiscoverControl::DestoryDeviceList()
{
	Lock();
	DISCOVER_DEVICE_ADDR *pCurNode = m_pDeviceHeader;
	while(pCurNode != NULL)
	{
		DISCOVER_DEVICE_ADDR *pTmpNode = pCurNode;
		pCurNode = pCurNode->next;

		delete pTmpNode;
	}

	m_pDeviceHeader = NULL;
	Unlock();
}

//清除设备地址表中的update状态
void CDiscoverControl::ClearDeviceUpdateStatus()
{
	Lock();
	DISCOVER_DEVICE_ADDR *pCurNode = m_pDeviceHeader;
	while(pCurNode != NULL)
	{
		pCurNode->bUpdated = FALSE;
		pCurNode = pCurNode->next;
	}

	Unlock();
}

//添加设备节点到设备地址表中
int CDiscoverControl::AddDeviceToList(DISCOVER_DEVICE_ADDR *pDeviceAddr)
{
	if(pDeviceAddr == NULL)
	{
		return -1;
	}

	DISCOVER_DEVICE_ADDR *pNewNode = new DISCOVER_DEVICE_ADDR;
	memcpy(pNewNode, pDeviceAddr, sizeof(DISCOVER_DEVICE_ADDR));
	pNewNode->next = NULL;
	pNewNode->bUpdated = TRUE;

	rl_log_debug("%s:%d_%s-%d,%s", __FUNCTION__, pNewNode->nType, pNewNode->szDeviceID, pNewNode->nExtension, pNewNode->szMac);

	Lock();
	if(m_pDeviceHeader == NULL)
	{
		m_pDeviceHeader = pNewNode;
	}
	else
	{
		DISCOVER_DEVICE_ADDR *pCurNode = m_pDeviceHeader;
		DISCOVER_DEVICE_ADDR *pLastNode = m_pDeviceHeader;
		while(pCurNode != NULL)
		{
			//判断是否存在相同ID和EXTENSION的节点，有则替换，无则添加
			if(!rl_strcmp(pCurNode->szMac, pNewNode->szMac))
			{
				//判断是否完全一样(除了指针外的结构体数据)，完全一样则不做处理
				if((!rl_strcmp(pCurNode->szDeviceID, pNewNode->szDeviceID)) &&
					(pCurNode->nExtension == pNewNode->nExtension) &&
					(pCurNode->nType == pNewNode->nType) &&
					(!rl_strcmp(pCurNode->szName, pNewNode->szName)) &&
					(!rl_strcmp(pCurNode->szIP, pNewNode->szIP)))
				{
					pCurNode->bUpdated = TRUE;
					Unlock();
					delete pNewNode;
					return -1;
				}
				else
				{
					//除了next指针，其余全部拷贝
					memcpy(pCurNode, pNewNode, sizeof(DISCOVER_DEVICE_ADDR) - sizeof(pNewNode->next));
					Unlock();
					delete pNewNode;
					m_bDeviceListUpdated = TRUE;
					return 0;
				}
			}
			
			pLastNode = pCurNode;
			pCurNode = pCurNode->next;
		}

		pLastNode->next = pNewNode;
	}

	Unlock();

	m_bDeviceListUpdated = TRUE;
	return 0;
}

//移除没有更新的设备，返回移除的设备的数量
int CDiscoverControl::RemoveNonUpdatedDevices()
{
	int nRemovedDevices = 0;
	Lock();

	DISCOVER_DEVICE_ADDR *pCurNode = m_pDeviceHeader;
	DISCOVER_DEVICE_ADDR *pLastNode = m_pDeviceHeader;
	while(pCurNode != NULL)
	{
		DISCOVER_DEVICE_ADDR *pTmpNode = pCurNode;
		pCurNode = pCurNode->next;

		if(!pTmpNode->bUpdated)
		{
			if(pTmpNode == m_pDeviceHeader)
			{
				m_pDeviceHeader = pCurNode;
				pLastNode = m_pDeviceHeader;
			}
			else
			{
				pLastNode->next = pCurNode;
			}
			delete pTmpNode;
			pTmpNode = NULL;
			nRemovedDevices++;
		}
		else
		{
			pLastNode = pTmpNode;
		}
		
	}

	Unlock();
	return nRemovedDevices;
}

//上锁
void CDiscoverControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁
void CDiscoverControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}



