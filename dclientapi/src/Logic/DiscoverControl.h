#ifndef __DISCOVER_CONTROL_H__
#define __DISCOVER_CONTROL_H__



class CDiscoverControl
{
public:
	CDiscoverControl();
	~CDiscoverControl();
	
	static CDiscoverControl *GetInstance();

	int ProcessBaseTimer();

	//收到IPC消息MSG_P2D_AUTO_DISCOVER的处理
	int OnRecvAutoDiscover();

	//组播自己的信息出去
	int MuticastDeviceInfo();

	//发送自动DISCOVER消息
	int SendAutoDiscoverMsg();
	int SendDiscoverMsg(DCLIENT_DISCOVER_SEND *pDiscoverMsg);
	int SendDiscoverMsg(CHAR *pDiscoverMsg, INT nFlag = DISCOVER_FLAG_PHONE, INT nSequenceNum = 0);
	int SendDiscoverAckMsg(SOCKET_MSG *pRecvMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, char *pszRemoteAddr, INT nRemotePort);
	int OnDiscoverMsg(SOCKET_MSG *pRecvMsg, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, char *pszRemoteAddr, INT nRemotePort);
	int OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, char *pszRemoteAddr, INT nRemotePort);

	//销毁设备地址表
	void DestoryDeviceList();

	//清除设备地址表中的update状态
	void ClearDeviceUpdateStatus();

	//添加设备节点到设备地址表中
	int AddDeviceToList(DISCOVER_DEVICE_ADDR *pDeviceAddr);

	//移除没有更新的设备，返回移除的设备的数量
	int RemoveNonUpdatedDevices();

private:
	static CDiscoverControl *instance;

	void Lock();
	void Unlock();

	rl_time_val m_timeLastSendDiscover;
	rl_time_val m_timeLastReportAddr;
	BOOL m_bDeviceListUpdated;
	UINT m_nDiscoverSeqNum;
	DISCOVER_DEVICE_ADDR *m_pDeviceHeader;
	BOOL m_bHasReportAddrAfterDiscover;
	BOOL m_bForceSendAutoDiscover;
	BOOL m_bHasSendDiscover;

	void *m_lock;
};

CDiscoverControl *GetDiscoverControlInstance();

#endif
