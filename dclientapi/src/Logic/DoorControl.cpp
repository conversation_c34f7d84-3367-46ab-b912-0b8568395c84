#include "DoorControl.h"
#include "ConnectControl.h"
#include "MsgControl.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "Lock.h"


CDoorControl *GetDoorControlInstance()
{
        return CDoorControl::GetInstance();
}

CDoorControl::CDoorControl()
{

}

CDoorControl::~CDoorControl()
{

}

int CDoorControl::Check<PERSON>ey(int type, char *key)
{
		rl_log_debug("check key : %s ",key);
		SOCKET_MSG socketMsg;
		if(GetMsgControlInstance()->BuildCheckKeyMsg(&socketMsg,type,key) < 0)
		{
			return -1;
		}
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			return -1;
		}
        return 0;
}


int CDoorControl::CheckKeyForResult(char *result, char *info)
{
        //ipc_send(IPC_ID_PHONE, MSG_D2P_CHECK_KEY, rl_strcmp(result, "OK")==0?1:0, 0, (void *)info, rl_strlen(info) + 1);      
	return 0;  
}

CDoorControl *CDoorControl::instance = NULL;

CDoorControl *CDoorControl::GetInstance()
{
        if(instance == NULL)
        {
                instance = new CDoorControl();
        }

        return instance;
}
#if RL_GLOBAL_SUPPORT_TMP_KEY
int CDoorControl::SendCheckTmpKey(DCLIENT_CHECK_TMP_KEY *pCheckTmpKey)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildCheckTmpKeyMsg(&socketMsg, pCheckTmpKey) < 0)
	{
		rl_log_err("%s: BuildCheckTmpKey failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug("SendCheckTmpKey success.");
	return 0;
}
#endif

