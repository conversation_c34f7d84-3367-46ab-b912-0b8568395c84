#ifndef __DOOR_CONTROL_H__
#define __DOOR_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "dclient_ipc.h"

class CDoorControl
{
public:
        CDoorControl();
        ~CDoorControl();

        static CDoorControl *GetInstance();

        int CheckKey(int type, char *key);
        int CheckKeyForResult(char *result, char *info);
#if RL_GLOBAL_SUPPORT_TMP_KEY		
		int SendCheckTmpKey(DCLIENT_CHECK_TMP_KEY *pCheckTmpKey);
#endif

private:
        static CDoorControl *instance;
};

CDoorControl *GetDoorControlInstance();

#endif
