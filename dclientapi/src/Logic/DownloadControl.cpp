#include "DownloadControl.h"
#include "Utility.h"
#include "Lock.h"
#include "WaitEvent.h"
#include "Control.h"
#include "dclient_ipc.h"
#include "DeviceControl.h"
#include "D2ChannelControl.h"
#include "DClient.h"

pthread_t tidDclientDownload = 0;


void *ProcessDownloadThread(void *pData)
{
	CDownloadControl *pDownloadControl = (CDownloadControl*)pData;
	
	rl_log_info("start download thread...");
	while(!pDownloadControl->IsUnInit())
	{
		pDownloadControl->ProcessMsg();
	}

	return NULL;
}

CDownloadControl *GetDownloadControlInstance()
{
	return CDownloadControl::GetInstance();
}

CDownloadControl::CDownloadControl()
	: m_bUnInit(true)
{
	m_msgHeader = NULL;

	m_lock = new CLock();
	m_wait = new CWaitEvent();
}

CDownloadControl::~CDownloadControl()
{
	DelAllMsg();

	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
	if(NULL != m_wait)
	{
		delete (CWaitEvent *)m_wait;
		m_wait = NULL;
	}
}

CDownloadControl *CDownloadControl::instance = NULL;

CDownloadControl *CDownloadControl::GetInstance()
{
    if(instance == NULL)
    {
            instance = new CDownloadControl();
    }

    return instance;
}


/*@function
*******************************************************************
功  能:  判断是否已经反初始化.

参  数:  

返回值:  是否已经反初始化.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.29
******************************************************************/
bool CDownloadControl::IsUnInit()
{
	return m_bUnInit;
}

int CDownloadControl::Init()
{
	m_bUnInit = false;
	DownLoadThreadInit();
	srand((unsigned int)time(0));
	return 0;
}


/*@function
*******************************************************************
功  能:  反初始化.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.29
******************************************************************/
void CDownloadControl::UnInit()
{
	m_bUnInit = true;
	SetWaitEvent();
	if (tidDclientDownload != 0)
	{
		pthread_join(tidDclientDownload, NULL);
	}
}

int CDownloadControl::DownLoadThreadInit()
{
	pthread_create(&tidDclientDownload, NULL, ProcessDownloadThread, this);
 	return RL_SUCCESS;
}

//上锁消息缓冲区
void CDownloadControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁消息缓冲区
void CDownloadControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}

//设置事件
void CDownloadControl::SetWaitEvent()
{
	((CWaitEvent *)m_wait)->Set();
}

//清除事件
void CDownloadControl::ResetWaitEvent()
{
	((CWaitEvent *)m_wait)->Reset();
}

//等待事件触发
void CDownloadControl::WaitForEvent()
{
	((CWaitEvent *)m_wait)->Wait();
}


//处理消息
int CDownloadControl::ProcessMsg()
{
	WaitForEvent();
	Lock();
	MESSAGE *tmpNode = NULL;

	while(m_msgHeader != NULL)
	{
		tmpNode = (MESSAGE *)m_msgHeader;
		m_msgHeader = ((MESSAGE *)m_msgHeader)->next;
		Unlock();
		OnMessage(tmpNode->id, tmpNode->wParam, tmpNode->lParam, tmpNode->lpData);
		Lock();
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}
		delete(tmpNode);
	}

	m_msgHeader = NULL;

	ResetWaitEvent();

	Unlock();

	return 0;
}

//增加一个新的消息
int CDownloadControl::AddMsg(UINT id, UINT wParam, UINT lParam, void *lpData, int nDataLen)
{
	Lock();
	MESSAGE *curNode = NULL;
	MESSAGE *newNode = new MESSAGE();
	if(NULL == newNode)
	{
		Unlock();
		return -1;
	}

	memset(newNode, 0, sizeof(MESSAGE));

	newNode->id = id;
	newNode->wParam = wParam;
	newNode->lParam = lParam;
	if((lpData != NULL) && (nDataLen > 0))
	{
		newNode->lpData = new char[nDataLen];
		memcpy(newNode->lpData, lpData, nDataLen);
	}

	if(m_msgHeader == NULL)
	{
		m_msgHeader = newNode;
	}
	else
	{
		curNode = (MESSAGE *)m_msgHeader;
		while((curNode != NULL) && (curNode->next != NULL))
		{
			curNode = curNode->next;
		}
		curNode->next = newNode;
	}
	SetWaitEvent();

	Unlock();

	return 0;
}


//删除所有消息
int CDownloadControl::DelAllMsg()
{
	Lock();

	MESSAGE *curNode = NULL;
	MESSAGE *tmpNode = NULL;

	curNode = (MESSAGE *)m_msgHeader;

	while(curNode != NULL)
	{
		tmpNode = curNode;
		curNode = curNode->next;
		if(tmpNode->lpData != NULL)
		{
			delete [](char *)tmpNode->lpData;
		}

		delete tmpNode;
	}

	m_msgHeader = NULL;

	Unlock();

	return 0;
}

//消息处理句柄
int CDownloadControl::OnMessage(UINT msg, UINT wParam, UINT lParam, void *lpData)
{
	int nMsgType = msg;
	if(nMsgType & RFO_REQUEST_MASK)
	{
		OnRfoRequestMessage(msg, wParam, lParam, lpData);
		return 0;
	}
	switch(nMsgType)
	{
	case DOWNLOAD_MSG_BACKUP_CONFIG_RECOVERY:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", "/tmp/backup_config_", nRand, ".tgz");			
			UnlinkFileByKeyWord("/tmp/", "backup_config_");
			rl_log_info("process DOWNLOAD_MSG_BACKUP_CONFIG_RECOVERY");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE_LARGE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match remote MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_BACKUP_CONFIG_RECOVERY_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_PRIVATEKEY:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_PRIVATEKEY, nRand, SUFFIX_XML);
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_PRIVATEKEY_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_PRIVATEKEY");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				char szTmp[URL_SIZE] = {0};
				rl_sprintf_s(szTmp, sizeof(szTmp), "download failed:url=%s", szUrl);
				GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_PRIKEY_DW_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					char szTmp[URL_SIZE] = {0};
					rl_sprintf_s(szTmp, sizeof(szTmp), "The file MD5(%s) not match keysend MD5(%s)", szCurrentMd5, pDownloadInfo->szMD5);
					GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_PRIKEY_DW_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_PRIVATEKEY_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_RFID:
		{		
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_RFID, nRand, SUFFIX_XML);		
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_RFID_PRIFIX);
			rl_log_info("process DOWNLOAD_FILE_RFID");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				char szTmp[URL_SIZE] = {0};
				rl_sprintf_s(szTmp, sizeof(szTmp), "download failed:url=%s", szUrl);
				GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_RFCARD_DW_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					char szTmp[URL_SIZE] = {0};
					rl_sprintf_s(szTmp, sizeof(szTmp), "The file MD5(%s) not match keysend MD5(%s)", szCurrentMd5, pDownloadInfo->szMD5);
					GetD2ChannelControlInstance()->AddMsg(DCLIENT2_ALARM_MSG_RFCARD_DW_FAILED, 0, 0, szTmp, rl_strlen(szTmp)+1);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_RFID_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_ADDRESS:
		{		
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_ADDRESS, nRand, SUFFIX_XML);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_ADDRESS_PRIFIX);
			rl_log_info("process DOWNLOAD_FILE_ADDRESS");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_ADDRESS_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	
	case DOWNLOAD_MSG_AD:
		{
			DOWNLOAD_INFO *pDownloadInfo = (DOWNLOAD_INFO *)lpData;
			if(pDownloadInfo == NULL)
			{
				rl_log_err("download failed:pDownloadInfo=NULL");
				return -1;
			}
			
			CHAR *pszLocalFile = pDownloadInfo->szLocalFile;
			CHAR *pszUrl = pDownloadInfo->szUrl;

			rl_log_info("process DOWNLOAD_FILE_AD %s",  pszUrl);
			if(DownloadUrlToFileByCurl(pszUrl, pszLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX) < 0)
			{
				rl_log_err("download failed:url=%s", pszUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", pszUrl, pszLocalFile);
			char szChmodCmd[VALUE_SIZE] = { 0 };
			rl_sprintf_s(szChmodCmd, sizeof(szChmodCmd), "chmod 777 %s", pszLocalFile);
			system(szChmodCmd);
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_AD_DONE, wParam, lParam, NULL, 0);
		}
		break;

	case DOWNLOAD_MSG_CONFIG:
		{			
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};			
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%s%.10d%s", GetDClientInstance()->GetDownloadFolderPath().c_str(), "config_", nRand, SUFFIX_CFG);				
			char szFolderPath[DCLIENT_URL_SIZE] = {0};
			rl_strcpy_s(szFolderPath, sizeof(szFolderPath), GetDClientInstance()->GetDownloadFolderPath().c_str());
			UnlinkFileByKeyWord(szFolderPath, DOWNLOAD_FILE_CONFIG_PRIFIX);
			rl_log_info("process DOWNLOAD_FILE_CONFIG");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_CONFIG_DONE, rl_strstr(szUrl, "000000000001.cfg") != NULL ? 1 : 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_ADMODULE:
		{
			char *pszLocalFile = DOWNLOAD_FILE_ADMODULE;
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID)
			char cmd[512] = {0};
			rl_sprintf_s(cmd, sizeof(cmd), "mkdir -p %s", DOWNLOAD_PATH_ADMODULE);
			if(rl_system_ex(cmd, 10, 2) < 0) {
				rl_log_err("%s: exec(%s) failed.", __FUNCTION__, cmd);
				return -1;
			}
#endif
			rl_log_info("process DOWNLOAD_FILE_ADMODULE");
			//check the file size before download
			long nDownloadFileLenth = GetDownloadFileSizeByCurl((char *)lpData);
			if(nDownloadFileLenth > DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX)
			{
				rl_log_info("The AD size(%d) > limit size(%d),cancel download",nDownloadFileLenth, DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX);
				return -1;
			}
			unlink(DOWNLOAD_FILE_ADMODULE);	
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, pszLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, pszLocalFile);
			if(rl_file_exists(pszLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(pszLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, pszLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_ADMODULE_DONE, 0, 0, pszLocalFile, rl_strlen(pszLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_COMMUNITY_PHONEBOOK:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_COMMUNITY_PHONEBOOK, nRand, SUFFIX_CSV);
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_COMMUNITY_PHONEBOOK_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_COMMUNITY_PHONEBOOK");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_COMMUNITY_PHONEBOOK_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);	
		}
		break;
	case DOWNLOAD_MSG_AKCS_CONTACT:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%s%.10d%s", GetDClientInstance()->GetDownloadFolderPath().c_str(), DOWNLOAD_FILE_AKCS_CONTACT_PRIFIX, nRand, SUFFIX_XML);			
			char szFolderPath[DCLIENT_URL_SIZE] = {0};
			rl_strcpy_s(szFolderPath, sizeof(szFolderPath), GetDClientInstance()->GetDownloadFolderPath().c_str());
			UnlinkFileByKeyWord(szFolderPath, DOWNLOAD_FILE_AKCS_CONTACT_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_AKCS_CONTACT");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}

			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_AKCS_CONTACT_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
	case DOWNLOAD_MSG_FACEID:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_FACEID, nRand, SUFFIX_XML);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_FACEID_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_FACEID");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_FACEID_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_DOWNLOAD_FACEPIC_XML:
	case DOWNLOAD_MSG_SYNC_FACE_PIC:
	case DOWNLOAD_MSG_SYNC_FACE_PIC_AES:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_DOWNLOAD_FACEPIC, nRand, SUFFIX_XML);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_DOWNLOAD_FACEPIC_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_DOWNLOAD_FACEPIC_XML");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			if(nMsgType == DOWNLOAD_MSG_SYNC_FACE_PIC)
			{
				GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
			}
			else if(nMsgType == DOWNLOAD_MSG_SYNC_FACE_PIC_AES)
			{
				GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_AES_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
			}
			else
			{
				GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_D_FACEPIC_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
			}
		}
		break;
	case DOWNLOAD_MSG_FACE_DATA:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_FACE_DATA, nRand, SUFFIX_TGZ);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_FACEDATA_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_FACE_DATA");
			if(DownloadUrlToFileByCurl((char*)lpData, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", (char*)lpData);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", (char*)lpData, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_FACEDATA_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;

#endif
#if RL_SUPPORT_FP_DOWNLOAD
	case DOWNLOAD_MSG_FP:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_FP, nRand, SUFFIX_TGZ);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_FP_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_FP");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_FP_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
#endif
	case DOWNLOAD_MSG_TZ:
	case DOWNLOAD_MSG_TZDATA:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", DOWNLOAD_FILE_TIME_ZONE, nRand, SUFFIX_XML);			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, DOWNLOAD_FILE_TIME_ZONE_PRIFIX);
			rl_log_info("process DOWNLOAD_MSG_TZ/DOWNLOAD_MSG_TZDATA");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_TZ_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_AC_INFO:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", "/tmp/acInfo_", nRand, ".json");			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, "acInfo_");
			rl_log_info("process DOWNLOAD_MSG_AC_INFO");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_ACINFO_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_AC_META:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", "/tmp/acMeta_", nRand, ".json");			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, "acMeta_");
			rl_log_info("process DOWNLOAD_MSG_AC_META");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_ACMETA_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	case DOWNLOAD_MSG_SCHEDULE:
		{
			int nRand = (1 + rand()%0xFFFFFFF0);
			char szLocalFile[FILE_NAME_SIZE] = {0};
			rl_sprintf_s(szLocalFile, sizeof(szLocalFile), "%s%.10d%s", "/tmp/schedule_", nRand, ".json");			
			UnlinkFileByKeyWord(DOWNLOAD_FILE_PATH, "schedule_");
			rl_log_info("process DOWNLOAD_MSG_SCHEDULE");
			DOWNLOAD_SERVER_INFO *pDownloadInfo = (DOWNLOAD_SERVER_INFO*)lpData;
			char szUrl[URL_SIZE] = {0};
			if(pDownloadInfo != NULL)
			{
				rl_strcpy_s(szUrl, sizeof(szUrl), pDownloadInfo->szUrl);
			}
			if(DownloadUrlToFileByCurl(szUrl, szLocalFile, DCLIENT_DOWNLOAD_RETRY_COUNT, DCLIENT_DOWNLOAD_FILE_SIZE) < 0)
			{
				rl_log_err("download failed:url=%s", szUrl);
				return -1;
			}
			
			rl_log_info("download success:url=%s, local=%s", szUrl, szLocalFile);
			if(rl_file_exists(szLocalFile))
			{
				char szCurrentMd5[MD5_SIZE] = {0};
				GetFileMD5(szLocalFile, szCurrentMd5, MD5_SIZE);
				rl_log_info("%s: file(%s) MD5:%s",__FUNCTION__, szLocalFile, szCurrentMd5);
				if(pDownloadInfo && rl_strcmp(szCurrentMd5, pDownloadInfo->szMD5) != 0)
				{
					rl_log_err("%s:The file MD5(%s) not match keysend MD5(%s)",__FUNCTION__, szCurrentMd5, pDownloadInfo->szMD5);
					break;
				}
			}
			GetControlInstance()->AddMsg(MSG_CTRL_DOWNLOAD_SCHEDULE_DONE, 0, 0, szLocalFile, rl_strlen(szLocalFile) + 1);
		}
		break;
	default:
		break;
	}

	return 0;
}

int CDownloadControl::OnRfoRequestMessage(UINT msg, UINT wParam, UINT lParam, void *lpData)
{
	int nMsgType = msg;
	switch(nMsgType)
	{
#if RL_SUPPORT_DEVICE_HTTP_REGISTER
		case RFO_REQUEST_MSG_PERSONAL_REGISTER:
		{
			GetDeviceControlInstance()->PersonalRegister((DCLIENT_PERSONAL_REGISTER*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_PERSONAL_LOGIN:
		{
			GetDeviceControlInstance()->PersonalLogin((DCLIENT_PERSONAL_LOGIN*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_ADD_SLAVE_ACCOUNT:
		{
			GetDeviceControlInstance()->AddSlaveAccount((DCLIENT_ADD_SLAVE_ACCOUNT*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_GET_SLAVE_ACCOUNTLIST:
		{
			GetDeviceControlInstance()->GetSlaveAccountList((DCLIENT_GET_SLAVE_ACCOUNT_LIST*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_DEL_SLAVE_ACCOUNT:
		{
			GetDeviceControlInstance()->DeleteSlaveAccount((DCLIENT_DEL_SLAVE_ACCOUNT*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_BIND_DEVICE_BY_DEVICE_CODE:
		{
			GetDeviceControlInstance()->BindDeviceByDeviceCode((DCLIENT_BIND_DEVICE*) lpData);
		}
		break;
	case RFO_REQUEST_MSG_EMAIL_EXIT:
		{
			GetDeviceControlInstance()->EmailExit((DCLIENT_EMAIL_EXIST*) lpData);
		}
		break;
#endif
#if RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR
	case RFO_REQUEST_MSG_GET_MOTION_ROBCALL_CFG:
		{
			GetDeviceControlInstance()->GetMotionAndRobinCallConfig();
		}
		break;
	case RFO_REQUEST_MSG_SET_PER_ACC_MOTION:
		{
			GetDeviceControlInstance()->SetPersonalAccountMotion((DCLIENT_SET_PERSONAL_ACCOUNT_MOTION*)lpData);	
		}
		break;
	case RFO_REQUEST_MSG_SET_PER_ACC_ROBINCALL:
		{
			GetDeviceControlInstance()->SetPersonalAccountRobinCall((DCLIENT_MOTION_AND_ROBINCALL*)lpData);
		}
		break;
#endif
	default:
		break;
	}
	return 0;
}

