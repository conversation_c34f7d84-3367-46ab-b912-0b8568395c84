#ifndef __DOWNLOAD_CONTROL_H__
#define __DOWNLOAD_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "Utility.h"


#define DOWNLOAD_MSG_PRIVATEKEY		0x0001
#define DOWNLOAD_MSG_RFID			0x0002
#define DOWNLOAD_MSG_ADDRESS		0x0003
#define DOWNLOAD_MSG_FIRMWARE		0x0004
#define DOWNLOAD_MSG_AD				0x0005
#define DOWNLOAD_MSG_CONFIG			0x0006
#define DOWNLOAD_MSG_ADMODULE		0x0007
#define DOWNLOAD_MSG_COMMUNITY_PHONEBOOK		0x0008
#define DOWNLOAD_MSG_AKCS_CONTACT	0x0009
#define DOWNLOAD_MSG_FACEID	0x000A
#define DOWNLOAD_MSG_DOWNLOAD_FACEPIC_XML	0x000B
#define DOWNLOAD_MSG_FP						0x000C
#define DOWNLOAD_MSG_FIRMWARE_MEM			0x0010
#define DOWNLOAD_MSG_FACE_DATA				0x0011
#define DOWNLOAD_MSG_SYNC_FACE_PIC			0x0012
#define DOWNLOAD_MSG_TZ						0x0013
#define DOWNLOAD_MSG_TZDATA					0x0014
#define DOWNLOAD_MSG_SYNC_FACE_PIC_AES		0x0015
#define DOWNLOAD_MSG_AC_INFO			0x0016
#define DOWNLOAD_MSG_AC_META			0x0017
#define DOWNLOAD_MSG_SCHEDULE			0x0018
#define DOWNLOAD_MSG_BACKUP_CONFIG_RECOVERY	0x0019


#define RFO_REQUEST_MASK					0x0100
#define RFO_REQUEST_MSG_PERSONAL_REGISTER	0x0101
#define RFO_REQUEST_MSG_PERSONAL_LOGIN		0x0102
#define RFO_REQUEST_MSG_ADD_SLAVE_ACCOUNT		0x0103
#define RFO_REQUEST_MSG_GET_SLAVE_ACCOUNTLIST	0x0104
#define RFO_REQUEST_MSG_DEL_SLAVE_ACCOUNT		0x0105
#define RFO_REQUEST_MSG_BIND_DEVICE_BY_DEVICE_CODE	0x0106
#define RFO_REQUEST_MSG_EMAIL_EXIT				0x0107
#define RFO_REQUEST_MSG_GET_MOTION_ROBCALL_CFG	0x0108
#define RFO_REQUEST_MSG_SET_PER_ACC_MOTION		0x0109
#define RFO_REQUEST_MSG_SET_PER_ACC_ROBINCALL	0x010A

#define FILE_NAME_SIZE					256
#define DOWNLOAD_FILE_PATH			"/tmp/"
#define DOWNLOAD_FILE_PRIVATEKEY_PRIFIX	"privatekey_"
#define DOWNLOAD_FILE_RFID_PRIFIX	"rfid_"
#define DOWNLOAD_FILE_ADDRESS_PRIFIX	"addr_"
#define DOWNLOAD_FILE_CONFIG_PRIFIX	"config_"
#define DOWNLOAD_FILE_COMMUNITY_PHONEBOOK_PRIFIX	"communityphonebook_"
#define DOWNLOAD_FILE_AKCS_CONTACT_PRIFIX	"contact_"
#define DOWNLOAD_FILE_FACEID_PRIFIX	"faceid_"
#define DOWNLOAD_FILE_FACEDATA_PRIFIX	"facedata_"
#define DOWNLOAD_FILE_DOWNLOAD_FACEPIC_PRIFIX	"D_facepic_"
#define DOWNLOAD_FILE_FP_PRIFIX		"fp_"
#define DOWNLOAD_FILE_TIME_ZONE_PRIFIX	"tTimeZone_"


#define DOWNLOAD_FILE_PRIVATEKEY "/tmp/privatekey_"
#define DOWNLOAD_FILE_RFID		 "/tmp/rfid_"
#define DOWNLOAD_FILE_ADDRESS	"/tmp/addr_"
#define DOWNLOAD_FILE_CONFIG	"/tmp/config_"
#define DOWNLOAD_FILE_TIME_ZONE	"/tmp/tTimeZone_"
#define DOWNLOAD_FILE_BACKUP_CONFIG	"/tmp/backup_config_"


#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	#if (RL_MODELID == RL_MODELID_X916)
	#define DOWNLOAD_FILE_FIRMWARE  "/cache/firmware.zip"	
	#else
	#define DOWNLOAD_FILE_FIRMWARE  "/mnt/internal_sd/firmware.zip"
	#endif
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX)
#define DOWNLOAD_FILE_FIRMWARE  "/tmp/firmware.rom"
#else
	#if (RL_PLATFORMID == RL_PLATFORMID_V3)
		#ifdef RL_GLOBAL_DOWNLOAD_FILE_FIRMWARE	
			#define DOWNLOAD_FILE_FIRMWARE RL_GLOBAL_DOWNLOAD_FILE_FIRMWARE
		#else
			#ifdef RL_DOWNLOAD_FILE_FIRMWARE
			#define DOWNLOAD_FILE_FIRMWARE RL_DOWNLOAD_FILE_FIRMWARE
			#else
			#define DOWNLOAD_FILE_FIRMWARE  "/tmp/upgrader.rom"
			#endif
		#endif
	#else
	#define DOWNLOAD_FILE_FIRMWARE  "/tmp/firmware.zip"
	#endif
#endif

#define DCLIENT_DOWNLOAD_FILE_SIZE		(3 * 1024 * 1024)
#define DCLIENT_DOWNLOAD_FILE_SIZE_LARGE	(100 * 1024 * 1024)

#if RL_MODELID == RL_MODELID_C315
#define DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX	(256*1024*1024)
#elif (RL_MODELID == RL_MODELID_C317) || (RL_MODELID == RL_MODELID_IT82) || (RL_MODELID == RL_MODELID_IT83) || (RL_MODELID == RL_MODELID_R48) || (RL_MODELID ==RL_MODELID_X933) || (RL_MODELID == RL_MODELID_C319) || (RL_MODELID == RL_MODELID_IT88) || (RL_MODELID == RL_MODELID_PS51)
#define DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX	(500*1024*1024)
#else
#define DCLIENT_DOWNLOAD_AD_FILE_SIZE_MAX	(1*1024*1024)
#endif

#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID) || (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID) 
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(1024 * 1024 * 1024)
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_V3)
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(20 * 1024 * 1024)
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX)
	#if FIRMWARE_FILE_MAX_SIZE
		#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	FIRMWARE_FILE_MAX_SIZE
	#else
		#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(20 * 1024 * 1024)
	#endif
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_SV82X)
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE (24 * 1024 * 1024)
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_RV1109)
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE (300 * 1024 * 1024)
#elif (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
	#if (RL_MODELID == RL_MODELID_A05) || (RL_MODELID == RL_MODELID_E16) || (RL_MODELID == RL_MODELID_E16V2) || (RL_MODELID == RL_MODELID_A07) || (RL_MODELID == RL_MODELID_E17) || (RL_MODELID == RL_MODELID_E18)	
		#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(300 * 1024 * 1024)
	#else
		#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(20 * 1024 * 1024)
	#endif
#else
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(20 * 1024 * 1024)
#endif

#if (RL_PLATFORMID == RL_PLATFORMID_DVF99)
#undef DCLIENT_DOWNLOAD_FIRMWARE_SIZE
#define DCLIENT_DOWNLOAD_FIRMWARE_SIZE	(16 * 1024 * 1024 + 2048)
#endif

#define DCLIENT_DOWNLOAD_RETRY_COUNT	3
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID)
#define DOWNLOAD_PATH_ADMODULE	"/sdcard/Pictures/tmp/"
#define DOWNLOAD_FILE_ADMODULE	"/sdcard/Pictures/tmp/ad.tgz"
#else
#define DOWNLOAD_FILE_ADMODULE	"/tmp/ad.tgz"
#endif
#define DOWNLOAD_FILE_COMMUNITY_PHONEBOOK	"/tmp/communityphonebook_"
#define DOWNLOAD_FILE_AKCS_CONTACT "/tmp/contact_"
#define DOWNLOAD_FILE_FACEID	"/tmp/faceid_"
#define DOWNLOAD_FILE_FACE_DATA	"/tmp/facedata_"

#define DOWNLOAD_FILE_DOWNLOAD_FACEPIC	"/tmp/D_facepic_"
#define DOWNLOAD_FILE_FP		"/tmp/fp_"


#define SUFFIX_XML				".xml"
#define SUFFIX_CFG				".cfg"
#define SUFFIX_CSV				".csv"
#define SUFFIX_TGZ				".tgz"

typedef struct DOWNLOAD_INFO_T
{
	TCHAR szUrl[1024];
	TCHAR szLocalFile[URL_SIZE];
	void *obj;
	CURL_DOWNUPLOAD_FILE_CALLBACK callback;
}DOWNLOAD_INFO;

class CDownloadControl
{
public:
    CDownloadControl();
    ~CDownloadControl();
	
	//初始化
	int Init();
	void UnInit();	//反初始化

	int DownLoadThreadInit();

	//处理消息
	int ProcessMsg();

	//增加一个新的消息
	int AddMsg(unsigned int id, unsigned int wParam, unsigned int lParam, void *lpData, int nDataLen);

	//删除所有消息
	int DelAllMsg();

	//消息处理句柄
	int OnMessage(unsigned int msg, unsigned int wParam, unsigned int lParam, void *lpData);
	int OnRfoRequestMessage(UINT msg, UINT wParam, UINT lParam, void *lpData);

    static CDownloadControl *GetInstance();

	bool IsUnInit();

private:
    static CDownloadControl *instance;

	void Lock();
	void Unlock();
	void SetWaitEvent();
	void ResetWaitEvent();
	void WaitForEvent();

	void *m_msgHeader;
	void *m_lock;
	void *m_wait;
	pthread_t m_tidProcess;
	bool m_bUnInit;
};

CDownloadControl *GetDownloadControlInstance();

#endif
