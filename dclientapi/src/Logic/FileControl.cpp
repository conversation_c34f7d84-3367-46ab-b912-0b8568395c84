#include "FileControl.h"
#include "ConnectControl.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "Lock.h"


CFileControl *GetFileControlInstance()
{
	return CFileControl::GetInstance();
}

CFileControl::CFileControl()
{

}

CFileControl::~CFileControl()
{
	
}

CFileControl *CFileControl::instance = NULL;

CFileControl *CFileControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CFileControl();
	}

	return instance;
}
