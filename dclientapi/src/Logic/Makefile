##########################################################################################
## (C)Copyright 2012-2020 Ringslink .Ltd 
##
##########################################################################################
include $(PJ_BUILD_DIR)OBJ_CPP.mak

#SOURCES:=$(wildcard *.cpp)
#OBJS:=$(subst .cpp,.o,$(SOURCES))

#.PHONY: all clean

#all:$(OBJS)
#	cp *.o $(MOD_OBJ_DIR)

#%.o:%.cpp
#	$(CXX) $(CPPFLAGS) -c $<


#clean:
#	-rm *.o

