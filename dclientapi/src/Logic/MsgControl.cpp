#include "DclientIncludes.h"
#include "MsgControl.h"
#include "ConnectControl.h"
#include "SettingControl.h"
#include "DeviceControl.h"
#include "MsgHandle.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "Lock.h"
#include "DClient.h"
#include "DclientDefine.h"

const DEVICE_TYPE_MAP DEVICE_TYPE_FORMAT_MAP[] =
{
	{SDMC_DEVICE_TYPE_STAIR, "STAIR"},          
	{SDMC_DEVICE_TYPE_DOOR, "DOOR"},           
	{SDMC_DEVICE_TYPE_INDOOR, "INDOOR"},            
	{SDMC_DEVICE_TYPE_MANAGEMENT, "MANAGEMENT"},      
	{SDMC_DEVICE_TYPE_WALL, "WALL"},               
	{SDMC_DEVICE_TYPE_SDMC, "SDMC"},
	{SDMC_DEVICE_TYPE_MAX, ""},
};


static int BuildNormalMsgHeader(SOCKET_MSG *pSocketMsg, USHORT nMsgID, UINT nDataSize)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}

	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	pMsgNormal->byMagic[0] = SOCKET_MSG_MAGIC_MSB;
	pMsgNormal->byMagic[1] = SOCKET_MSG_MAGIC_LSB;
	pMsgNormal->nMsgID = nMsgID & SOCKET_MSG_ID_MASK;
	pMsgNormal->nMsgID = nMsgID | SOCKET_MSG_VERSION_02;
	UINT nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	pMsgNormal->nHeadSize = DCLIENT_HTONS(nHeadSize);
	pMsgNormal->nDataSize = DCLIENT_HTONS(nDataSize);

	return 0;
}


CMsgControl *GetMsgControlInstance()
{
	return CMsgControl::GetInstance();
}

CMsgControl::CMsgControl()
{

}

CMsgControl::~CMsgControl()
{
	
}

CMsgControl *CMsgControl::instance = NULL;

CMsgControl *CMsgControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CMsgControl();
	}

	return instance;
}

int CMsgControl::BuildBootupMsg(SOCKET_MSG *pSocketMsg)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_BOOTUP bootupMsg;
	memset(&bootupMsg, 0, sizeof(bootupMsg));
	
	bootupMsg.nPort = GetConnectControlInstance()->GetMulticastListenPort();
	GetConnectControlInstance()->GetLocalIPAddr(bootupMsg.szIPAddr, sizeof(bootupMsg.szIPAddr));
	rl_strcpy_s(bootupMsg.szProtocal, sizeof(bootupMsg.szProtocal), PROTOCAL_NAME_DEFAULT);

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildBootupMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &bootupMsg, &nDataSize) < 0)
	{
		return -1;
	}

	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_BOOTUP, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;

	return 0;
}


int CMsgControl::BuildACKMsg(SOCKET_MSG *pSocketMsg, int msgid, USHORT crc, int result, char *info, int nSequenceNum)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_ACK ackMsg;
	memset(&ackMsg, 0, sizeof(ackMsg));

	rl_strcpy_s(ackMsg.szProtocal, sizeof(ackMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_sprintf_s(ackMsg.szMsgID,sizeof(ackMsg.szMsgID),"%04X",msgid);
	rl_sprintf_s(ackMsg.szMsgCRC,sizeof(ackMsg.szMsgCRC),"%04X",crc);
	rl_strcpy_s(ackMsg.szResult, sizeof(ackMsg.szResult), result==0?"OK":"FAIL");
	rl_strcpy_s(ackMsg.szInfo, sizeof(ackMsg.szInfo), info);
	ackMsg.nSequenceNum = nSequenceNum;

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildACKMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &ackMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	
	if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
	{
		if(BuildNormalMsgHeader(pSocketMsg, MSG_TO_DEVICE_ACK, nDataSize) < 0)
		{
			return -1;
		}
	}
	else
	{
		if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ACK, nDataSize) < 0)
		{
			return -1;
		}
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;

	return 0;
}

int CMsgControl::BuildAKCSACKMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_AKCS_ACK *pAkcsAckMsg)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildAKCSACKMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pAkcsAckMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;	
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_AKCS_ACK, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;

	return 0;
}

int CMsgControl::BuildCheckKeyMsg(SOCKET_MSG *pSocketMsg, int type, char *key)
{
    if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_CHECK_KEY checkKeyMsg;
	memset(&checkKeyMsg, 0, sizeof(checkKeyMsg));

	rl_strcpy_s(checkKeyMsg.szProtocal, sizeof(checkKeyMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	GetSettingHandleInstance()->GetDeviceID(checkKeyMsg.szDeviceID, sizeof(checkKeyMsg.szDeviceID));
	rl_strcpy_s(checkKeyMsg.szKeyType, sizeof(checkKeyMsg.szKeyType), type==0?"PRIVATE":"RF");
	rl_strcpy_s(checkKeyMsg.szKeyValue, sizeof(checkKeyMsg.szKeyValue), key);

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildCheckKeyMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &checkKeyMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_CHECK_KEY, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;

	return 0;
}

int CMsgControl::BuildReportStatusMsg(SOCKET_MSG *pSocketMsg)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_REPORT_STATUS reportStatusMsg;
	memset(&reportStatusMsg, 0, sizeof(reportStatusMsg));

	reportStatusMsg.nDclientVer = DCLIENT_VERSION;
	rl_strcpy_s(reportStatusMsg.szProtocal, sizeof(reportStatusMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(reportStatusMsg.szStatus, sizeof(reportStatusMsg.szStatus), "1");
	

	char szConfigMD5[DCLIENT_MD5_SIZE] = {0};			//配置文件的MD5值
	char szContactMD5[DCLIENT_MD5_SIZE] = {0};			//联系人文件的MD5值
	GetSettingHandleInstance()->GetConfigMD5(szConfigMD5, sizeof(DCLIENT_MD5_SIZE));
	GetSettingHandleInstance()->GetContactMD5(szContactMD5, sizeof(DCLIENT_MD5_SIZE));
	DCLIENT_DEVICE_INFO infoDevice;
	GetDClientInstance()->GetDeviceInfo(&infoDevice);
	rl_strcpy_s(reportStatusMsg.szModelName, sizeof(reportStatusMsg.szModelName), infoDevice.szModelName);
	rl_strcpy_s(reportStatusMsg.szIPAddr, sizeof(reportStatusMsg.szIPAddr), infoDevice.szIPAddr);
	rl_strcpy_s(reportStatusMsg.szSubnetMask, sizeof(reportStatusMsg.szSubnetMask), infoDevice.szMask);
	rl_strcpy_s(reportStatusMsg.szGateway, sizeof(reportStatusMsg.szGateway), infoDevice.szGateWay);
	rl_strcpy_s(reportStatusMsg.szPrimaryDNS, sizeof(reportStatusMsg.szPrimaryDNS), infoDevice.szPrimaryDNS);
	rl_strcpy_s(reportStatusMsg.szSecondaryDNS, sizeof(reportStatusMsg.szSecondaryDNS), infoDevice.szSecondaryDNS);
	rl_strcpy_s(reportStatusMsg.szMAC, sizeof(reportStatusMsg.szMAC), infoDevice.szMAC);
	rl_strcpy_s(reportStatusMsg.szConfigMD5, sizeof(reportStatusMsg.szConfigMD5), szConfigMD5);
	rl_strcpy_s(reportStatusMsg.szContactMD5, sizeof(reportStatusMsg.szContactMD5), szContactMD5);

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportStatusMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &reportStatusMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_STATUS, nDataSize) < 0)
	{
		return -1;
	}

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	pSocketMsg->nSize = nDataSize + nHeadSize;

	return 0;
}

int CMsgControl::BuildReportConfigMsg(SOCKET_MSG *pSocketMsg, CONFIG_MODULE *pConfigModule, BOOL bForceMAC)
{
	if((pSocketMsg == NULL) || (pConfigModule == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	//获取配置值
	if(GetSettingHandleInstance()->GetConfigModule(pConfigModule) < 0)
	{
		return -1;
	}

	SOCKET_MSG_CONFIG reportConfigMsg;
	memset(&reportConfigMsg, 0, sizeof(reportConfigMsg));
	rl_strcpy_s(reportConfigMsg.szProtocal, sizeof(reportConfigMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	memcpy(&reportConfigMsg.module, pConfigModule, sizeof(CONFIG_MODULE));

	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportConfigMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &reportConfigMsg, &nDataSize, bForceMAC) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_CONFIG, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgControl::BuildReportConfigMsg(SOCKET_MSG *pSocketMsg, CONFIG_MODULE_FROM_DEVICE* pConfigModule, BOOL bForceMAC)
{
	if((pSocketMsg == NULL) || (pConfigModule == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	//获取配置值
	if(GetSettingHandleInstance()->GetConfigModule(pConfigModule) < 0)
	{
		return -1;
	}

	SOCKET_MSG_CONFIG_FROM_DEVICE reportConfigMsg;
	memset(&reportConfigMsg, 0, sizeof(reportConfigMsg));
	memcpy(&reportConfigMsg.module, pConfigModule, sizeof(CONFIG_MODULE_FROM_DEVICE));
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportConfigMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &reportConfigMsg, &nDataSize, bForceMAC) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_CONFIG, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

int CMsgControl::BuildAlarmMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALARM_MSG* pszAlarmMsg)
{
	if((pSocketMsg == NULL) || (pszAlarmMsg == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_ALARM alarmMsg;
	memset(&alarmMsg, 0, sizeof(SOCKET_MSG_ALARM));
	rl_strcpy_s(alarmMsg.szProtocal, sizeof(alarmMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(alarmMsg.szType, sizeof(alarmMsg.szType), pszAlarmMsg->type);
	//phone端没有将from或者from_name设置上，在这里由dclient设置
	if(rl_str_isempty(pszAlarmMsg->from_name))
	{
		char szIP[IP_SIZE]={0};	
		GetSettingHandleInstance()->GetLanIPAddr(szIP, sizeof(szIP));
		rl_strcpy_s(alarmMsg.szFromName, sizeof(alarmMsg.szFromName), szIP);
	}
	else
	{		
		rl_strcpy_s(alarmMsg.szFromName, sizeof(alarmMsg.szFromName), pszAlarmMsg->from_name);
	}
	if(rl_str_isempty(pszAlarmMsg->from))
	{		
		char szDeviceExtension[VALUE_SIZE]={0};
		char szDeviceId[VALUE_SIZE]={0};	
		char szDeviceType[INT_SIZE]={0};
		GetSettingHandleInstance()->GetDeviceExtension(szDeviceExtension, sizeof(szDeviceExtension));
		GetSettingHandleInstance()->GetDeviceID(szDeviceId, sizeof(szDeviceId));
		GetSettingHandleInstance()->GetDeviceType(szDeviceType, sizeof(szDeviceType));
		int nDeviceType = atoi(szDeviceType);
		if(nDeviceType < 0 || nDeviceType > SDMC_DEVICE_TYPE_MAX)
		{
			nDeviceType = 0;
		}
		if(!rl_str_isempty(szDeviceExtension))
		{			
			if(nDeviceType < SDMC_DEVICE_TYPE_MAX)
			{				
				rl_sprintf_s(alarmMsg.szFrom, sizeof(alarmMsg.szFromName),"%d_%s-%s", nDeviceType, szDeviceId, szDeviceExtension);
			}
			else
			{				
				rl_sprintf_s(alarmMsg.szFrom, sizeof(alarmMsg.szFromName),"%s-%s", szDeviceId, szDeviceExtension);
			}
		}
		else
		{			
			if(nDeviceType < SDMC_DEVICE_TYPE_MAX)
			{
				rl_sprintf_s(alarmMsg.szFrom, sizeof(alarmMsg.szFrom), "%d_%s", nDeviceType, szDeviceId);
			}
			else
			{
				rl_strcpy_s(alarmMsg.szFrom, sizeof(alarmMsg.szFrom), szDeviceId);
			}
		}
		
	}
	else
	{
		rl_strcpy_s(alarmMsg.szFrom, sizeof(alarmMsg.szFrom), pszAlarmMsg->from);
	}
	rl_strcpy_s(alarmMsg.szToName, sizeof(alarmMsg.szToName), pszAlarmMsg->to_name);	
	rl_strcpy_s(alarmMsg.szTo, sizeof(alarmMsg.szTo), pszAlarmMsg->to);	
	rl_strcpy_s(alarmMsg.szTime, sizeof(alarmMsg.szTo), pszAlarmMsg->time);
	alarmMsg.nSequenceNum = pszAlarmMsg->nSequenceNum;
	alarmMsg.nAlarmCode= pszAlarmMsg->alarm_code;
	alarmMsg.unAlarmLocation = pszAlarmMsg->unAlarmLocation;
	alarmMsg.unAlarmZone = pszAlarmMsg->unAlarmZone;
	alarmMsg.unAlarmCustomize = pszAlarmMsg->unAlarmCustomize;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildAlarmMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &alarmMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ALARM, nDataSize) < 0)
	{
		return -1;
	}
	
	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}


int CMsgControl::BuildAlarmDealMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALARM_DEAL_INFO *pAlarmDeal)
{
	if((pSocketMsg == NULL) || (pAlarmDeal == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_ALARM_DEAL alarmDealMsg;
	memset(&alarmDealMsg, 0, sizeof(SOCKET_MSG_ALARM_DEAL));
	rl_strcpy_s(alarmDealMsg.szProtocal, sizeof(alarmDealMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	alarmDealMsg.id = pAlarmDeal->id;
	rl_strcpy_s(alarmDealMsg.szUser, sizeof(alarmDealMsg.szUser), pAlarmDeal->szUser);	
	rl_strcpy_s(alarmDealMsg.szResult, sizeof(alarmDealMsg.szResult), pAlarmDeal->szResult);
	rl_strcpy_s(alarmDealMsg.szType, sizeof(alarmDealMsg.szType), pAlarmDeal->szType);
	rl_strcpy_s(alarmDealMsg.szTime, sizeof(alarmDealMsg.szTime), pAlarmDeal->szTime);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildAlarmDealMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &alarmDealMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_PUT_ALARM_DEAL, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#if RL_SUPPORT_ARMING_P2P
int CMsgControl::BuildRequestArmingMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_ARMING *pRequestArming, BOOL bForceMAC)
{
	if(pSocketMsg == NULL || pRequestArming == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_REQUEST_ARMING requestArming;
	memset(&requestArming, 0, sizeof(requestArming));	
	rl_strcpy_s(requestArming.szProtocal, sizeof(requestArming.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(requestArming.szAction, sizeof(requestArming.szAction), pRequestArming->action);
	rl_strcpy_s(requestArming.szFrom, sizeof(requestArming.szFrom), pRequestArming->from);
	rl_strcpy_s(requestArming.szTo, sizeof(requestArming.szTo), pRequestArming->to);
	rl_strcpy_s(requestArming.szFromIP, sizeof(requestArming.szFromIP), pRequestArming->from_ip);
	rl_strcpy_s(requestArming.szToIP, sizeof(requestArming.szToIP), pRequestArming->to_ip);
	requestArming.nMode = pRequestArming->mode;
	requestArming.nSequenceNum= pRequestArming->nSequenceNum;

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestArming((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &requestArming, &nDataSize, bForceMAC) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_TO_DEVICE_REQUEST_ARMING, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

int CMsgControl::BuildTextMsg(SOCKET_MSG *pSocketMsg, DCLIENT_TEXT_MSG *pTextMsg)
{
	if((pSocketMsg == NULL) || (pTextMsg == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	SOCKET_MSG_TEXT_MESSAGE textMsg;
	memset(&textMsg, 0, sizeof(textMsg));	
	rl_strcpy_s(textMsg.szProtocal, sizeof(textMsg.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(textMsg.szContent, sizeof(textMsg.szContent), pTextMsg->content);
	rl_strcpy_s(textMsg.szTitle, sizeof(textMsg.szTitle), pTextMsg->title);	
	textMsg.nSequenceNum = pTextMsg->nSequenceNum;
	//phone端没有将from或者from_name设置上，在这里由dclient设置
	if(rl_str_isempty(pTextMsg->from_name))
	{
		char szIP[IP_SIZE]={0};	
		GetSettingHandleInstance()->GetLanIPAddr(szIP, sizeof(szIP));
		rl_strcpy_s(textMsg.szFromName, sizeof(textMsg.szFromName), szIP);
	}
	else
	{		
		rl_strcpy_s(textMsg.szFromName, sizeof(textMsg.szFromName), pTextMsg->from_name);
	}
	if(rl_str_isempty(pTextMsg->from))
	{		
		char szDeviceExtension[VALUE_SIZE]={0};
		char szDeviceId[VALUE_SIZE]={0};	
		char szDeviceType[INT_SIZE]={0};
		GetSettingHandleInstance()->GetDeviceExtension(szDeviceExtension, sizeof(szDeviceExtension));
		GetSettingHandleInstance()->GetDeviceID(szDeviceId, sizeof(szDeviceId));
		GetSettingHandleInstance()->GetDeviceType(szDeviceType, sizeof(szDeviceType));
		int nDeviceType = atoi(szDeviceType);
		if(nDeviceType < 0 || nDeviceType > SDMC_DEVICE_TYPE_MAX)
		{
			nDeviceType = 0;
		}
		if(!rl_str_isempty(szDeviceExtension))
		{			
			if(nDeviceType < SDMC_DEVICE_TYPE_MAX)
			{				
				rl_sprintf_s(textMsg.szFrom, sizeof(textMsg.szFrom),"%d_%s-%s", nDeviceType, szDeviceId, szDeviceExtension);
			}
			else
			{				
				rl_sprintf_s(textMsg.szFrom, sizeof(textMsg.szFrom),"%s-%s", szDeviceId, szDeviceExtension);
			}
		}
		else
		{			
			if(nDeviceType < SDMC_DEVICE_TYPE_MAX)
			{
				rl_sprintf_s(textMsg.szFrom, sizeof(textMsg.szFrom), "%d_%s", nDeviceType, szDeviceId);
			}
			else
			{
				rl_strcpy_s(textMsg.szFrom, sizeof(textMsg.szFrom), szDeviceId);
			}
		}
		
	}
	else
	{
		rl_strcpy_s(textMsg.szFrom, sizeof(textMsg.szFrom), pTextMsg->from);
	}
	rl_strcpy_s(textMsg.szTo, sizeof(textMsg.szTo), pTextMsg->to);
	rl_strcpy_s(textMsg.szToName, sizeof(textMsg.szToName), pTextMsg->to_name);
	rl_strcpy_s(textMsg.szTime, sizeof(textMsg.szTo), pTextMsg->time);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildTextMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &textMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(GetConnectControlInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
	{
		if(BuildNormalMsgHeader(pSocketMsg, MSG_TO_DEVICE_SEND_TEXT_MESSAGE, nDataSize) < 0)
		{
			return -1;
		}
	}
	else
	{
		if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_TEXT_MSG, nDataSize) < 0)
		{
			return -1;
		}
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildAccessInfo(SOCKET_MSG *pSocketMsg, DCLIENT_ACCESS_INFO *pAccessInfo)
{
	if((pSocketMsg == NULL) || (pAccessInfo == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_ACCESS_INFO tAccessInfo;
	memset(&tAccessInfo, 0, sizeof(tAccessInfo));	
	rl_strcpy_s(tAccessInfo.szProtocal, sizeof(tAccessInfo.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(tAccessInfo.szCode, sizeof(tAccessInfo.szCode), pAccessInfo->szCode);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildAccessInfo((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &tAccessInfo, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ACCESS_INFO, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildHeartBeatMsg(SOCKET_MSG *pSocketMsg)
{
	if((pSocketMsg == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	
	int nDataSize = 0;
	

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_HEART_BEAT, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildHeartBeatAckMsg(SOCKET_MSG *pSocketMsg)
{
	if((pSocketMsg == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	
	int nDataSize = 0;
	

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ACK_HEARTBEAT, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}


#if RL_GLOBAL_SUPPORT_TMP_KEY
int CMsgControl::BuildCheckTmpKeyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_CHECK_TMP_KEY *pCheckTmpKey)
{
	if((pSocketMsg == NULL) || (pCheckTmpKey == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_CHECK_TMP_KEY tCheckTmpKey;
	memset(&tCheckTmpKey, 0, sizeof(tCheckTmpKey));	
	rl_strcpy_s(tCheckTmpKey.szProtocal, sizeof(tCheckTmpKey.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(tCheckTmpKey.szCheckTmpKeyCode, sizeof(tCheckTmpKey.szCheckTmpKeyCode), pCheckTmpKey->szCheckTmpKeyCode);
	rl_strcpy_s(tCheckTmpKey.szMsgSeqCode, sizeof(tCheckTmpKey.szMsgSeqCode), pCheckTmpKey->szMsgSeqCode);

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildCheckTmpKey((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &tCheckTmpKey, &nDataSize) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_CHECK_TMP_KEY, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

int CMsgControl::BuildGetBindCodeRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	CHAR szSerialNum[BINDCODE_SEQUENCE_NUM_SIZE+1]={0};
	rl_sprintf_s(szSerialNum, sizeof(szSerialNum), "%.10d", nSerialNum);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildGetBindCodeRequestMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), szSerialNum, &nDataSize) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_CREATE_BIND_CODE, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildUnBindCodeRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum, char *pszBindCode)
{
	if(pSocketMsg == NULL || pszBindCode == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	CHAR szSerialNum[BINDCODE_SEQUENCE_NUM_SIZE+1]={0};
	rl_sprintf_s(szSerialNum, sizeof(szSerialNum), "%.10d", nSerialNum);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildUnBindCodeRequestMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), szSerialNum, pszBindCode,&nDataSize) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_DELETE_BIND_CODE, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildGetBindCodeListRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	CHAR szSerialNum[BINDCODE_SEQUENCE_NUM_SIZE+1]={0};
	rl_sprintf_s(szSerialNum, sizeof(szSerialNum), "%.10d", nSerialNum);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildGetBindCodeListRequestMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), szSerialNum, &nDataSize) < 0)
	{
		return -1;
	}
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_GET_BIND_CODE_LIST, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildDiscoverMsg(SOCKET_MSG *pSocketMsg, DCLIENT_DISCOVER_SEND *pDiscoverMsg)
{
	if(pSocketMsg == NULL || pDiscoverMsg == NULL)
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_DISCOVER_SEND discoverMsg;
	memset(&discoverMsg, 0, sizeof(discoverMsg));	
	discoverMsg.nSequenceNum = pDiscoverMsg->nSequenceNum;
	rl_strcpy_s(discoverMsg.szProtocal, sizeof(discoverMsg.szProtocal), PROTOCAL_NAME_DEFAULT);		
	rl_strcpy_s(discoverMsg.szDeviceID, sizeof(discoverMsg.szDeviceID), pDiscoverMsg->szDeviceID);	
	rl_sprintf_s(discoverMsg.szType , sizeof(discoverMsg.szType), "%d", pDiscoverMsg->nType);	
	rl_sprintf_s(discoverMsg.szExtension, sizeof(discoverMsg.szExtension), "%d", pDiscoverMsg->nExtension);
	rl_sprintf_s(discoverMsg.szFlag, sizeof(discoverMsg.szFlag), "%d", pDiscoverMsg->nFlag);
	rl_sprintf_s(discoverMsg.szDiscoverMethod, sizeof(discoverMsg.szDiscoverMethod), "%d", pDiscoverMsg->nDiscoverMethod);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildDiscoverMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &discoverMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_SEND_DISCOVER, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildDiscoverAckMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg)
{
	if(pSocketMsg == NULL || pDiscoverAckMsg == NULL)
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	rl_strcpy_s(pDiscoverAckMsg->szProtocal, sizeof(pDiscoverAckMsg->szProtocal), PROTOCAL_NAME_DEFAULT);		
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildDiscoverAckMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pDiscoverAckMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ACK_DISCOVER, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#if RL_SUPPORT_CLOUD_DEV_INFO	
int CMsgControl::BuildRequestDeviceListMsg(SOCKET_MSG *pSocketMsg)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_REQUEST_DEVICE_LIST requestDeviceList;
	memset(&requestDeviceList, 0, sizeof(SOCKET_MSG_REQUEST_DEVICE_LIST));
	rl_strcpy_s(requestDeviceList.szProtocal, sizeof(requestDeviceList.szProtocal), PROTOCAL_NAME_DEFAULT);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestDeviceListMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &requestDeviceList, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_DEVICE_LIST, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif
#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
int CMsgControl::BuildReportArmingMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_ARMING *pReportArming, BOOL bForceMAC)
{
	if((pSocketMsg == NULL) || (pReportArming == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_REPORT_ARMING reportArmingMsg;
	memset(&reportArmingMsg, 0, sizeof(SOCKET_MSG_REPORT_ARMING));	
	rl_strcpy_s(reportArmingMsg.szProtocal, sizeof(reportArmingMsg.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(reportArmingMsg.szFrom, sizeof(reportArmingMsg.szFrom), pReportArming->from);
	rl_strcpy_s(reportArmingMsg.szTo, sizeof(reportArmingMsg.szTo), pReportArming->to);
	rl_strcpy_s(reportArmingMsg.szFromIP, sizeof(reportArmingMsg.szFromIP), pReportArming->from_ip);
	rl_strcpy_s(reportArmingMsg.szToIP, sizeof(reportArmingMsg.szToIP), pReportArming->to_ip);
	reportArmingMsg.nMode = pReportArming->mode;
	reportArmingMsg.nSync = pReportArming->nSync;
	reportArmingMsg.nActionType = pReportArming->nActionType;
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportArmingMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &reportArmingMsg, &nDataSize, bForceMAC) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_ARMING, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
int CMsgControl::BuildPushForwardNumberMsg(SOCKET_MSG *pSocketMsg, DCLIENT_PUSH_NOANSWER_FWD_NUMBER *pPushForwardNumber)
{
	if((pSocketMsg == NULL) || (pPushForwardNumber == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_PUSH_FORWARD_NUMBER pushFwdNumberMsg;
	memset(&pushFwdNumberMsg, 0, sizeof(SOCKET_MSG_PUSH_FORWARD_NUMBER));	
	rl_strcpy_s(pushFwdNumberMsg.szProtocal, sizeof(pushFwdNumberMsg.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(pushFwdNumberMsg.szFrom, sizeof(pushFwdNumberMsg.szFrom), pPushForwardNumber->from);
	rl_strcpy_s(pushFwdNumberMsg.szTo, sizeof(pushFwdNumberMsg.szTo), pPushForwardNumber->to);
	rl_strcpy_s(pushFwdNumberMsg.szGroup0, sizeof(pushFwdNumberMsg.szGroup0), pPushForwardNumber->group0);
	rl_strcpy_s(pushFwdNumberMsg.szGroup1, sizeof(pushFwdNumberMsg.szGroup1), pPushForwardNumber->group1);
	rl_strcpy_s(pushFwdNumberMsg.szGroup2, sizeof(pushFwdNumberMsg.szGroup2), pPushForwardNumber->group2);
	rl_strcpy_s(pushFwdNumberMsg.szAction, sizeof(pushFwdNumberMsg.szAction), pPushForwardNumber->action);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildPushForwardNumberMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &pushFwdNumberMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_TO_DEVICE_PUSH_NOANSWER_FWD_NUMBER, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
int CMsgControl::BuildReportForwardNumberMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pReportForwardNumber)
{
	if((pSocketMsg == NULL) || (pReportForwardNumber == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_REPORT_FORWARD_NUMBER reportFwdNumberMsg;
	memset(&reportFwdNumberMsg, 0, sizeof(SOCKET_MSG_REPORT_FORWARD_NUMBER));	
	rl_strcpy_s(reportFwdNumberMsg.szProtocal, sizeof(reportFwdNumberMsg.szProtocal), PROTOCAL_NAME_DEFAULT);	
	rl_strcpy_s(reportFwdNumberMsg.szFrom, sizeof(reportFwdNumberMsg.szFrom), pReportForwardNumber->from);
	rl_strcpy_s(reportFwdNumberMsg.szTo, sizeof(reportFwdNumberMsg.szTo), pReportForwardNumber->to);
	rl_strcpy_s(reportFwdNumberMsg.szGroup0, sizeof(reportFwdNumberMsg.szGroup0), pReportForwardNumber->group0);
	rl_strcpy_s(reportFwdNumberMsg.szGroup1, sizeof(reportFwdNumberMsg.szGroup1), pReportForwardNumber->group1);
	rl_strcpy_s(reportFwdNumberMsg.szGroup2, sizeof(reportFwdNumberMsg.szGroup2), pReportForwardNumber->group2);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportForwardNumberMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &reportFwdNumberMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_TO_DEVICE_REPORT_NOANSWER_FWD_NUMBER, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

#endif
#if RL_SUPPORT_REPORT_ACTIVITY
int CMsgControl::BuildMotionAlertMsg(SOCKET_MSG *pSocketMsg, DCLIENT_MOTION_ALERT *pMotionAlert)
{
	if((pSocketMsg == NULL) || (pMotionAlert == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_MOTION_ALERT motionAlertMsg;
	memset(&motionAlertMsg, 0, sizeof(SOCKET_MSG_MOTION_ALERT));	
	rl_strcpy_s(motionAlertMsg.szProtocal, sizeof(motionAlertMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(motionAlertMsg.szPicName, sizeof(motionAlertMsg.szPicName), pMotionAlert->szPicName);
	
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildMotionAlertMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &motionAlertMsg, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_MOTION_ALERT, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildReportActivityMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_ACTIVITY *pReportActivity)
{
	if((pSocketMsg == NULL) || (pReportActivity == NULL))
	{
		return -1;
	}
	
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportActivityMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportActivity, &nDataSize) < 0)
	{
		return -1;
	}
	
	//rl_log_debug("pMsgNormal->byData = (%d)[%d]%s",  sizeof(pMsgNormal->byData), rl_strlen((char *)pMsgNormal->byData), (char *)pMsgNormal->byData);

	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_ACTIVITY, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

int CMsgControl::BuildCheckDtmfMsg(SOCKET_MSG *pSocketMsg, DCLIENT_CHECK_DTMF *pCheckDtmfMsg)
{
	if((pSocketMsg == NULL) || (pCheckDtmfMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_CHECK_DTMF checkDtmfMsg;
	memset(&checkDtmfMsg, 0, sizeof(SOCKET_MSG_CHECK_DTMF));

	checkDtmfMsg.nSequenceNum = pCheckDtmfMsg->nSequenceNum;
	rl_strcpy_s(checkDtmfMsg.szProtocal, sizeof(checkDtmfMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(checkDtmfMsg.szRemoteSip, sizeof(checkDtmfMsg.szRemoteSip), pCheckDtmfMsg->szRemoteSip);

	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildCheckDtmfMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &checkDtmfMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_CHECK_DTMF, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}

int CMsgControl::BuildReportDeviceCodeMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCodeMsg)
{
	if((pSocketMsg == NULL) || (pReportDeviceCodeMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	rl_memcpy(pMsgNormal->byData, pReportDeviceCodeMsg, sizeof(DCLIENT_REPORT_DEVICE_CODE));
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	int nDataSize = sizeof(DCLIENT_REPORT_DEVICE_CODE);
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_DEVICE_CODE, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportNetworkInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_NETWORK_INFO *pReportNetworkInfoMsg)
{
	if((pSocketMsg == NULL) || (pReportNetworkInfoMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	rl_memcpy(pMsgNormal->byData, pReportNetworkInfoMsg, sizeof(DCLIENT_REPORT_NETWORK_INFO));
	
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	int nDataSize = sizeof(DCLIENT_REPORT_NETWORK_INFO);
	if(BuildNormalMsgHeader(pSocketMsg, MSG_DEVICE_TO_DEVICE_REPORT_NETWORK_INFO, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

#if RL_SUPPORT_DTMF_SET
int CMsgControl::BuildSendDTMFSetMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SET_DTMF *pDtmfSetMsg)
{
	if((pSocketMsg == NULL) || (pDtmfSetMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	SOCKET_MSG_SEND_DTMF_SET DtmfSetMsg;
	memset(&DtmfSetMsg, 0, sizeof(SOCKET_MSG_SEND_DTMF_SET));

	DtmfSetMsg.nSequenceNum = pDtmfSetMsg->nSequenceNum;
	rl_strcpy_s(DtmfSetMsg.szProtocal, sizeof(DtmfSetMsg.szProtocal), PROTOCAL_NAME_DEFAULT);
	rl_strcpy_s(DtmfSetMsg.szFrom, sizeof(DtmfSetMsg.szFrom), pDtmfSetMsg->szFrom);
	rl_strcpy_s(DtmfSetMsg.szTo, sizeof(DtmfSetMsg.szTo), pDtmfSetMsg->szTo);
	rl_strcpy_s(DtmfSetMsg.szDTMF, sizeof(DtmfSetMsg.szDTMF), pDtmfSetMsg->szDTMF);
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildSendDTMFSetMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &DtmfSetMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_COMMON_FORWARD, nDataSize) < 0)
	{
		return -1;
	}

	pSocketMsg->nSize = nDataSize + nHeadSize;
	
	return 0;
}
#endif

#if RL_SUPPORT_DEVICE_MAINTENANCE
int CMsgControl::BuildCliCommandRespMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_CLI_COMMAND_RESP *pSocketCliCommandRespMsg)
{
	if((pSocketMsg == NULL) || (pSocketCliCommandRespMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildCliCommandRespMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pSocketCliCommandRespMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_CLI_COMMAND_RESP, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
	
}
#endif

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgControl::BuildRequestCfgFromDeviceMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_CONFIG *requestCfgMsg)
{
	if((pSocketMsg == NULL) || (requestCfgMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestCfgFromDeviceMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), requestCfgMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;

	if(BuildNormalMsgHeader(pSocketMsg, MSG_DEVICE_TO_DEVICE_REQUEST_CONFIG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}
#endif

int CMsgControl::BuildReportCallCaptureMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture)
{
	if((pSocketMsg == NULL) || (pReportCallCapture == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportCallCaptureMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportCallCapture, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_CALL_CAPTURE, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_TRIGGER *pReportTrigger)
{
	if((pSocketMsg == NULL) || (pReportTrigger == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportTriggerMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportTrigger, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_TRIGGER, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildManageBroadcastMsg(SOCKET_MSG *pSocketMsg, DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg)
{
	if((pSocketMsg == NULL) || (pManageBroadcastMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildManageBroadcastMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pManageBroadcastMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}	

int CMsgControl::BuildReportHealthMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_HEALTH *pReportHealth)
{
	if((pSocketMsg == NULL) || (pReportHealth == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportHealthMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportHealth, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_HEALTH, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildResponseSensorTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg)
{
	if((pSocketMsg == NULL) || (pSensorTriggerMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildResponseSensorTriggerMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pSensorTriggerMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildUploadVideoNotifyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg)
{
	if((pSocketMsg == NULL) || (pUploadVideoNotifyMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildUploadVideoNotifyMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pUploadVideoNotifyMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_UPLOAD_VIDEO_NOTIFY, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildUploadCaptureNotifyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg)
{
	if((pSocketMsg == NULL) || (pUploadCaptureNotifyMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildUploadCaptureNotifyMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pUploadCaptureNotifyMsg, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_UPLOAD_CAPTURE_NOTIFY, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}


int CMsgControl::BuildRequestAllTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger)
{
	if((pSocketMsg == NULL) || (pRequestAllTrigger == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestAllTriggerMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pRequestAllTrigger, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_DEVICE_TO_DEVICE_REQUEST_ALL_TRIGGER, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;

}

int CMsgControl::BuildReportAllTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger)
{
	if((pSocketMsg == NULL) || (pRequestAllTrigger == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportAllTriggerMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pRequestAllTrigger, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_DEVICE_TO_DEVICE_REPORT_ALL_TRIGGER, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;

}

#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
int CMsgControl::BuildReportDoorStatusMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_DOORSTATUS *pDoorStatus)
{
	if((pSocketMsg == NULL) || (pDoorStatus == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportDoorStatusMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pDoorStatus, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_DOOR_STATUS, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}
#endif

#if RL_SUPPORT_SEND_REPORT_GAS
int CMsgControl::BuildReportGasMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_GAS *pReportGas)
{
	if((pSocketMsg == NULL) || (pReportGas == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportGasMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportGas, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_GAS, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}
#endif

int CMsgControl::BuildReportVisitorInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo)
{
	if((pSocketMsg == NULL) || (pReportVisitorInfo == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportVisitorInfoMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportVisitorInfo, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_VISITOR_INFO, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportVisitorAuthInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo)
{
	if((pSocketMsg == NULL) || (pReportVisitorAuthInfo == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportVisitorAuthInfoMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pReportVisitorAuthInfo, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestOssStsMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_OSS_STS *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestOssStsMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_OSS_STS, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestOpenDoor(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_OPENDOOR *pData, int nType)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestOpenDoor((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize, nType) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_OPENDOOR, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestACInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_ACINFO *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestACInfoMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_ACINFO, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildSendDeliveryMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SEND_DELIVERY *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildSendDeliveryMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_SEND_DELIVERY_MSG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildSyncActivityMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SYNC_ACTIVITY *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildSyncActivityMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_SYNC_ACTIVITY, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportRelayStatusMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_RELAY_STATUS *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportRelayStatusMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_RELAY_STATUS, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildFlowOutOfLimitMsg(SOCKET_MSG *pSocketMsg, DCLIENT_FLOW_OUT_OF_LIMIT *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildFlowOutOfLimitMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportCallLogMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_CALLLOG *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportCallLogMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_CALLLOG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildBackupConfigACKMsg(SOCKET_MSG *pSocketMsg, DCLIENT_BACKUP_CONFIG_ACK *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildBackupConfigACKMsg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_BACKUP_CONFIG_ACK, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestRtspMonitor(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_RTSP_MONITOR *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestRtspMonitor((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_RTSP_MONITOR, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRtspMonitorStop(SOCKET_MSG *pSocketMsg, DCLIENT_RTSP_MONITOR_STOP *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRtspMonitorStop((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_RTSP_MONITOR_STOP, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestEndUserReg(SOCKET_MSG *pSocketMsg)
{
	if((pSocketMsg == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestEndUserReg((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_END_USER_REG, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildAddKitDevice(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildAddKitDevice((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_ADD_KIT_DEVICES, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildReportKitDevice(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildReportKitDevice((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REPORT_KIT_DEVICES, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildRequestKitDevice(SOCKET_MSG *pSocketMsg)
{
	if(pSocketMsg == NULL)
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildRequestKitDevice((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_REQUEST_KIT_DEVICES, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::BuildModifyDeviceLocation(SOCKET_MSG *pSocketMsg, KIT_DEVICE_BASE_INFO *pData)
{
	if((pSocketMsg == NULL) || (pData == NULL))
	{
		return -1;
	}
	memset(pSocketMsg, 0, sizeof(SOCKET_MSG));
	SOCKET_MSG_NORMAL *pMsgNormal = (SOCKET_MSG_NORMAL *)pSocketMsg->byData;
	int nDataSize = 0;
	if(GetMsgHandleInstance()->BuildModifyDeviceLocation((char *)pMsgNormal->byData, sizeof(pMsgNormal->byData), pData, &nDataSize) < 0)
	{
		return -1;
	}
	int nHeadSize = SOCKET_MSG_NORMAL_HEADER_SIZE;
	if(BuildNormalMsgHeader(pSocketMsg, MSG_FROM_DEVICE_MODIFY_LOCATION, nDataSize) < 0)
	{
		return -1;
	}
	pSocketMsg->nSize = nDataSize + nHeadSize;
	return 0;
}

int CMsgControl::ParseDiscoverMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pDiscoverMsg == NULL))
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDiscoverMsg(pszPayload, pDiscoverMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseDiscoverAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pDiscoverAckMsg == NULL))
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDiscoverAckMsg(pszPayload, pDiscoverAckMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}


int CMsgControl::ParseReqConnMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQ_CONN *pReqConnMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pReqConnMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseReqConnMsg(pszPayload, pReqConnMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseReqStatusMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQ_STATUS *pReqStatusMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pReqStatusMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseReqStatusMsg(pszPayload, pReqStatusMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseRemoteControlMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pRemoteControlMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseRemoteControlMsg(pszPayload, pRemoteControlMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseReqConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG *pReqConfigMsg, INT nDataSize, BOOL bForceMAC)
{
	if((pNormalMsg == NULL) || (pReqConfigMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseReqConfigMsg(pszPayload, pReqConfigMsg, nDataSize, bForceMAC) < 0)
	{
		return -1;
	}

	return 0;
}

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgControl::ParseReqConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG_FROM_DEVICE *pReqConfigMsg, INT nDataSize, BOOL bForceMAC)
{
	if((pNormalMsg == NULL) || (pReqConfigMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseReqConfigMsg(pszPayload, pReqConfigMsg, nDataSize, bForceMAC) < 0)
	{
		return -1;
	}

	return 0;
}
#endif

int CMsgControl::ParseUpdateConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG *pUpdateConfigMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pUpdateConfigMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseUpdateConfigMsg(pszPayload, pUpdateConfigMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::RecvUpdateConfigMsg(CONFIG_MODULE *pConfigModule)
{
	if(pConfigModule == NULL)
	{
		return -1;
	}

	//更新配置
	if(GetSettingControlInstance()->SetConfigModule(pConfigModule) < 0)
	{
		rl_log_err("%s: SetConfigModule failed.", __FUNCTION__);
		return -1;
	}

	return 0;
}


int CMsgControl::ParseUpgradeStartMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_UPGRADE_START *pUpgradeStartMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pUpgradeStartMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseUpgradeStartMsg(pszPayload, pUpgradeStartMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseFileEndMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_FILE_END *pFileEndMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pFileEndMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseFileEndMsg(pszPayload, pFileEndMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseACKMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ACK*pACKMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pACKMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseACKMsg(pszPayload, pACKMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseOwnerMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_OWNER_MESSAGE*pOwnerMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pOwnerMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseOwnerMsg(pszPayload, pOwnerMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseTextMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_TEXT_MESSAGE*pTextMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pTextMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseTextMsg(pszPayload, pTextMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}


int CMsgControl::ParseKeySendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_KEY_SEND* pKeySendMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || (pKeySendMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseKeySendMsg(pszPayload, pKeySendMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}


int CMsgControl::ParseUpgradeSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_UPGRADE_SEND* pUpgradeSendMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pUpgradeSendMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseUpgradeSendMsg(pszPayload, pUpgradeSendMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseAdSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_AD_SEND* pAdSendMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pAdSendMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseAdSendMsg(pszPayload, pAdSendMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseAlarmSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM_SEND* pAlarmSendMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pAlarmSendMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseAlarmSendMsg(pszPayload, pAlarmSendMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseAlarmMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM* pAlarmMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pAlarmMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseAlarmMsg(pszPayload, pAlarmMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}


int CMsgControl::ParseAlarmAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM* pAlarmMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pAlarmMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseAlarmAckMsg(pszPayload, pAlarmMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}



int CMsgControl::ParseAlarmDealMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM_DEAL* pAlarmDealMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pAlarmDealMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseAlarmDealMsg(pszPayload, pAlarmDealMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseHeartBeatPeriodMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_HEARTBEAT_PERIOD* pHeartBeatPeriodMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pHeartBeatPeriodMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseHeartBeatPeriodMsg(pszPayload, pHeartBeatPeriodMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}
#if RL_SUPPORT_ARMING
int CMsgControl::ParseRequestArmingMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQUEST_ARMING *pRequestArmingMsg, INT nDataSize, BOOL bForceMAC)
{
	if((pNormalMsg == NULL) || (pRequestArmingMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseRequestArmingMsg(pszPayload, pRequestArmingMsg, nDataSize, bForceMAC) < 0)
	{
		return -1;
	}

	return 0;
}
#endif

#if RL_SUPPORT_ARMING_P2P
int CMsgControl::ParseReportArmingMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_ARMING *pReportArming, int nDataSize, BOOL bForceMAC)
{
	if((pNormalMsg == NULL) || pReportArming == NULL)
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReportArmingMsg(pszPayload, pReportArming, nDataSize, bForceMAC) < 0)
	{
		return -1;
	}

	return 0;
}

#endif

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
int CMsgControl::ParseReportForwardNumberMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumberMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pReportForwardNumberMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseReportForwardNumberMsg(pszPayload, pReportForwardNumberMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
int CMsgControl::ParsePushForwardNumberMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumber, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pPushForwardNumber == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParsePushForwardNumberMsg(pszPayload, pPushForwardNumber, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

#endif

#if RL_GLOBAL_SUPPORT_TMP_KEY
int CMsgControl::ParseCheckTmpKeyMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CHECK_TMP_KEY* pCheckTmpKeyMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pCheckTmpKeyMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseCheckTmpKeyMsg(pszPayload, pCheckTmpKeyMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}
#endif
#if RL_GLOBAL_SUPPORT_VRTSP	
int CMsgControl::ParseStartRtspMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_START_RTSP* pStartRtspMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pStartRtspMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseStartRtspMsg(pszPayload, pStartRtspMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseStopRtspMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_STOP_RTSP* pStopRtspMsg, INT nDataSize)
{
	if((pNormalMsg == NULL) || (pStopRtspMsg == NULL))
	{
		return -1;
	}
	
	char *pszPayload = (char *)pNormalMsg->byData;

	if(GetMsgHandleInstance()->ParseStopRtspMsg(pszPayload, pStopRtspMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}
#endif
int CMsgControl::ParseGetBindCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeCreate, int nDataSize)
{
	if((pNormalMsg == NULL) || pBindCodeCreate == NULL)
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseGetBindCodeMsg(pszPayload, pBindCodeCreate, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseDeleteBindCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeDelete, int nDataSize)
{
	if((pNormalMsg == NULL) || pBindCodeDelete == NULL)
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDeleteBindCodeMsg(pszPayload, pBindCodeDelete, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseGetBindCodeListMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_LIST *pBindCodeList, int nDataSize)
{
	if((pNormalMsg == NULL) || pBindCodeList == NULL)
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseGetBindCodeListMsg(pszPayload, pBindCodeList, nDataSize) < 0)
	{
		return -1;
	}

	return 0;

}

int CMsgControl::ParseNotifyBindCodeChangeMsg(SOCKET_MSG_NORMAL *pNormalMsg, INT *pSequenceNum, int nDataSize)
{
	if((pNormalMsg == NULL) || pSequenceNum == NULL)
	{
		return -1;
	}
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseNotifyBindCodeChangeMsg(pszPayload, pSequenceNum, nDataSize) < 0)
	{
		return -1;
	}

	return 0;

}

int CMsgControl::ParseContactUrlMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONTACT_URL *pContactUrlMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pContactUrlMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseContactUrlMsg(pszPayload, pContactUrlMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseReconnectRPSMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_RPS *pReconnectRPSMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pReconnectRPSMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReconnectRPSMsg(pszPayload, pReconnectRPSMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseReconnectGateWayMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_GATEWAY *pReconnectGateWayMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pReconnectGateWayMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReconnectGateWayMsg(pszPayload, pReconnectGateWayMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseReconnectAccessMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_ACCESS_SERVER *pReconnectAccessMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pReconnectAccessMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReconnectAccessMsg(pszPayload, pReconnectAccessMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseCheckDtmfAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CHECK_DTMF_ACK* pCheckDtmfAckMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pCheckDtmfAckMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseCheckDtmfAckMsg(pszPayload, pCheckDtmfAckMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseDeviceCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DEVICE_CODE* pDeviceCodeMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pDeviceCodeMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDeviceCodeMsg(pszPayload, pDeviceCodeMsg, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

#if RL_SUPPORT_DEVICE_MAINTENANCE	
int CMsgControl::ParseMaintenanceGetLogMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_GETLOG* pMaintenanceGetLog, int nDataSize)
{
	if((pNormalMsg == NULL) || pMaintenanceGetLog == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseMaintenanceGetLogMsg(pszPayload, pMaintenanceGetLog, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseMaintenanceStartPcapMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_START_PCAP* pMaintenanceStartPcap, int nDataSize)
{
	if((pNormalMsg == NULL) || pMaintenanceStartPcap == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseMaintenanceStartPcapMsg(pszPayload, pMaintenanceStartPcap, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseMaintenanceStopPcapMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_STOP_PCAP* pMaintenanceStopPcap, int nDataSize)
{
	if((pNormalMsg == NULL) || pMaintenanceStopPcap == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseMaintenanceStopPcapMsg(pszPayload, pMaintenanceStopPcap, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseMaintenanceGetDevConfig(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG* pMaintenanceGetDevConfig, int nDataSize)
{
	if((pNormalMsg == NULL) || pMaintenanceGetDevConfig == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseMaintenanceGetDevConfig(pszPayload, pMaintenanceGetDevConfig, nDataSize) < 0)
	{
		return -1;
	}

	return 0;
}

int CMsgControl::ParseCliCommandMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CLI_COMMAND *pCliCommandMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pCliCommandMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseCliCommandMsg(pszPayload, pCliCommandMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

#endif
#if RL_SUPPORT_RECV_MOTION_ALERT
int CMsgControl::ParseDoorMotionAlertMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DOOR_MOTION_ALERT* pDoorMotionAlert, int nDataSize)
{
	if((pNormalMsg == NULL) || pDoorMotionAlert == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDoorMotionAlertMsg(pszPayload, pDoorMotionAlert, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}
#endif
int CMsgControl::ParseCommonTransferMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_COMMON_TRANSFER* pCommonTransferMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pCommonTransferMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseCommonTransferMsg(pszPayload, pCommonTransferMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
int CMsgControl::ParseDownUploadFacePicMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DOWNUPLOAD_FACE_PIC *pDownUploadFacePicMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pDownUploadFacePicMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDownUploadFacePicMsg(pszPayload, pDownUploadFacePicMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}
#endif

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgControl::ParseReqCfgFromDeviceMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pRequestConfigMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReqCfgFromDeviceMsg(pszPayload, pRequestConfigMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}
#endif

int CMsgControl::ParseManageAlarmMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pManageAlarmMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseManageAlarmMsg(pszPayload, pManageAlarmMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseMaintenaceServerChange(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange, int nDataSize)
{
	if((pNormalMsg == NULL) || pMaintenanceSrvChange == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseMaintenaceServerChange(pszPayload, pMaintenanceSrvChange, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestSensorTrigger(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, int nDataSize)
{
	if((pNormalMsg == NULL) || pSensorTriggerMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestSensorTrigger(pszPayload, pSensorTriggerMsg, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestAllTrigger(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger, int nDataSize)
{
	if((pNormalMsg == NULL) || pRequestAllTrigger == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestAllTrigger(pszPayload, pRequestAllTrigger, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseReportAllTrigger(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_ALL_TRIGGER *pReportAllTrigger, int nDataSize)
{
	if((pNormalMsg == NULL) || pReportAllTrigger == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReportAllTrigger(pszPayload, pReportAllTrigger, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
int CMsgControl::ParseReportDoorStatus(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REPORT_DOORSTATUS *pDoorStatus, INT nDataSize)
{
	if((pNormalMsg == NULL) || pDoorStatus == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReportDoorStatus(pszPayload, pDoorStatus, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}
#endif

int CMsgControl::ParseNotifyVisitorAuthMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuth, INT nDataSize)
{
	if((pNormalMsg == NULL) || pReportVisitorAuth == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseNotifyVisitorAuthMsg(pszPayload, pReportVisitorAuth, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseFaceDataForwardMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_DATA_FORWARD *pFaceDataForward, INT nDataSize)
{
	if((pNormalMsg == NULL) || pFaceDataForward == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseFaceDataForwardMsg(pszPayload, pFaceDataForward, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseSendTempKeyMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_SEND_TEMP_KEY *pSendTempKey, INT nDataSize)
{
	if((pNormalMsg == NULL) || pSendTempKey == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseSendTempKeyMsg(pszPayload, pSendTempKey, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseSendOssStsMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_SEND_OSS_STS *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseSendOssStsMsg(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRemoteAccessWeb(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REMOTE_ACCESS_WEB *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRemoteAccessWeb(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseOpenDoorACK(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_OPENDOOR_ACK *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseOpenDoorACK(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRegisterFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRegisterFace(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseModifyFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseModifyFace(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseDeleteFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseDeleteFace(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseGSFaceHttpApiLogin(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_GSFACE_HTTPAPI *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseGSFaceHttpApiLogin(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestPersonelData(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestPersonelData(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseSyncPersonelData(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseSyncPersonelData(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestFingerPrint(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestFingerPrint(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseSyncFingerPrint(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseSyncFingerPrint(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseNotifyAttendanceSrv(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_NOTIFY_ATTENDANCE_SERVICE *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseNotifyAttendanceSrv(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestKeepRelayOpenClose(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestKeepRelayOpenClose(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseBackupConfig(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_BACKUP_CONFIG *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseBackupConfig(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseBackupConfigRecovery(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BACKUP_CONFIG_RECOVERY *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseBackupConfigRecovery(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestRtspMonitor(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_RTSP_MONITOR *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestRtspMonitor(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRtspMonitorStop(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_RTSP_MONITOR_STOP *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRtspMonitorStop(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRegEndUser(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REG_END_USER *pData, int nDataSize)
{
	if((pNormalMsg == NULL) || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRegEndUser(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseRequestIsKit(SOCKET_MSG_NORMAL *pNormalMsg, INT& nKitFlag, int nDataSize)
{
	if(pNormalMsg == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseRequestIsKit(pszPayload, nKitFlag, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::ParseReportKitDevices(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int nDataSize)
{
	if(pNormalMsg == NULL || pData == NULL)
	{
		return -1;
	}	
	char *pszPayload = (char *)pNormalMsg->byData;
	if(GetMsgHandleInstance()->ParseReportKitDevices(pszPayload, pData, nDataSize) < 0)
	{
		return -1;
	}
	return 0;
}

int CMsgControl::AesDecryptByMac(char *pszSource, char *pszDest, int nDataSize)
{
	return GetMsgHandleInstance()->AesDecryptByMac(pszSource, pszDest, nDataSize);
}


