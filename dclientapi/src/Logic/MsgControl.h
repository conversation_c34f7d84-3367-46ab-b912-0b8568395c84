#ifndef __MSG_CONTROL_H__
#define __MSG_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "dclient_ipc.h"
#include "Def.h"

class CMsgControl
{
public:
	CMsgControl();
	~CMsgControl();

	int BuildBootupMsg(SOCKET_MSG *pSocketMsg);
	int BuildReportStatusMsg(SOCKET_MSG *pSocketMsg);
	int BuildCheckKeyMsg(SOCKET_MSG *pSocketMsg, int type, char *key);
	int BuildACKMsg(SOCKET_MSG *pSocketMsg, int msgid, USHORT crc, int result, char *info, int nSequenceNum = 0);
	int ParseACKMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ACK*pACKMsg, INT nDataSize);
	int BuildReportConfigMsg(SOCKET_MSG *pSocketMsg, CONFIG_MODULE *pConfigModule, BOOL bForceMac = FALSE);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int BuildReportConfigMsg(SOCKET_MSG *pSocketMsg, CONFIG_MODULE_FROM_DEVICE* pConfigModule, BOOL bForceMAC = FALSE);
#endif
	int BuildAlarmMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALARM_MSG *pszAlarmMsg);
	int BuildAlarmDealMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALARM_DEAL_INFO *pAlarmDeal);
	int BuildTextMsg(SOCKET_MSG *pSocketMsg, DCLIENT_TEXT_MSG *pTextMsg);
	int BuildAccessInfo(SOCKET_MSG *pSocketMsg, DCLIENT_ACCESS_INFO *pAccessInfo);
	int BuildHeartBeatMsg(SOCKET_MSG *pSocketMsg);
#if RL_GLOBAL_SUPPORT_TMP_KEY
	int BuildCheckTmpKeyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_CHECK_TMP_KEY *pCheckTmpKey);
#endif
	int BuildGetBindCodeRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum);
	int BuildUnBindCodeRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum, char *pszBindCode);
	int BuildGetBindCodeListRequestMsg(SOCKET_MSG *pSocketMsg, UINT nSerialNum);
	int BuildDiscoverMsg(SOCKET_MSG *pSocketMsg, DCLIENT_DISCOVER_SEND *pDiscoverMsg);
	int BuildDiscoverAckMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg);
#if RL_SUPPORT_CLOUD_DEV_INFO	
	int BuildRequestDeviceListMsg(SOCKET_MSG *pSocketMsg);
#endif
#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
	int BuildReportArmingMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_ARMING *pReportArmingMsg, BOOL bForceMAC = FALSE);
#endif
#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	int BuildPushForwardNumberMsg(SOCKET_MSG *pSocketMsg, DCLIENT_PUSH_NOANSWER_FWD_NUMBER *pPushForwardNumber);
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER	
	int BuildReportForwardNumberMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pReportForwardNumber);
#endif
#if RL_SUPPORT_REPORT_ACTIVITY
	int BuildMotionAlertMsg(SOCKET_MSG *pSocketMsg, DCLIENT_MOTION_ALERT *pMotionAlert);
	int BuildReportActivityMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_ACTIVITY *pReportActivity);
#endif

	int BuildRequestArmingMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_ARMING *pRequestArming, BOOL bForceMAC = FALSE);
	int BuildCheckDtmfMsg(SOCKET_MSG *pSocketMsg, DCLIENT_CHECK_DTMF *pCheckDtmfMsg);
	
	int BuildReportDeviceCodeMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_DEVICE_CODE *pReportDeviceCodeMsg);	
	int BuildReportNetworkInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_NETWORK_INFO *pReportNetworkInfoMsg);
#if RL_SUPPORT_DTMF_SET
	int BuildSendDTMFSetMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SET_DTMF *pDtmfSetMsg);
#endif
#if RL_SUPPORT_DEVICE_MAINTENANCE
	int BuildCliCommandRespMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_CLI_COMMAND_RESP *pSocketCliCommandRespMsg);
#endif
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int BuildRequestCfgFromDeviceMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_CONFIG *requestCfgMsg);
#endif
	int BuildReportCallCaptureMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture);
	int BuildReportTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_TRIGGER *pReportTrigger);
	int BuildManageBroadcastMsg(SOCKET_MSG *pSocketMsg, DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg);
	int BuildReportHealthMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_HEALTH *pReportHealth);
	int BuildResponseSensorTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg);
	int BuildUploadVideoNotifyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg);
	int BuildUploadCaptureNotifyMsg(SOCKET_MSG *pSocketMsg, DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg);
	int BuildReportAllTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger);
	int BuildRequestAllTriggerMsg(SOCKET_MSG *pSocketMsg, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger);
#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
	int BuildReportDoorStatusMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_DOORSTATUS *PDoorStatus);
#endif
#if RL_SUPPORT_SEND_REPORT_GAS
	int BuildReportGasMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_GAS *pReportGas);
#endif
	int BuildHeartBeatAckMsg(SOCKET_MSG *pSocketMsg);
	int BuildReportVisitorInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo);	
	int BuildReportVisitorAuthInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo);
	int BuildRequestOssStsMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_OSS_STS *pDate);
	int BuildAKCSACKMsg(SOCKET_MSG *pSocketMsg, SOCKET_MSG_AKCS_ACK *pAkcsAckMsg);	
	int BuildRequestOpenDoor(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_OPENDOOR *pData, int nType = 0);
	int BuildRequestACInfoMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_ACINFO *pData);
	int BuildSendDeliveryMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SEND_DELIVERY *pData);
	int BuildSyncActivityMsg(SOCKET_MSG *pSocketMsg, DCLIENT_SYNC_ACTIVITY *pData);
	int BuildReportRelayStatusMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_RELAY_STATUS *pData);
	int BuildFlowOutOfLimitMsg(SOCKET_MSG *pSocketMsg, DCLIENT_FLOW_OUT_OF_LIMIT *pData);
	int BuildReportCallLogMsg(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_CALLLOG *pData);
	int BuildBackupConfigACKMsg(SOCKET_MSG *pSocketMsg, DCLIENT_BACKUP_CONFIG_ACK *pData);
	int BuildRequestRtspMonitor(SOCKET_MSG *pSocketMsg, DCLIENT_REQUEST_RTSP_MONITOR *pData);
	int BuildRtspMonitorStop(SOCKET_MSG *pSocketMsg, DCLIENT_RTSP_MONITOR_STOP *pData);
	int BuildRequestEndUserReg(SOCKET_MSG *pSocketMsg);
	int BuildAddKitDevice(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData);
	int BuildReportKitDevice(SOCKET_MSG *pSocketMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData);
	int BuildRequestKitDevice(SOCKET_MSG *pSocketMsg);
	int BuildModifyDeviceLocation(SOCKET_MSG *pSocketMsg, KIT_DEVICE_BASE_INFO *pData);
#if RL_SUPPORT_ARMING_P2P
	int ParseRequestArmingMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQUEST_ARMING *pRequestArmingP2PMsg, INT nDataSize, BOOL bForceMAC = FALSE);	
	int ParseReportArmingMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_ARMING *pReportArming, int nDataSize, BOOL bForceMAC = FALSE);
#endif
	int ParseDiscoverMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, INT nDataSize);
	int ParseDiscoverAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, INT nDataSize);
	int ParseGetBindCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeCreate, int nDataSize);
	int ParseDeleteBindCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeDelete, int nDataSize);
	int ParseGetBindCodeListMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BIND_CODE_LIST *pBindCodeList, int nDataSize);
	int ParseNotifyBindCodeChangeMsg(SOCKET_MSG_NORMAL *pNormalMsg, INT *pnSequenceNum, int nDataSize);

	int ParseReqConnMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQ_CONN *pReqConnMsg, INT nDataSize);
	int ParseReqStatusMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQ_STATUS *pReqStatusMsg, INT nDataSize);
	int ParseRemoteControlMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg, INT nDataSize);
	int ParseReqConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG *pReqConfigMsg, INT nDataSize, BOOL bForceMAC = FALSE);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int ParseReqConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG_FROM_DEVICE *pReqConfigMsg, INT nDataSize, BOOL bForceMAC = FALSE);
#endif
	int ParseUpdateConfigMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG *pUpdateConfigMsg, INT nDataSize);

	int RecvUpdateConfigMsg(CONFIG_MODULE *pConfigModule);

	int ParseUpgradeStartMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_UPGRADE_START *pUpgradeStartMsg, INT nDataSize);
	int ParseFileEndMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_FILE_END *pFileEndMsg, INT nDataSize);
	int ParseOwnerMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_OWNER_MESSAGE*pOwnerMsg, INT nDataSize);
	int ParseTextMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_TEXT_MESSAGE*pTextMsg, INT nDataSize);
	int ParseKeySendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_KEY_SEND* pKeySendMsg, int nDataSize);
	int ParseUpgradeSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_UPGRADE_SEND* pUpgradeSendMsg, INT nDataSize);
	int ParseAdSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_AD_SEND* pAdSendMsg, INT nDataSize);
	int ParseAlarmSendMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM_SEND* pAlarmSendMsg, INT nDataSize);
	int ParseHeartBeatPeriodMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_HEARTBEAT_PERIOD* pHeartBeatPeriodMsg, INT nDataSize);
#if RL_GLOBAL_SUPPORT_TMP_KEY
	int ParseCheckTmpKeyMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CHECK_TMP_KEY* pCheckTmpKeyMsg, INT nDataSize);
#endif	
#if RL_GLOBAL_SUPPORT_VRTSP	
	int ParseStartRtspMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_START_RTSP* pStartRtspMsg, INT nDataSize);
	int ParseStopRtspMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_STOP_RTSP* pStopRtspMsg, INT nDataSize);
#endif

	int ParseAlarmMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM* pAlarmMsg, INT nDataSize);
	int ParseAlarmDealMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM_DEAL* pAlarmDealMsg, INT nDataSize);
	int ParseAlarmAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_ALARM* pAlarmMsg, INT nDataSize);
#if RL_SUPPORT_ARMING	
	int ParseRequestArmingMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQUEST_ARMING *pRequestArmingMsg, INT nDataSize);
#endif

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	int ParseReportForwardNumberMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumber, INT nDataSize);
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER	
	int ParsePushForwardNumberMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumber, INT nDataSize);
#endif	
	
	int ParseContactUrlMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONTACT_URL *pContactUrlMsg, int nDataSize);
	int ParseReconnectRPSMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_RPS *pReconnectRPSMsg, int nDataSize);
	int ParseReconnectGateWayMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_GATEWAY *pReconnectGateWayMsg, int nDataSize);
	int ParseReconnectAccessMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_RECONNECT_ACCESS_SERVER *pReconnectAccessMsg, int nDataSize);
	int ParseCheckDtmfAckMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CHECK_DTMF_ACK* pCheckDtmfAckMsg, int nDataSize);	
	int ParseDeviceCodeMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DEVICE_CODE* pDeviceCodeMsg, int nDataSize);
#if RL_SUPPORT_DEVICE_MAINTENANCE	
	int ParseMaintenanceGetLogMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_GETLOG* pMaintenanceGetLog, int nDataSize);	
	int ParseMaintenanceStartPcapMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_START_PCAP* pMaintenanceStartPcap, int nDataSize);	
	int ParseMaintenanceStopPcapMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_STOP_PCAP* pMaintenanceStopPcap, int nDataSize);	
	int ParseMaintenanceGetDevConfig(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG* pMaintenanceGetDevConfig, int nDataSize);	
	int ParseCliCommandMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CLI_COMMAND *pCliCommandMsg, int nDataSize);
#endif
#if RL_SUPPORT_RECV_MOTION_ALERT
	int ParseDoorMotionAlertMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DOOR_MOTION_ALERT* pDoorMotionAlert, int nDataSize);
#endif	
	int ParseCommonTransferMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_COMMON_TRANSFER* pCommonTransferMsg, int nDataSize);	
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC	
	int ParseDownUploadFacePicMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_DOWNUPLOAD_FACE_PIC *pDownUploadFacePicMsg, int nDataSize);
#endif	
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int ParseReqCfgFromDeviceMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg, int nDataSize);
#endif
	int ParseManageAlarmMsg(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg, int nDataSize);
	int ParseMaintenaceServerChange(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange, int nDataSize);
	int ParseRequestSensorTrigger(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, int nDataSize);
	int ParseRequestAllTrigger(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger, int nDataSize);
	int ParseReportAllTrigger(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_REPORT_ALL_TRIGGER *pReportAllTrigger, int nDataSize);
#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
	int ParseReportDoorStatus(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REPORT_DOORSTATUS *pDoorStatus, INT nDataSize);	
#endif		
	int ParseNotifyVisitorAuthMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuth, INT nDataSize);
	int ParseFaceDataForwardMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_DATA_FORWARD *pFaceDataForward, INT nDataSize);
	int ParseSendTempKeyMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_SEND_TEMP_KEY *pSendTempKey, INT nDataSize);
	int ParseSendOssStsMsg(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_SEND_OSS_STS *pData, INT nDataSize);	
	int ParseRemoteAccessWeb(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_REMOTE_ACCESS_WEB *pData, INT nDataSize);	
	int ParseOpenDoorACK(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_OPENDOOR_ACK *pData, INT nDataSize);
	int ParseRegisterFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseModifyFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseDeleteFace(SOCKET_MSG_NORMAL *pNormalMsg,  DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseGSFaceHttpApiLogin(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_GSFACE_HTTPAPI *pData, int nDataSize);	
	int ParseRequestPersonelData(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, int nDataSize);	
	int ParseSyncPersonelData(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, int nDataSize);	
	int ParseRequestFingerPrint(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, int nDataSize);
	int ParseSyncFingerPrint(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, int nDataSize);
	int ParseNotifyAttendanceSrv(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_NOTIFY_ATTENDANCE_SERVICE *pData, int nDataSize);
	int ParseRequestKeepRelayOpenClose(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE *pData, int nDataSize);
	int ParseBackupConfig(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_BACKUP_CONFIG *pData, int nDataSize);
	int ParseBackupConfigRecovery(SOCKET_MSG_NORMAL *pNormalMsg, SOCKET_MSG_BACKUP_CONFIG_RECOVERY *pData, int nDataSize);
	int ParseRequestRtspMonitor(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REQUEST_RTSP_MONITOR *pData, int nDataSize);
	int ParseRtspMonitorStop(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_RTSP_MONITOR_STOP *pData, int nDataSize);
	int ParseRegEndUser(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REG_END_USER *pData, int nDataSize);
	int ParseRequestIsKit(SOCKET_MSG_NORMAL *pNormalMsg, INT& nKitFlag, int nDataSize);
	int ParseReportKitDevices(SOCKET_MSG_NORMAL *pNormalMsg, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int nDataSize);
	
	int AesDecryptByMac(char *pszSource, char *pszDest, int nDataSize);
	
	static CMsgControl *GetInstance();


private:
	static CMsgControl *instance;
};

CMsgControl *GetMsgControlInstance();

#endif

