#include "DclientIncludes.h"

#include "SettingControl.h"
#include "ConnectControl.h"
#include "MsgControl.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "Lock.h"


CSettingControl *GetSettingControlInstance()
{
	return CSettingControl::GetInstance();
}

CSettingControl::CSettingControl()
{

}

CSettingControl::~CSettingControl()
{
	
}

CSettingControl *CSettingControl::instance = NULL;

CSettingControl *CSettingControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CSettingControl();
	}

	return instance;
}

int CSettingControl::GetConfigModule(CONFIG_MODULE *pConfigModule)
{
	return GetSettingHandleInstance()->GetConfigModule(pConfigModule); 
}

int CSettingControl::SetConfigModule(CONFIG_MODULE *pConfigModule)
{
#if 0
	int nCfgIDList[CFG_ID_NUM_MAX];
	for(UINT i=0; i<sizeof(nCfgIDList)/sizeof(int); i++)
	{
		nCfgIDList[i] = -1;
	}
	
	rl_log_debug("SetConfigModule");
	GetSettingHandleInstance()->SetConfigModule(pConfigModule, nCfgIDList, sizeof(nCfgIDList)/sizeof(int)); 

	for(UINT i=0; i<sizeof(nCfgIDList)/sizeof(int); i++)
	{
		if(nCfgIDList[i] != -1)
		{
			rl_log_debug("ipc_broadcast BROAD_ID_CONFIG_CHANGED %d", nCfgIDList[i]);
			//发送IPC广播
			ipc_broadcast(BROAD_ID_CONFIG_CHANGED, nCfgIDList[i], 0, NULL, 0);
		}
	}
#endif // 0

	return 0;
}

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
int CSettingControl::SendPushForwardNumber(DCLIENT_PUSH_NOANSWER_FWD_NUMBER *pPushForwardNumber)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildPushForwardNumberMsg(&socketMsg, pPushForwardNumber) < 0)
	{
		rl_log_err("%s: BuildPushForwardNumberMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}

int CSettingControl::RecvReportForwardNumber(SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumberMsg)
{
	if(pReportForwardNumberMsg == NULL)
	{
		return -1;
	}

	//发送消息给phone
	DCLIENT_REPORT_NOANSWER_FWD_NUMBER dclientReportForwardNumber;
	memset(&dclientReportForwardNumber, 0, sizeof(DCLIENT_REPORT_NOANSWER_FWD_NUMBER));

	rl_strcpy_s(dclientReportForwardNumber.from, sizeof(dclientReportForwardNumber.from), pReportForwardNumberMsg->szFrom);
	rl_strcpy_s(dclientReportForwardNumber.to, sizeof(dclientReportForwardNumber.to), pReportForwardNumberMsg->szTo);
	rl_strcpy_s(dclientReportForwardNumber.group0, sizeof(dclientReportForwardNumber.group0), pReportForwardNumberMsg->szGroup0);
	rl_strcpy_s(dclientReportForwardNumber.group1, sizeof(dclientReportForwardNumber.group1), pReportForwardNumberMsg->szGroup1);
	rl_strcpy_s(dclientReportForwardNumber.group2, sizeof(dclientReportForwardNumber.group2), pReportForwardNumberMsg->szGroup2);
	
	return ipc_send(IPC_ID_PHONE, MSG_D2P_REPORT_NOANSWER_FWD_NUMBER, 0, 0, (void *)&dclientReportForwardNumber, sizeof(DCLIENT_REPORT_NOANSWER_FWD_NUMBER));
}
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
int CSettingControl::SendReportForwardNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pReportForwardNumber)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildReportForwardNumberMsg(&socketMsg, pReportForwardNumber) < 0)
	{
		rl_log_err("%s: BuildReportForwardNumberMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}

int CSettingControl::RecvPushForwardNumber(SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumberMsg)
{
	if(pPushForwardNumberMsg == NULL)
	{
		return -1;
	}
	
	DCLIENT_REPORT_NOANSWER_FWD_NUMBER reportNoAnswerForwardNumber;
	memset(&reportNoAnswerForwardNumber, 0, sizeof(DCLIENT_REPORT_NOANSWER_FWD_NUMBER));

	//应答信息，From和To对调
	rl_strcpy_s(reportNoAnswerForwardNumber.group0, sizeof(reportNoAnswerForwardNumber.group0), pPushForwardNumberMsg->szGroup0);
	rl_strcpy_s(reportNoAnswerForwardNumber.group1, sizeof(reportNoAnswerForwardNumber.group1), pPushForwardNumberMsg->szGroup1);
	rl_strcpy_s(reportNoAnswerForwardNumber.group2, sizeof(reportNoAnswerForwardNumber.group2), pPushForwardNumberMsg->szGroup2);
	rl_strcpy_s(reportNoAnswerForwardNumber.from, sizeof(reportNoAnswerForwardNumber.from), pPushForwardNumberMsg->szTo);
	rl_strcpy_s(reportNoAnswerForwardNumber.to, sizeof(reportNoAnswerForwardNumber.to), pPushForwardNumberMsg->szFrom);

	//Action=Set则需要保存下来
	if(rl_strcasecmp(pPushForwardNumberMsg->szAction, "Set") == 0)
	{
		GetSettingHandleInstance()->SetNoAnswerFwdNumber(&reportNoAnswerForwardNumber);
	}

	//从配置库读取配置项并答复
	GetSettingHandleInstance()->GetNoAnswerFwdNumber(&reportNoAnswerForwardNumber);

	return GetSettingControlInstance()->SendReportForwardNumber(&reportNoAnswerForwardNumber);
}


#endif

#if RL_SUPPORT_DTMF_SET
int CSettingControl::SendDtmfSetMsg(DCLIENT_SET_DTMF *pDtmfSetMsg)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(SOCKET_MSG));
	if (GetMsgControlInstance()->BuildSendDTMFSetMsg(&socketMsg, pDtmfSetMsg) < 0)
	{
		rl_log_err("%s: BuildSendDTMFSetMsg failed.", __FUNCTION__);
		return -1;
	}
	if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
	{
		rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
		return -1;
	}

	rl_log_debug(" %s success.",__FUNCTION__);
	return 0;
}
#endif

