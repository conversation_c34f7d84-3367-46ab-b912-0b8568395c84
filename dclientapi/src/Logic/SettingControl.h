#ifndef __SETTING_CONTROL_H__
#define __SETTING_CONTROL_H__

#pragma once

#include "DclientIncludes.h"
#include "dclient_ipc.h"

class CSettingControl
{
public:
	CSettingControl();
	~CSettingControl();

	int GetConfigModule(CONFIG_MODULE *pConfigModule);
	int SetConfigModule(CONFIG_MODULE *pConfigModule);

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	int SendPushForwardNumber(DCLIENT_PUSH_NOANSWER_FWD_NUMBER *pPushForwardNumber);
	int RecvReportForwardNumber(SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumberMsg);
#endif	
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
	int SendReportForwardNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pReportForwardNumber);
	int RecvPushForwardNumber(SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumberMsg);
#endif
#if RL_SUPPORT_DTMF_SET	
	int SendDtmfSetMsg(DCLIENT_SET_DTMF *pDtmfSetMsg);
#endif	
	static CSettingControl *GetInstance();


private:
	static CSettingControl *instance;
};

CSettingControl *GetSettingControlInstance();

#endif

