#include "MsgControl.h"
#include "ConnectControl.h"
#include "TextMsgControl.h"
#include "dclient_ipc.h"
#include "DclientDefine.h"
#include "Utility.h"
#include "Lock.h"

CTextMessageControl *GetTextMessageControlInstance()
{
	return CTextMessageControl::GetInstance();
}

CTextMessageControl::CTextMessageControl()
{
	INIT_LIST_HEAD(&m_listTextMsg);
	m_lock = new CLock();
}

CTextMessageControl::~CTextMessageControl()
{
	if(NULL != m_lock)
	{
		delete (CLock *)m_lock;
		m_lock = NULL;
	}
}

CTextMessageControl *CTextMessageControl::instance = NULL;

CTextMessageControl *CTextMessageControl::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CTextMessageControl();
	}

	return instance;
}

//发送TEXT消息
int CTextMessageControl::SendTextMsg(DCLIENT_TEXT_MSG *pTextMsg, INT nType)
{
	SOCKET_MSG socketMsg;
	memset(&socketMsg, 0, sizeof(socketMsg));
	if (GetMsgControlInstance()->BuildTextMsg(&socketMsg, pTextMsg) < 0)
	{
		rl_log_err("%s: BuildAlarmMsg failed.", __FUNCTION__);
		return -1;
	}
	if(nType == TRANSPORT_TYPE_UDP)
	{
		if(GetConnectControlInstance()->SendUdpMsg(pTextMsg->to_name, SOCKET_MULTICAST_PORT, socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendUdpMsg failed.", __FUNCTION__);
			return -1;
		}
	}
	else
	{
		if(GetConnectControlInstance()->SendTcpMsg(socketMsg.byData, socketMsg.nSize) < 0)
		{
			rl_log_err("%s: SendTcpMsg failed.", __FUNCTION__);
			return -1;
		}
	}

	rl_log_debug("SendTextMsg success.");
	return 0;
}

//收到TEXT消息
/*int CTextMessageControl::OnRecvTextSend(SOCKET_MSG_TEXT_SEND *pTextSend)
{
	DLCIENT_RECV_TEXT_DATA alarm;
	memset(&alarm, 0, sizeof(DLCIENT_RECV_TEXT_DATA));

	alarm.id = pTextSend->id;
	alarm.nExtension = pTextSend->nExtension;
	alarm.nDeviceType = pTextSend->nDeviceType;
	alarm.id = pTextSend->id;
	rl_strcpy_s(alarm.szAddress, sizeof(alarm.szAddress), pTextSend->szAddress);
	rl_strcpy_s(alarm.szType, sizeof(alarm.szType), pTextSend->szType);
	rl_strcpy_s(alarm.szTime, sizeof(alarm.szTime), pTextSend->szTime);

	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_RECV_TEXT, 0, 0, &alarm, sizeof(alarm));
}*/

int CTextMessageControl::OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg)
{
	if(pDiscoverAckMsg == NULL)
	{
		return -1;
	}
	Lock();
	list_t *pos = NULL;
	list_t *tmp = NULL;
	list_for_each_safe(pos, tmp, &m_listTextMsg)
	{
		DCLIENT_TEXT_MSG_NODE *pTmpTextMsgNode = list_entry(pos, DCLIENT_TEXT_MSG_NODE, node);
		INT nType = -1;
		INT nExtension = -1;
		CHAR szDeviceID[DEVICE_ID_SIZE] = "";
		if(pTmpTextMsgNode->dclientTextMsg.nSequenceNum != pDiscoverAckMsg->nSequenceNum)
		{
			continue;
		}
		if(rl_strchr(pTmpTextMsgNode->dclientTextMsg.to, '_') == NULL)
		{
			sscanf(pTmpTextMsgNode->dclientTextMsg.to, "%[0-9a-zA-Z.]-%d", szDeviceID, &nExtension);
		}
		else
		{		
			sscanf(pTmpTextMsgNode->dclientTextMsg.to, "%d_%[0-9a-zA-Z.]-%d", &nType,szDeviceID, &nExtension);
		}		
		if(rl_strcmp(szDeviceID, pDiscoverAckMsg->szDeviceID) == 0 &&
			(nType == -1 || nType == atoi(pDiscoverAckMsg->szType))&&
			(nExtension == -1 || nExtension == atoi(pDiscoverAckMsg->szExtension)) ||
			(nType == SDMC_DEVICE_TYPE_MANAGEMENT && atoi(pDiscoverAckMsg->szType) == SDMC_DEVICE_TYPE_MANAGEMENT && rl_strcmp(szDeviceID, "0.0.0.0.0") == 0))
		{
			rl_strcpy_s(pTmpTextMsgNode->dclientTextMsg.to_name, sizeof(pTmpTextMsgNode->dclientTextMsg.to_name), pDiscoverAckMsg->szIPAddr);
			SendTextMsg(&pTmpTextMsgNode->dclientTextMsg, TRANSPORT_TYPE_UDP);
			list_del(&pTmpTextMsgNode->node);
			//pTmpTextMsgNode->bSendFlag = TRUE;
			delete pTmpTextMsgNode;
			pTmpTextMsgNode = NULL;
		}
	}
	Unlock();
	return 0;
}

int CTextMessageControl::AddTextMsgToList(DCLIENT_TEXT_MSG *pTextMsg)
{
	if(pTextMsg == NULL)
	{
		return -1;
	}
	DCLIENT_TEXT_MSG_NODE *pTextMsgNode;
	pTextMsgNode = new DCLIENT_TEXT_MSG_NODE;
	rl_memset(pTextMsgNode, 0, sizeof(DCLIENT_TEXT_MSG_NODE));
	rl_memcpy(&pTextMsgNode->dclientTextMsg, pTextMsg, sizeof(DCLIENT_TEXT_MSG));
	rl_gettimeofday(&pTextMsgNode->time_val);
	Lock();
	list_add_tail(&pTextMsgNode->node, &m_listTextMsg);
	Unlock();
	return 0;
}


//处理定时器
int CTextMessageControl::ProcessBaseTimer()
{
	rl_time_val time_val;
	rl_gettimeofday(&time_val);
	
	static int i=0;
	if(++i % 5 == 0)
	{
		Lock();
		//检查发送列表中的TextMsg是否符合条件发送
		list_t *pos = NULL;
		list_t *tmp = NULL;
		list_for_each_safe(pos, tmp, &m_listTextMsg)
		{
			DCLIENT_TEXT_MSG_NODE *pTmpTextMsgNode = list_entry(pos, DCLIENT_TEXT_MSG_NODE, node);
			int subtime = rl_get_subtimeint(&pTmpTextMsgNode->time_val, &time_val);
			if(subtime >= 3 || subtime < 0)//为防止没有发送成功然后整个链表堆积起来，3s之内没有发送出去的就直接删除掉
			{
				list_del(&pTmpTextMsgNode->node);
				delete pTmpTextMsgNode;
				pTmpTextMsgNode = NULL;
			}
			
		}
		Unlock();
	}
	if(i == 10000)
	{
		i = 0;
	}
	return 0;
}

int CTextMessageControl::OnTextMsgAck(SOCKET_MSG_ACK *pTextMsgAck)
{
	if(pTextMsgAck == NULL)
	{
		return -1;
	}
	DCLIENT_MSG_ACK textMsgAck;
	rl_memset(&textMsgAck, 0, sizeof(DCLIENT_MSG_ACK));
	rl_strcpy_s(textMsgAck.szMsgID, sizeof(textMsgAck.szMsgID), pTextMsgAck->szMsgID);
	rl_strcpy_s(textMsgAck.szMsgCRC, sizeof(textMsgAck.szMsgCRC), pTextMsgAck->szMsgCRC);
	rl_strcpy_s(textMsgAck.szResult, sizeof(textMsgAck.szResult), pTextMsgAck->szResult);
	rl_strcpy_s(textMsgAck.szInfo, sizeof(textMsgAck.szInfo), pTextMsgAck->szInfo);
	textMsgAck.nSquenceNum = pTextMsgAck->nSequenceNum;
	//发送IPC消息给PHONE
	return ipc_send(IPC_ID_PHONE, MSG_D2P_ACK, 0, 0, &textMsgAck, sizeof(textMsgAck));
}


//上锁消息缓冲区
void CTextMessageControl::Lock()
{
	((CLock *)m_lock)->Lock();
}

//解锁消息缓冲区
void CTextMessageControl::Unlock()
{
	((CLock *)m_lock)->Unlock();
}


