#ifndef __TEXTMSG_CONTROL_H__
#define __TEXTMSG_CONTROL_H__
#include <time.h>

typedef struct DCLIENT_TEXT_MSG_NODE_T
{
	DCLIENT_TEXT_MSG dclientTextMsg;
	<PERSON>O<PERSON> bSendFlag;
	rl_time_val time_val;
	list_t node;
}DCLIENT_TEXT_MSG_NODE;

class CTextMessageControl
{
public:
	CTextMessageControl();
	~CTextMessageControl();
	
	//发送TEXT消息
	int SendTextMsg(DCLIENT_TEXT_MSG *pTextMsg, INT nType = TRANSPORT_TYPE_TCP);

	//收到TEXT消息
	//int OnRecvTextSend(SOCKET_MSG_ALARM_SEND *pTextSend);
	int OnDiscoverAckMsg(SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg);
	int AddTextMsgToList(DCLIENT_TEXT_MSG *pTextMsg);
	int ProcessBaseTimer();
	int OnTextMsgAck(SOCKET_MSG_ACK *pTextMsgAck);
	static CTextMessageControl *GetInstance();
protected:
	list_t m_listTextMsg;
private:
	static CTextMessageControl *instance;
	void Lock();
	void Unlock();


	void *m_lock;
public:

};

CTextMessageControl *GetTextMessageControlInstance();

#endif