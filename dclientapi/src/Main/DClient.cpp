#include "DClient.h"
#include "../../IDClientDelegate.h"
#include "Control.h"
#include "ConnectControl.h"
#include "SimpleIni.h"
#include "dclient_ipc.h"
#include "DeviceControl.h"
#include "SettingHandle.h"


CDClient::CDClient(void)
	: m_pDelegate(NULL)
	, m_strDownloadFolderPath("")
	, m_strConfigFilePath("")
	, m_pIniFile(NULL)
{
	m_pIniFile = new CSimpleIni;
}


/*@function
*******************************************************************
功  能:  获取实例.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
CDClient* CDClient::GetInstance()
{
	static CDClient* s_pInstance = NULL;
	if (NULL == s_pInstance)
	{
		s_pInstance = new CDClient;
	}

	return s_pInstance;
}

CDClient::~CDClient(void)
{
	delete m_pIniFile;
}

/*@function
*******************************************************************
功  能:  初始化.

参  数:  pstrDownloadFolderPath : 存放下载文件的文件夹路径
		 pstrConfigFilePath : 配置文件路径
		 pDelegate : 委托的指针

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
int CDClient::Init(const char* pstrDownloadFolderPath, const char* pstrConfigFilePath, IDClientDelegate* pDelegate)
{
	m_strDownloadFolderPath = NULL == pstrDownloadFolderPath ? "" : pstrDownloadFolderPath;
	m_strConfigFilePath = NULL == pstrConfigFilePath ? "" : pstrConfigFilePath;
	m_pDelegate = pDelegate;

	InitConfig();
	rl_log_info("Init finish, DownloadFolderPath:%s, ConfigFilePath:%s, delegate:%d.", pstrDownloadFolderPath, pstrConfigFilePath, pDelegate);
	return 0;
}

/*@function
*******************************************************************
功  能:  反初始化.

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
int CDClient::UnInit()
{
	GetControlInstance()->UnInit();
	UnInitConfig();
	m_pDelegate = NULL;

	rl_log_info("UnInit finish.");
	return 0;
}

/*@function
*******************************************************************
功  能:  设置设备信息.

参  数:  infoDevice: 设备信息，包括网络IP，MAC等

返回值:  成功返回0，失败返回-1.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.26
******************************************************************/
int CDClient::SetDeviceInfo(const DCLIENT_DEVICE_INFO& infoDevice)
{
	bool bNeedReconnect = false;
	if (strcmp(infoDevice.szIPAddr, m_infoDevice.szIPAddr) != 0 ||
		strcmp(infoDevice.szPrimaryDNS, m_infoDevice.szPrimaryDNS) != 0 ||
		strcmp(infoDevice.szSecondaryDNS, m_infoDevice.szSecondaryDNS) != 0 ||
		strcmp(infoDevice.szMask, m_infoDevice.szMask) != 0 ||
		strcmp(infoDevice.szGateWay, m_infoDevice.szGateWay) != 0 ||
		strcmp(infoDevice.szMAC, m_infoDevice.szMAC) != 0)
	{
		bNeedReconnect = true;
	}

	memcpy(&m_infoDevice, &infoDevice, sizeof(DCLIENT_DEVICE_INFO));
	GetSettingHandleInstance()->SetMAC(m_infoDevice.szMAC);

	if (bNeedReconnect)
	{
		GetConnectControlInstance()->SetReconnectFlag(TRUE);
	}

	return 0;
}

/*@function
*******************************************************************
功  能:  运行.

参  数:  

返回值:  成功返回0，失败返回-1.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.26
******************************************************************/
int CDClient::Run()
{
	GetControlInstance()->Init();
	return 	GetControlInstance()->Run();
}

/*@function
*******************************************************************
功  能:  请求开门.

参  数:	 pstrMAC : 请求哪台设备开门
		 nRelay : 开哪个门

返回值:  请求发送成功返回0，失败返回-1.
------------------------------------------------------------------
作  者:  kangyujin, 2022.08.01
******************************************************************/
int CDClient::RequestOpenDoor(const char* pstrMAC, const char* pstrRelayID)
{
	if (NULL == pstrMAC ||
		NULL == pstrRelayID)
	{
		return -1;
	}

	DCLIENT_REQUEST_OPENDOOR infoRequestOpenDoor;
	memset(&infoRequestOpenDoor, 0, sizeof(DCLIENT_REQUEST_OPENDOOR));
	rl_strcpy_s(infoRequestOpenDoor.szMAC, sizeof(infoRequestOpenDoor.szMAC), pstrMAC);
	rl_strcpy_s(infoRequestOpenDoor.szRelay, sizeof(infoRequestOpenDoor.szRelay), pstrRelayID);
	return GetDeviceControlInstance()->RequestOpenDoor((DCLIENT_REQUEST_OPENDOOR*)&infoRequestOpenDoor, 0);
}

/*@function
	*******************************************************************
	功  能:  发送告警
	参  数:	 alarmMsg : 发送的alarm消息
			

	返回值:  请求发送成功返回0，失败返回-1.
	------------------------------------------------------------------
	作  者:  jeffrey, 2023.11.13
	******************************************************************/
int CDClient::SendAlarm(DCLIENT_ALARM_MSG& alarmMsg, int nSendType)
{
	return GetDeviceControlInstance()->SendAlarm(alarmMsg);
}

/*@function
	*******************************************************************
	功  能:  发送解除告警
	参  数:	 alarmDeal : 发送的告警解除消息
			

	返回值:  请求发送成功返回0，失败返回-1.
	------------------------------------------------------------------
	作  者:  kaijia, 2025.04.07
	******************************************************************/
int CDClient::SendAlarmDeal(DCLIENT_ALARM_DEAL_INFO& alarmDeal, int nSendType)
{
	return GetDeviceControlInstance()->SendAlarmDeal(alarmDeal, nSendType);
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, void* pData, int nDataSize)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2, (DCLIENT_NOTIFY_HANDLE_FILE_INFO*)pData, nDataSize);
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.27
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, const char* pData)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2, pData);
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.28
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, int nData)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2, nData);
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.08.03
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_SIP_INFO* pSipInfo, int nDataSize)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2, pSipInfo, nDataSize);
}

/*@function
*******************************************************************
功  能:  获取物业信息.

参  数:

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kaijia.wu, 2023.05.10
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_OWNER_MSG *pTextMsg, int nDataSize)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2, pTextMsg, nDataSize);
}

/*@function
*******************************************************************
功  能:  .

参  数:  

返回值:  0 on success, others on failed.
------------------------------------------------------------------
作  者:  kangyujin, 2022.08.03
******************************************************************/
int CDClient::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2)
{
	if (NULL == m_pDelegate)
	{
		rl_log_err("delegate is null, notify message faild.");
		return -1;
	}

	rl_log_info("notify phone message id:%d.", nMsgID);
	return m_pDelegate->OnMessage(nMsgID, uParam1, uParam2);
}

/*@function
*******************************************************************
功  能:  获取下载文件夹路径.

参  数:  

返回值:  下载文件夹路径.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
string CDClient::GetDownloadFolderPath()
{
	string strPath = m_strDownloadFolderPath;
	if (strPath.empty())
	{
		return "";
	}

	if (strPath.substr(strPath.length() - 1, 1) != "/" &&
		strPath.substr(strPath.length() - 1, 1) != "\\")
	{
		strPath += "/";
	}

	return strPath;
}

/*@function
*******************************************************************
功  能:  获取设备信息.

参  数:  pInfo : 传出设备信息

返回值:  是否获取成功.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.22
******************************************************************/
bool CDClient::GetDeviceInfo(DCLIENT_DEVICE_INFO* pInfo)
{
	if (nullptr == pInfo)
	{
		return false;
	}

	memcpy(pInfo, &m_infoDevice, sizeof(DCLIENT_DEVICE_INFO));
	return true;
}

/*@function
*******************************************************************
功  能:  设置配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.27
******************************************************************/
bool CDClient::CfgSetString(const char* pstrSession, const char* pstrKey, const char* pstrValue)
{
	return SetConfig(pstrSession, pstrKey, pstrValue, rl_strcmp(pstrSession, "Status") != 0);
}

/*@function
*******************************************************************
功  能:  读取配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.27
******************************************************************/
bool CDClient::CfgGetString(const char* pstrSession, const char* pstrKey, char* pstrValue, int nBuffSize, const char* pstrDefaultValue)
{
	return GetConfig(pstrSession, pstrKey, pstrValue, nBuffSize, pstrDefaultValue);
}

/*@function
*******************************************************************
功  能:  打日志.

参  数:  pstrLog : 日志内容

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
void CDClient::OutputLog(const char* pstrLog)
{
	if (NULL == m_pDelegate || NULL == pstrLog)
	{
		return;
	}

	m_pDelegate->OuputLog(pstrLog);
}

/*@function
*******************************************************************
功  能:  设置配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.27
******************************************************************/
bool CDClient::CfgSetInt(const char* pstrSession, const char* pstrKey, int nValue)
{
	char szTmp[40] = {0};
	rl_sprintf_s(szTmp, sizeof(szTmp), "%d", nValue);
	return SetConfig(pstrSession, pstrKey, szTmp, rl_strcmp(pstrSession, "Status") != 0);
}

/*@function
*******************************************************************
功  能:  读取配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.27
******************************************************************/
int CDClient::CfgGetInt(const char* pstrSession, const char* pstrKey, int nDefaultValue)
{
	char szDefaultValue[40] = {0};
	rl_sprintf_s(szDefaultValue, sizeof(szDefaultValue), "%d", nDefaultValue);

	char szTmp[40] = {0};
	bool bGet = GetConfig(pstrSession, pstrKey, szTmp, sizeof(szTmp), szDefaultValue);
	if (false == bGet)
	{
		return nDefaultValue;
	}

	return atoi(szTmp);
}

/*@function
*******************************************************************
功  能:  获取 DClient 实例.

参  数:  

返回值:  DClient 实例.
------------------------------------------------------------------
作  者:  kangyujin, 2022.06.21
******************************************************************/
CDClient* GetDClientInstance()
{
	return CDClient::GetInstance();
}

IDClient* GetIDClientInstance()
{
	return CDClient::GetInstance();
}

/*@function
*******************************************************************
功  能:  初始化配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
void CDClient::InitConfig()
{
	m_pIniFile->SetUnicode();
	m_pIniFile->LoadFile(m_strConfigFilePath.c_str());
}

/*@function
*******************************************************************
功  能:  反初始化配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
void CDClient::UnInitConfig()
{
	m_pIniFile->Reset();
}

/*@function
*******************************************************************
功  能:  设置配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
bool CDClient::SetConfig(const char* pstrSession, const char* pstrKey, const char* pstrValue, bool bSaveFile /*= true*/)
{
	if (m_strConfigFilePath.empty() || 
		NULL == pstrSession || 
		NULL == pstrKey || 
		NULL == pstrValue)
	{
		return false;
	}

	if (rl_strcmp(pstrSession, "Status") == 0)
	{
		m_mapStatus[pstrKey] = pstrValue == NULL ? "" : pstrValue;
		return true;
	}

	m_pIniFile->SetValue(pstrSession, pstrKey, pstrValue);
	if (bSaveFile)
	{
		m_pIniFile->SaveFile(m_strConfigFilePath.c_str());
	}
	return true;
}

/*@function
*******************************************************************
功  能:  获取配置.

参  数:  

返回值:  <无>.
------------------------------------------------------------------
作  者:  kangyujin, 2022.07.05
******************************************************************/
bool CDClient::GetConfig(const char* pstrSession, const char* pstrKey, char* pstrValue, int nBuffSize, const char* pstrDefaultValue)
{
	if (m_strConfigFilePath.empty() || 
		NULL == pstrSession || 
		NULL == pstrKey)
	{
		if (m_strConfigFilePath.empty())
		{
			rl_log_err("config file path is empty.");
		}
		else
		{
			rl_log_err("get invalid config.");
		}

		return false;
	}

	if (rl_strcmp(pstrSession, "Status") == 0)
	{
		if (m_mapStatus.find(pstrKey) == m_mapStatus.end())
		{
			rl_strcpy_s(pstrValue, nBuffSize, pstrDefaultValue);
		}
		else
		{
			rl_strcpy_s(pstrValue, nBuffSize, m_mapStatus[pstrKey].c_str());
		}
		
		return true;
	}

	const char* pstrGetValue = m_pIniFile->GetValue(pstrSession, pstrKey, pstrDefaultValue);
	if (NULL == pstrGetValue)
	{
		rl_strcpy_s(pstrValue, nBuffSize, pstrDefaultValue);
	}
	else
	{
		rl_strcpy_s(pstrValue, nBuffSize, pstrGetValue);
	}

	return true;
}
