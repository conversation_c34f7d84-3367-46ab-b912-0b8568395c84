#pragma once
#include "../../Def.h"
#include "../../IDClient.h"
#include "DclientMsg.h"
#include <string.h>
#include "SimpleIni.h"
using namespace std;

#define CFG_ID_ACCOUNT_01 ("Account1")
#define CFG_ID_STATUS ("Status")
#define CFG_ID_DOORSETTING ("DoorSetting")

#define DOORSETTING_KEY_CLOUDSERVER_SERVER ("CLOUDSERVER.Server")
#define DOORSETTING_KEY_CLOUDSERVER_PORT ("CLOUDSERVER.Port")
#define DOORSETTING_KEY_CLOUDSERVER_GATESERVER ("CLOUDSERVER.GateServer")
#define DOORSETTING_KEY_CLOUDSERVER_GATEPORT ("CLOUDSERVER.GatePort")
#define DOORSETTING_KEY_CLOUDSERVER_RPSENABLE ("CLOUDSERVER.RpsEnable")
#define DOORSETTING_KEY_CLOUDSERVER_RPSSERVER ("CLOUDSERVER.RpsServer")
#define DOORSETTING_KEY_CLOUDSERVER_TOKEN ("CLOUDSERVER.Token")
#define DOORSETTING_KEY_CLOUDSERVER_ENABLE ("CLOUDSERVER.Enable")
#define DOORSETTING_KEY_DEVICENODE_LOCATION ("DEVICENODE.Location")
#define DOORSETTING_KEY_CONNECT_SUBMODE ("CONNECT.SubMode")
#define DOORSETTING_KEY_CONNECT_SERVER_MODE ("CONNECT.ServerMode")
#define DOORSETTING_KEY_CONNECT_DISCOVERY_MODE ("CONNECT.DiscoveryMode")
#define DOORSETTING_KEY_UPDATE_CONFIGMD5 ("UPDATE.ConfigMD5")
#define DOORSETTING_KEY_UPDATE_CONTACTMD5 ("UPDATE.ContactMD5")
#define STATUS_KEY_DOORSETTING_DEVICE_CODE ("DEVICE.Code")
#define STATUS_KEY_MAINTENANCE_SERVER_WEB ("MAINTENANCE.ServerWeb")
#define STATUS_KEY_MAINTENANCE_SERVER_FTP ("MAINTENANCE.ServerFtp")
#define STATUS_KEY_MAINTENANCE_SERVER_VRTSP ("MAINTENANCE.Vrtsp")
#define STATUS_KEY_NETWORK_MAC ("NETWORK.Mac")
#define STATUS_KEY_DOORSETTING_CONNECT_MODE ("CONNECT.Mode")
#define ACCOUNT_KEY_GENERAL_ENABLE ("GENERAL.Enable")
#define ACCOUNT_KEY_SIP_SERVER ("SIP.Server")
#define ACCOUNT_KEY_SIP_PORT ("SIP.Port")
#define ACCOUNT_KEY_SIP_TRANSTYPE ("SIP.TransType")
#define ACCOUNT_KEY_SIP_USERNAME ("GENERAL.UserName")
#define ACCOUNT_KEY_SIP_PASSWORD ("GENERAL.Pwd")
#define ACCOUNT_KEY_GENERAL_DISPLAYNAME ("GENERAL.DisplayName")

class CDClient : public IDClient
{
public:
	static CDClient* GetInstance(); 
	virtual ~CDClient(void);
	virtual int Init(const char* pstrDownloadFolderPath, const char* pstrConfigFilePath, IDClientDelegate* pDelegate);
	virtual int UnInit();
	virtual int SetDeviceInfo(const DCLIENT_DEVICE_INFO& infoDevice);
	virtual int Run();
	virtual int RequestOpenDoor(const char* pstrMAC, const char* pstrRelayID);
	virtual int SendAlarm(DCLIENT_ALARM_MSG& alarmMsg, int nSendType = TRANSPORT_TYPE_TCP);
	virtual int SendAlarmDeal(DCLIENT_ALARM_DEAL_INFO& alarmDeal, int nSendType = TRANSPORT_TYPE_TCP);

public:
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, void* pData, int nDataSize);
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_SIP_INFO* pSipInfo, int nDataSize);
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_OWNER_MSG* pTextMsg, int nDataSize);
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, const char* pData);
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, int nData);
	int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2);
	string GetDownloadFolderPath();
	bool GetDeviceInfo(DCLIENT_DEVICE_INFO* pInfo);
	bool CfgSetString(const char* pstrSession, const char* pstrKey, const char* pstrValue);
	bool CfgGetString(const char* pstrSession, const char* pstrKey, char* pstrValue, int nBuffSize, const char* pstrDefaultValue);
	bool CfgSetInt(const char* pstrSession, const char* pstrKey, int nValue);
	int CfgGetInt(const char* pstrSession, const char* pstrKey, int nDefaultValue);

	void OutputLog(const char* pstrLog);	//����־

private:
	void InitConfig();
	void UnInitConfig();
	bool SetConfig(const char* pstrSession, const char* pstrKey, const char* pstrValue, bool bSaveFile = true);
	bool GetConfig(const char* pstrSession, const char* pstrKey, char* pstrValue, int nBuffSize, const char* pstrDefaultValue);

private:
	CDClient(void);

private:
	IDClientDelegate* m_pDelegate;
	string m_strDownloadFolderPath;
	string m_strConfigFilePath;
	map<string, string> m_mapStatus;
	CSimpleIni* m_pIniFile;
	DCLIENT_DEVICE_INFO m_infoDevice;
};

extern "C" {
	__attribute__ ((visibility("default"))) CDClient* GetDClientInstance();
}
