#include "MsgHandle.h"
#include "rl_log.h"
#include "tinyxml.h"
#include "AES256.h"
#include "SettingHandle.h"
#include "Utility.h"
#include "DclientDefine.h"


#define XML_NODE_NAME_MSG					"Msg"
#define XML_NODE_NAME_MSG_TYPE				"Type"
#define XML_NODE_NAME_MSG_PROTOCAL			"Protocal"
#define XML_NODE_NAME_MSG_FROM				"From"
#define XML_NODE_NAME_MSG_TO				"To"
#define XML_NODE_NAME_MSG_PARAM				"Params"
#define XML_NODE_NAME_MSG_PARAM_IP			"IP"
#define XML_NODE_NAME_MSG_PARAM_SUBNETMASK	"SM"
#define XML_NODE_NAME_MSG_PARAM_GATEWAY		"GW"
#define XML_NODE_NAME_MSG_PARAM_PRIMARYDNS	"DNS1"
#define XML_NODE_NAME_MSG_PARAM_SECONDARYDNS	"DNS2"
#define XML_NODE_NAME_MSG_PARAM_PORT		"Port"
#define XML_NODE_NAME_MSG_PARAM_DEVICEID	"DeviceID"
#define XML_NODE_NAME_MSG_PARAM_EXTENSION	"Extension"
#define XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER "DownloadServer"
#define XML_NODE_NAME_MSG_PARAM_UPLOADSERVER	"UploadServer"
#define XML_NODE_NAME_MSG_PARAM_REMOTE_IP	"RemoteIP"
#define XML_NODE_NAME_MSG_PARAM_REMOTE_PORT	"RemotePort"
#define XML_NODE_NAME_MSG_PARAM_FLAG		"Flag"
#define XML_NODE_NAME_MSG_PARAM_DEVICE_NAME	"DeviceName"
#define XML_NODE_NAME_MSG_PARAM_RTSP_URL	"RTSP"

#define XML_NODE_NAME_MSG_PARAM_MAC			"MAC"
#define XML_NODE_NAME_MSG_PARAM_STATUS		"Status"
#define XML_NODE_NAME_MSG_PARAM_SWVER		"SWVer"
#define XML_NODE_NAME_MSG_PARAM_HWVER		"HWVer"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYURL	"PrikeyUrl"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYMD5	"PrikeyMD5"
#define XML_NODE_NAME_MSG_PARAM_RFIDURL		"RfidUrl"
#define XML_NODE_NAME_MSG_PARAM_RFIDMD5		"RfidMD5"
#define XML_NODE_NAME_MSG_PARAM_ADDRURL		"AddrUrl"
#define XML_NODE_NAME_MSG_PARAM_ADDRMD5		"AddrMD5"
#define XML_NODE_NAME_MSG_PARAM_CONFIGURL	"ConfigUrl"
#define XML_NODE_NAME_MSG_PARAM_CONFIGMD5	"ConfigMD5"
#define XML_NODE_NAME_MSG_PARAM_ADMODULEURL	"AdModuleUrl"
#define XML_NODE_NAME_MSG_PARAM_ADMODULEMD5	"AdModuleMD5"

#define XML_NODE_NAME_MSG_PARAM_CONTACTMD5	"ContactMD5"
#define XML_NODE_NAME_MSG_PARAM_CONTACTURL	"ContactUrl"
#define XML_NODE_NAME_MSG_PARAM_FACEIDMD5	"FaceidMD5"
#define XML_NODE_NAME_MSG_PARAM_FACEIDURL	"FaceidUrl"
#define XML_NODE_NAME_MSG_PARAM_AUTHCODE	"AuthCode"
#define XML_NODE_NAME_MSG_PARAM_FPURL		"FPUrl"
#define XML_NODE_NAME_MSG_PARAM_FPMD5		"FPMD5"
#define XML_NODE_NAME_MSG_PARAM_ARMING		"Arming"

#define XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKURL		"RemotePhonebookUrl"
#define XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKMD5		"RemotePhonebookMD5"
#define XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION		"DclientVer"

#define XML_NODE_NAME_MSG_PARAM_ITEM		"Item"
#define XML_NODE_NAME_MSG_PARAM_MODULE		"Module"
#define XML_NODE_NAME_MSG_PARAM_PATH		"Path"
#define XML_NODE_NAME_MSG_PARAM_SIZE		"Size"
#define XML_NODE_NAME_MSG_PARAM_MD5			"MD5"
#define XML_NODE_NAME_MSG_PARAM_KEYTYPE		"KeyType"
#define XML_NODE_NAME_MSG_PARAM_KEYVALUE	"KeyValue"
#define XML_NODE_NAME_MSG_PARAM_MSGID		"MsgID"
#define XML_NODE_NAME_MSG_PARAM_MSGCRC		"MsgCRC"
#define XML_NODE_NAME_MSG_PARAM_RESULT		"Result"
#define XML_NODE_NAME_MSG_PARAM_INFO		"Info"
#define XML_NODE_NAME_MSG_PARAM_FROM		"From"
#define XML_NODE_NAME_MSG_PARAM_FROM_NAME	"FromName"
#define XML_NODE_NAME_MSG_PARAM_TO			"To"
#define XML_NODE_NAME_MSG_PARAM_TO_NAME		"ToName"
#define XML_NODE_NAME_MSG_PARAM_MODE		"Mode"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREVER "FirmwareVer"
#define XML_NODE_NAME_MSG_PARAM_FW 			"FW"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREURL	"FirmwareUrl"
#define XML_NODE_NAME_MSG_PARAM_TITLE		"Titel"
#define XML_NODE_NAME_MSG_PARAM_CONTENT		"Content"
#define XML_NODE_NAME_MSG_PARAM_TIME		"Time"
#define XML_NODE_NAME_MSG_PARAM_TYPE		"Type"
#define XML_NODE_NAME_MSG_PARAM_URL			"Url"
#define XML_NODE_NAME_MSG_PARAM_DURATION	"Duration"
#define XML_NODE_NAME_MSG_PARAM_COUNT		"Count"
#define XML_NODE_NAME_MSG_PARAM_ID			"ID"
#define XML_NODE_NAME_MSG_PARAM_ADDRESS		"Address"
#define XML_NODE_NAME_MSG_PARAM_DEVICETYPE	"DeviceType"
#define XML_NODE_NAME_MSG_PARAM_FORCE		"Force"
#define XML_NODE_NAME_MSG_PARAM_HEART_BEAT_PERIOD		"HeartBeatPeriod"
#define XML_NODE_NAME_MSG_PARAM_EXPIRE		"Expire"

#define XML_NODE_NAME_MSG_PARAM_TMPKEY		"TmpKey"
#define XML_NODE_NAME_MSG_PARAM_MSGSEQ		"MsgSeq"
#define XML_NODE_NAME_MSG_PARAM_SEQ			"Seq"
#define XML_NODE_NAME_MSG_PARAM_RELAY		"Relay"

#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_FILE		"File"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_SHOWTIME		"ShowTime"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_DURATION		"Duration"

#define XML_NODE_NAME_MSG_PARAM_USER		"User"
#define XML_NODE_NAME_MSG_PARAM_CODE		"Code"
#define XML_NODE_NAME_MSG_PARAM_BIND_CODE	"BindCode"
#define XML_NODE_NAME_MSG_PARAM_BIND_NUM	"BindNum"
#define XML_NODE_NAME_MSG_PARAM_BIND		"Bind"

#define XML_NODE_NAME_MSG_BIND_ITEM_BINDCODE	"BindCode"
#define XML_NODE_NAME_MSG_BIND_ITEM_STATUS		"Status"
#define XML_NODE_NAME_MSG_BIND_ITEM_TIME		"Time"
#define XML_NODE_NAME_MSG_BIND_ITEM_DEVICECODE	"DeviceCode"

#define XML_NODE_NAME_MSG_PARAM_ACTION			"Action"
#define XML_NODE_NAME_MSG_PARAM_GROUP0			"Group0"
#define XML_NODE_NAME_MSG_PARAM_GROUP1			"Group1"
#define XML_NODE_NAME_MSG_PARAM_GROUP2			"Group2"

#define XML_NODE_NAME_MSG_PARAM_PICNAME			"PicName"
#define XML_NODE_NAME_MSG_PARAM_INITIATOR		"Initiator"
#define XML_NODE_NAME_MSG_PARAM_RESPONSE		"Response"

#define XML_NODE_NAME_MSG_PARAM_SERVER_ADDR		"ServerAddr"
#define XML_NODE_NAME_MSG_PARAM_SERVER_URL		"ServerUrl"

#define XML_NODE_NAME_MSG_PARAM_REMOTE_SIP_USER	"RemoteSipUser"

#define XML_NODE_NAME_MSG_PARAM_DEVICE_CODE		"DeviceCode"
#define XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD	"DiscoverMethod"
#define XML_NODE_NAME_MSG_PARAM_USER_NAME	"Username"
#define XML_NODE_NAME_MSG_PARAM_PASSWORD	"Password"
#define XML_NODE_NAME_MSG_PARAM_FILE_NAME	"Filename"

#define XML_NODE_NAME_MSG_PARAM_DTMF		"DTMF"

#define XML_NODE_NAME_MSG_PARAM_COMMAND		"Command"
#define XML_NODE_NAME_MSG_PARAM_CONTENT		"Content"

#define XML_NODE_NAME_MSG_PARAM_CALLER		"Caller"
#define XML_NODE_NAME_MSG_PARAM_CALLEE		"Callee"
#define XML_NODE_NAME_MSG_PARAM_DAILOUT		"DailOut"

#define XML_NODE_NAME_MSG_PARAM_ALARM_MSG	"AlarmMsg"
#define XML_NODE_NAME_MSG_PARAM_APT			"APT"
#define XML_NODE_NAME_MSG_PARAM_NODES		"Nodes"
#define XML_NODE_NAME_MSG_PARAM_FACTORY		"Factory"
#define XML_NODE_NAME_MSG_SERVER_TYPE		"SrvType"
#define XML_NODE_NAME_MSG_FPMD5				"FPMD5"

#define XML_NODE_NAME_MSG_PARAM_SSRC		"SSRC"
#define XML_NODE_NAME_MSG_PARAM_ALARM_CH_EXCEPTION	"AlarmChException"
#define XML_NODE_NAME_MSG_PARAM_MON		"Mon"
#define XML_NODE_NAME_MSG_PARAM_GAS		"GAS"
#define XML_NODE_NAME_MSG_PARAM_ISTRIGGER	"IS_TRIGGER"
#define XML_NODE_NAME_MSG_PARAM_NAME		"Name"
#define XML_NODE_NAME_MSG_PARAM_CREATETIME	"CreateTime"
#define XML_NODE_NAME_MSG_PARAM_HOME		"Home"
#define XML_NODE_NAME_MSG_PARAM_SLEEP		"Sleep"
#define XML_NODE_NAME_MSG_PARAM_AWAY		"Away"
#define XML_NODE_NAME_MSG_PARAM_DESCRIPTION	"Description"
#define XML_NODE_NAME_MSG_PARAM_EMAIL		"Email"
#define XML_NODE_NAME_MSG_PARAM_ALARM_CODE		"AlarmCode"
#define XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION	"AlarmLocation"
#define XML_NODE_NAME_MSG_PARAM_ALARM_ZONE	"AlarmZone"
#define XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE	"AlarmCustomize"
#define XML_NODE_NAME_MSG_PARAM_VISITOR		"Visitor"
#define XML_NODE_NAME_MSG_PARAM_PHONE		"Phone"
#define XML_NODE_NAME_MSG_PARAM_ACCOUNT		"Account"
#define XML_NODE_NAME_MSG_PARAM_MULTIFLAG	"MultiFlag"
#define XML_NODE_NAME_MSG_PARAM_SIP			"Sip"
#define XML_NODE_NAME_MSG_PARAM_SYNC		"Sync"
#define XML_NODE_NAME_MSG_PARAM_MODEL_NAME	"ModelName"
#define XML_NODE_NAME_MSG_PARAM_ACCESS_KEYSECRET		"AccessKeySecret"
#define XML_NODE_NAME_MSG_PARAM_ACCESS_KEYID	"AccessKeyId"
#define XML_NODE_NAME_MSG_PARAM_SECURITY_TOKEN	"SecurityToken"
#define XML_NODE_NAME_MSG_PARAM_BUCKET		"Bucket"
#define XML_NODE_NAME_MSG_PARAM_ENDPOINT	"Endpoint"
#define XML_NODE_NAME_MSG_PARAM_TRACEID		"TraceID"
#define XML_NODE_NAME_MSG_PARAM_SSHPORT		"SSHPort"
#define XML_NODE_NAME_MSG_PARAM_UNITAPT		"UnitApt"

#define XML_NODE_VALUE_SIZE			1024
#define XML_NODE_TEXT_SIZE			1024
#define XML_NODE_LINE_SIZE			1024
#define XML_ADDR_FILE_SIZE			8192
#define UPDATE_CONFIG_FILE_TMP_PATH		"/tmp/updateConfig.cfg"

static char* xml_get_node_attribute(TiXmlElement* node, const char* attr_name)
{
#define NODE_VALUE_MAX_LEN		256
        static char ret[NODE_VALUE_MAX_LEN] = {0};

        memset(ret, 0, NODE_VALUE_MAX_LEN);

        if (NULL == node || NULL == attr_name)
        {
                return ret;
        }

        TiXmlAttribute* node_attr = node->FirstAttribute();

        for (; node_attr != NULL; node_attr = node_attr->Next())
        {
                if (!rl_strcmp(attr_name, node_attr->Name()))
                {
                        rl_strcpy_s(ret, sizeof(ret), node_attr->Value());
                        break;
                }
        }

        return ret;
}

CMsgHandle *GetMsgHandleInstance()
{
	return CMsgHandle::GetInstance();
}

CMsgHandle::CMsgHandle()
{
	
}

CMsgHandle::~CMsgHandle()
{
	
}

CMsgHandle *CMsgHandle::instance = NULL;

CMsgHandle *CMsgHandle::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CMsgHandle();
	}

	return instance;
}


/*
<Msg>
  <Type>Bootup</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <IP>*************</IP>
    <Port>8501</Port>
  </Params>
</Msg>
*/
int CMsgHandle::BuildBootupMsg(char *pszBuf, int nSize, SOCKET_MSG_BOOTUP *pBootupMsg, INT *pDataSize)
{
	if((pszBuf == NULL) || (pBootupMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_BOOTUP, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pBootupMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(pBootupMsg->szIPAddr).data(), XML_NODE_NAME_MSG_PARAM_IP);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PORT, pBootupMsg->nPort, XML_NODE_NAME_MSG_PARAM_PORT);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByDefault(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>GetBindCode</Type>
  <Protocal>1.0</Protocal>
  <Params>	
    <MsgSeq> 429496700</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildGetBindCodeRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, INT *pDataSize)
{
	if((pszBuf == NULL) || (pszSerialNum == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_GET_BIND_CODE, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(pszSerialNum).data(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>UnBindCode</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <BindCode>01010102Xxxxx</BindCode>	
    <MsgSeq>429496701</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildUnBindCodeRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, char *pszBindCode, INT *pDataSize)
{
	if((pszBuf == NULL) || (pszSerialNum == NULL) || (pszBindCode == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_UN_BIND_CODE, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_BIND_CODE, XmlTrans(pszBindCode).data(), XML_NODE_NAME_MSG_PARAM_BIND_CODE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(pszSerialNum).data(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
/*
<Msg>
  <Type>GetBindList</Type>
  <Protocal>1.0</Protocal>
  <Params>	
    <MsgSeq>429496702</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildGetBindCodeListRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, INT *pDataSize)
{
	if((pszBuf == NULL) || (pszSerialNum == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_GET_BIND_LIST, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, PROTOCAL_NAME_DEFAULT, XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(pszSerialNum).data(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#if RL_SUPPORT_CLOUD_DEV_INFO	
/*
<Msg>
  <Type>RequestDevList</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MsgSeq>429496702</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestDeviceListMsg(char *pszBuf, int nSize, SOCKET_MSG_REQUEST_DEVICE_LIST *pRequestDeviceListMsg, INT *pDataSize)
{
	if((pszBuf == NULL) || (pRequestDeviceListMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQ_DEVICE_LIST, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pRequestDeviceListMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pRequestDeviceListMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#endif

#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
/*
<Msg>
  <Type>ReportArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>INDOOR_*******.2-1</FromName>
      <ToName>AppUser</ToName>           //如果AppUser=空转发给所有同节点App
      <From>***********3</From>
      <To>***********4</To>
      <Mode>0</Mode>
      <Sync>1</Sync>
      <Action>1</Action>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportArmingMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_ARMING *pReportArmingMsg, INT *pDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pReportArmingMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_ARMING, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pReportArmingMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, XmlTrans(pReportArmingMsg->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, XmlTrans(pReportArmingMsg->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pReportArmingMsg->szFromIP).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pReportArmingMsg->szToIP).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MODE, pReportArmingMsg->nMode, XML_NODE_NAME_MSG_PARAM_MODE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SYNC, pReportArmingMsg->nSync, XML_NODE_NAME_MSG_PARAM_SYNC);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ACTION, pReportArmingMsg->nActionType, XML_NODE_NAME_MSG_PARAM_ACTION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMAC);
#endif
	return 0;
}
#endif

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
/*
<Msg>
  <Type>PushNoAnswerFwdNumber</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>INDOOR_*******.2-1</FromName>
	  <ToName>OUTDOOR_*******.2-2</ToName>
	  <Action>Set<Action>
      <Group0>220,221,222</Group0>
      <Group1>330,331,332</Group1>
      <Group2>440,441,442</Group2>
  </Params>
</Msg>
*/
int CMsgHandle::BuildPushForwardNumberMsg(char *pszBuf, int nSize, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumerMsg, INT *pDataSize)
{
	if((pszBuf == NULL) || (pPushForwardNumerMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_PUSH_NOANSWER_FORWARD_NUMBER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pPushForwardNumerMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, XmlTrans(pPushForwardNumerMsg->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, XmlTrans(pPushForwardNumerMsg->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP0, XmlTrans(pPushForwardNumerMsg->szGroup0).data(), XML_NODE_NAME_MSG_PARAM_GROUP0);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP1, XmlTrans(pPushForwardNumerMsg->szGroup1).data(), XML_NODE_NAME_MSG_PARAM_GROUP1);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP2, XmlTrans(pPushForwardNumerMsg->szGroup2).data(), XML_NODE_NAME_MSG_PARAM_GROUP2);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ACTION, XmlTrans(pPushForwardNumerMsg->szAction).data(), XML_NODE_NAME_MSG_PARAM_ACTION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
/*
<Msg>
  <Type>ReportNoAnswerFwdNumber</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <FromName>OUTDOOR_*******.2-1</FromName>
	<ToName>INDOOR_*******.2-2</ToName>
    <Group0>220,221,222</Group0>
	<Group1>330,331,332</Group1>
	<Group2>440,441,442</Group2>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportForwardNumberMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumber, INT *pDataSize)
{
	if((pszBuf == NULL) || (pReportForwardNumber == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_NOANSWER_FORWARD_NUMBER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pReportForwardNumber->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, XmlTrans(pReportForwardNumber->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, XmlTrans(pReportForwardNumber->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP0, XmlTrans(pReportForwardNumber->szGroup0).data(), XML_NODE_NAME_MSG_PARAM_GROUP0);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP1, XmlTrans(pReportForwardNumber->szGroup1).data(), XML_NODE_NAME_MSG_PARAM_GROUP1);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GROUP2, XmlTrans(pReportForwardNumber->szGroup2).data(), XML_NODE_NAME_MSG_PARAM_GROUP2);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif

	return 0;
}

#endif
/*
<Msg>

  <Type>Ack</Type>

  <Protocal>1.0</Protocal>

  <Params>

    <MsgID>000A</MsgID>

    <MsgCRC>3D102391A</MsgCRC>

    <Result>FAIL</Result><!-- OK or FAIL -->

    <Info>File is not exist.</Info>
    <MsgSeq></MsgSeq>

  </Params>

</Msg>

*/
int CMsgHandle::ParseACKMsg(char *pszBuf, SOCKET_MSG_ACK*pACKMsg, int nDataSize)
{
    if((pszBuf == NULL) || (pACKMsg == NULL))
    {
        return -1;
    }
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	BOOL bForceMac = FALSE;
	if(GetSettingHandleInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
	{
		bForceMac = TRUE;
	}
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, bForceMac);
#endif
    rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

    memset(pACKMsg, 0, sizeof(SOCKET_MSG_ACK));

    TiXmlDocument doc;
    if (!doc.LoadBuffer(pszBuf))
    {
        rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
        return -1;
    }
    TiXmlElement* pRootNode = doc.RootElement();
    TiXmlElement* pNode = NULL;
    if (NULL == pRootNode)
    {
        rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
    {
        rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
        return -1;
    }

    for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
    {
        if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            rl_strcpy_s(pACKMsg->szProtocal, sizeof(pACKMsg->szProtocal), pNode->GetText());
        }
        else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* pSubNode = NULL;
            for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
            {
                if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGID) == 0)
                {
                    rl_strcpy_s(pACKMsg->szMsgID, sizeof(pACKMsg->szMsgID), pSubNode->GetText());
                }
                else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGCRC) == 0)
                {
                    rl_strcpy_s(pACKMsg->szMsgCRC, sizeof(pACKMsg->szMsgCRC), pSubNode->GetText());
                }
                else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
                {
                    rl_strcpy_s(pACKMsg->szResult, sizeof(pACKMsg->szResult), pSubNode->GetText());
                }
                else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_INFO) == 0)
                {
                    rl_strcpy_s(pACKMsg->szInfo, sizeof(pACKMsg->szInfo), pSubNode->GetText());
                }
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
					pACKMsg->nSequenceNum = rl_atoi(pSubNode->GetText());
                }
            }
        }

    }

    return 0;
}


/*
<Msg>
	<Type>OwnerMessage</Type>
	<Protocal>1.0</Protocal>
	<Params>
		<Title>Test</Title>
		<Content>Testabc</Content>...
		<Time>2016-07-28 15:13:00</Time>
	</Params>
</Msg>
*/
int CMsgHandle::ParseOwnerMsg(char *pszBuf, SOCKET_MSG_OWNER_MESSAGE*pOwnerMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pOwnerMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pOwnerMsg, 0, sizeof(SOCKET_MSG_OWNER_MESSAGE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pOwnerMsg->szProtocal, sizeof(pOwnerMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TITLE) == 0)
				{
					rl_strcpy_s(pOwnerMsg->szTitle, sizeof(pOwnerMsg->szTitle), pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
				{
					rl_strcpy_s(pOwnerMsg->szContent, sizeof(pOwnerMsg->szContent), pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pOwnerMsg->szTime, sizeof(pOwnerMsg->szTime), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>TextMessage</Type>
  <Protocal>1.0</Protocal>
  <Params>
  	<MsgSeq>429496701</MsgSeq>
	<Title>Hello!</Title>
	<Content>Hello,Everyone!</Content>
	<FromName>INDOOR_*******.1-1</FromName>
	<ToName>INDOOR_*******.2-2</ToName>
	<From>***********3</From>
	<To>***********4</To>
	<Time>2016-08-26 10:00:00</Time>
	<MsgID></MsgID>
  </Params>
</Msg>
*/
int CMsgHandle::ParseTextMsg(char *pszBuf, SOCKET_MSG_TEXT_MESSAGE*pTextMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pTextMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	memset(pTextMsg, 0, sizeof(SOCKET_MSG_TEXT_MESSAGE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err(_T("%s:XML LoadBuffer failed."), __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err(_T("%s: ROOT Node is NULL"), __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err(_T("%s: mismatched %s"), __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for(pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pTextMsg->szProtocal, sizeof(pTextMsg->szProtocal)/sizeof(TCHAR), pNode->GetText());
		}
		else if (strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TITLE) == 0)
				{
					rl_strcpy_s(pTextMsg->szTitle, sizeof(pTextMsg->szTitle)/sizeof(TCHAR), pSubNode->GetText());
				}
				if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pTextMsg->nSequenceNum =  rl_atoi(pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
				{
					rl_strcpy_s(pTextMsg->szContent, sizeof(pTextMsg->szContent)/sizeof(TCHAR), pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
				{
					rl_strcpy_s(pTextMsg->szFrom, sizeof(pTextMsg->szFrom)/sizeof(TCHAR), pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
				{
					rl_strcpy_s(pTextMsg->szTo, sizeof(pTextMsg->szTo)/sizeof(TCHAR), pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pTextMsg->szTime, sizeof(pTextMsg->szTime)/sizeof(TCHAR), pSubNode->GetText());
				}
/*去掉From_name和to_name的解析
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pTextMsg->szFromName, sizeof(pTextMsg->szFromName)/sizeof(TCHAR), pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pTextMsg->szToName, sizeof(pTextMsg->szToName)/sizeof(TCHAR), pSubNode->GetText());
				}
*/
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pTextMsg->nSequenceNum = rl_atoi(pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGID) == 0)
				{
					pTextMsg->nMsgID = rl_atoi(pSubNode->GetText());
				}
				else if(strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MULTIFLAG) == 0)
				{
					pTextMsg->nMultiFlag= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}



/*
<Msg>
  <Type>RequestConnection</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <IP>**************:3021</IP>
    <Port>3021</Port>
	<Force>1</Force>
	<Factory>1</Factory>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReqConnMsg(char *pszBuf, SOCKET_MSG_REQ_CONN *pReqConnMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReqConnMsg == NULL))
	{
		return -1;
	}
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	memset(pReqConnMsg, 0, sizeof(SOCKET_MSG_REQ_CONN));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReqConnMsg->szProtocal, sizeof(pReqConnMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
				{
					rl_strcpy_s(pReqConnMsg->szIPAddr, sizeof(pReqConnMsg->szIPAddr), pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PORT) == 0)
				{
					pReqConnMsg->nPort = rl_atoi(pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FORCE) == 0)
				{
					pReqConnMsg->bForceConnect = rl_atoi(pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_HEART_BEAT_PERIOD) == 0)
				{
					pReqConnMsg->nHeartBeatPeriod= rl_atoi(pSubNode->GetText());
				}
				else if (rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FACTORY) == 0)
				{
					pReqConnMsg->nFactoryMode= rl_atoi(pSubNode->GetText());
				}
			}
		}

	}

	return 0;
}

/*
<Msg>
  <Type>ReportStatus</Type>
  <Protocal>1.0</Protocal>
</Msg>
*/
int CMsgHandle::ParseReqStatusMsg(char *pszBuf, SOCKET_MSG_REQ_STATUS *pReqStatusMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReqStatusMsg == NULL))
	{
		return -1;
	}
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	memset(pReqStatusMsg, 0, sizeof(SOCKET_MSG_REQ_STATUS));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReqStatusMsg->szProtocal, sizeof(pReqStatusMsg->szProtocal), pNode->GetText());
		}
		else if(rl_strcmp(pNode->Value(), "SrvName") == 0)
		{
			rl_strcpy_s(pReqStatusMsg->szSrvName, sizeof(pReqStatusMsg->szSrvName), pNode->GetText());
		}
	}
	return 0;
}

/*
<Msg>
  <Type>Reboot</Type>
  <Protocal>1.0</Protocal>
</Msg>

或

<Msg>
  <Type>OpenDoor</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Item>0</Item>
    <Item>1</Item>
  </Params>
</Msg>

或

<Msg>
  <Type>MakeCall</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <From>111</From><!-- local sip account -->
    <To>222</To><!-- Remote sip accout -->
    <Mode>Audio</Mode><!-- Audio or Video -->
  </Params>
</Msg>

*/
int CMsgHandle::ParseRemoteControlMsg(char *pszBuf, SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pRemoteControlMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pRemoteControlMsg, 0, sizeof(SOCKET_MSG_REMOTE_CONTROL));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			rl_strcpy_s(pRemoteControlMsg->szType, sizeof(pRemoteControlMsg->szType), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pRemoteControlMsg->szProtocal, sizeof(pRemoteControlMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			UINT nIndex = 0;
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(nIndex >= REMOTE_CONTROL_ITEM_NUM)
				{
					break;
				}

				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) == 0)
				{
					rl_strcpy_s(pRemoteControlMsg->szItem[nIndex], sizeof(pRemoteControlMsg->szItem[nIndex]), pSubNode->GetText());
					nIndex++;
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
				{
					rl_strcpy_s(pRemoteControlMsg->szFrom, sizeof(pRemoteControlMsg->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
				{
					rl_strcpy_s(pRemoteControlMsg->szTo, sizeof(pRemoteControlMsg->szTo), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MODE) == 0)
				{
					rl_strcpy_s(pRemoteControlMsg->szMode, sizeof(pRemoteControlMsg->szMode), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>Ack</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MsgID>000A</MsgID>
    <MsgCRC>3D102391A</MsgCRC>
    <Result>FAIL</Result><!-- OK or FAIL -->
    <Info>File is not exist.</Info>
    <MsgSeq></MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildACKMsg(char *pszBuf, int nSize, SOCKET_MSG_ACK *pAckMsg, int *pDataSize)
{
    if((pszBuf == NULL) || (pAckMsg == NULL))
    {
        return -1;
    }
    
    char szTmpLine[XML_NODE_LINE_SIZE] = {0};
    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_ACK, XML_NODE_NAME_MSG_TYPE);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pAckMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGID, XmlTrans(pAckMsg->szMsgID).data(), XML_NODE_NAME_MSG_PARAM_MSGID);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGCRC, XmlTrans(pAckMsg->szMsgCRC).data(), XML_NODE_NAME_MSG_PARAM_MSGCRC);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, XmlTrans(pAckMsg->szResult).data(), XML_NODE_NAME_MSG_PARAM_RESULT);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_INFO, XmlTrans(pAckMsg->szInfo).data(), XML_NODE_NAME_MSG_PARAM_INFO);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pAckMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	BOOL bForceMac = FALSE;
	if(GetSettingHandleInstance()->GetConnectMode() == DOORSETTING_CONNECT_SERVER_MODE_NONE)
	{
		bForceMac = TRUE;
	}
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMac);
#endif
    return 0;

}

/*
<Msg>
  <Params>
	<MsgID>0x000A<MsgID/>  <!-命令ID/-->
    	<Type>OpenDoor</Type>--这个标识和控制命令的一致
	<TraceID>0123233000</TraceID> 
	<Result>1</Result>//重启不用管这个字段
	<Info></Info>
  </Params>
</Msg>
*/
int CMsgHandle::BuildAKCSACKMsg(char *pszBuf, int nSize, SOCKET_MSG_AKCS_ACK *pAkcsAckMsg, int *pDataSize)
{
    if((pszBuf == NULL) || (pAkcsAckMsg == NULL))
    {
        return -1;
    }
    
    char szTmpLine[XML_NODE_LINE_SIZE] = {0};
    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%04X</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGID, pAkcsAckMsg->nMsgID, XML_NODE_NAME_MSG_PARAM_MSGID);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pAkcsAckMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TRACEID, XmlTrans(pAkcsAckMsg->szTraceID).data(), XML_NODE_NAME_MSG_PARAM_TRACEID);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, XmlTrans(pAkcsAckMsg->szResult).data(), XML_NODE_NAME_MSG_PARAM_RESULT);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_INFO, XmlTrans(pAkcsAckMsg->szInfo).data(), XML_NODE_NAME_MSG_PARAM_INFO);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
    rl_strcat_s(pszBuf, nSize, szTmpLine);

    rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	BOOL bForceMac = FALSE;
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMac);
#endif
    return 0;

}


/*
<Msg>
  <Type>CheckKey</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <DeviceID>010001000101</DeviceID>
    <KeyType>RF</KeyType>
    <KeyValue>F8128A1C</KeyValue>
  </Params>
</Msg>

KeyType:  RF or PRIVATE
KeyValue: value
*/
int CMsgHandle::BuildCheckKeyMsg(char *pszBuf, int nSize, SOCKET_MSG_CHECK_KEY *pCheckKeyMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pCheckKeyMsg == NULL))
	{
		return -1;
	}
    
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_CHECK_KEY, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pCheckKeyMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICEID, XmlTrans(pCheckKeyMsg->szDeviceID).data(), XML_NODE_NAME_MSG_PARAM_DEVICEID);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_KEYTYPE, XmlTrans(pCheckKeyMsg->szKeyType).data(), XML_NODE_NAME_MSG_PARAM_KEYTYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_KEYVALUE, XmlTrans(pCheckKeyMsg->szKeyValue).data(), XML_NODE_NAME_MSG_PARAM_KEYVALUE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;

}


/*
<Msg>
  <Type>ReportStatus</Type>
  <Protocal>1.0</Protocal>
  <ThirdParty>1</ThirdParty>	--0:AKuvox 1:第三方设备
  <Params>
    <DeviceID>*******.2</DeviceID>
	<Extension>1</Extension>
	<DownloadServer></DownloadServer>
	<UploadServer></UploadServer>
	<Type>1</Type>
    <IP>*************</IP>
	<SM>*************</SM>
	<GW>***********</GW>
	<DNS1>***********</DNS1>
	<DNS2>***********</DNS2>
    <MAC>0C1105000001</MAC>
    <Status>Idle</Status>
    <SWVer>**********</SWVer>
    <ModelName>R29</ModelName>
    <HWVer>********.0.0.0.0</HWVer>
	<PrikeyMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</PrikeyMD5>
	<RfidMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</RfidMD5>
	<AddrMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</AddrMD5>
	<ConfigMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28E</ConfigMD5>
	<ContactMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</ContactMD5>
	<FaceidMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</FaceidMD5>
	<DclientVer>1</DclientVer>
	<Arming>1</Arming>
	<FPMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</FPMD5>
	<TzMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</TzMD5>
	<TzDataMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</TzDataMD5>
	<FaceSyncMD5>BB80C9DC38FC4F1FF0DFB71A13CEFEFF</FaceSyncMD5>
	<RelayStatus>12</RelayStatus>//新增 代表relay12为开启 其它为关闭
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportStatusMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_STATUS *pReportStatusMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportStatusMsg == NULL) || (pDataSize == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_STATUS, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pReportStatusMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_strcat_s(pszBuf, nSize, "\t<ThirdParty>1</ThirdParty>\r\n");

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICEID, XmlTrans(pReportStatusMsg->szDeviceID).data(), XML_NODE_NAME_MSG_PARAM_DEVICEID);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_EXTENSION, XmlTrans(pReportStatusMsg->szExtension).data(), XML_NODE_NAME_MSG_PARAM_EXTENSION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER, XmlTrans(pReportStatusMsg->szDownloadServer).data(), XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_UPLOADSERVER, XmlTrans(pReportStatusMsg->szUploadServer).data(), XML_NODE_NAME_MSG_PARAM_UPLOADSERVER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pReportStatusMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(pReportStatusMsg->szIPAddr).data(), XML_NODE_NAME_MSG_PARAM_IP);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SUBNETMASK, XmlTrans(pReportStatusMsg->szSubnetMask).data(), XML_NODE_NAME_MSG_PARAM_SUBNETMASK);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GATEWAY, XmlTrans(pReportStatusMsg->szGateway).data(), XML_NODE_NAME_MSG_PARAM_GATEWAY);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PRIMARYDNS, XmlTrans(pReportStatusMsg->szPrimaryDNS).data(), XML_NODE_NAME_MSG_PARAM_PRIMARYDNS);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SECONDARYDNS, XmlTrans(pReportStatusMsg->szSecondaryDNS).data(), XML_NODE_NAME_MSG_PARAM_SECONDARYDNS);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, XmlTrans(pReportStatusMsg->szMAC).data(), XML_NODE_NAME_MSG_PARAM_MAC);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_STATUS, XmlTrans(pReportStatusMsg->szStatus).data(), XML_NODE_NAME_MSG_PARAM_STATUS);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SWVER, XmlTrans(pReportStatusMsg->szSWVer).data(), XML_NODE_NAME_MSG_PARAM_SWVER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "ModelName", XmlTrans(pReportStatusMsg->szModelName).data(), "ModelName");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_HWVER, XmlTrans(pReportStatusMsg->szHWVer).data(), XML_NODE_NAME_MSG_PARAM_HWVER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PRIKEYMD5, XmlTrans(pReportStatusMsg->szPrivatekeyMD5).data(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RFIDMD5, XmlTrans(pReportStatusMsg->szRfidMD5).data(), XML_NODE_NAME_MSG_PARAM_RFIDMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ADDRMD5, XmlTrans(pReportStatusMsg->szAddressMD5).data(), XML_NODE_NAME_MSG_PARAM_ADDRMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONFIGMD5, XmlTrans(pReportStatusMsg->szConfigMD5).data(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ADMODULEMD5, XmlTrans(pReportStatusMsg->szAdModuleMD5).data(), XML_NODE_NAME_MSG_PARAM_ADMODULEMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKMD5, XmlTrans(pReportStatusMsg->szCommunityPhonebookMD5).data(), XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTACTMD5, XmlTrans(pReportStatusMsg->szContactMD5).data(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION, pReportStatusMsg->nDclientVer, XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FACEIDMD5, XmlTrans(pReportStatusMsg->szFaceidMD5).data(), XML_NODE_NAME_MSG_PARAM_FACEIDMD5);	
	rl_strcat_s(pszBuf, nSize, szTmpLine);
#endif

#if RL_SUPPORT_AUTH_CODE
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_AUTHCODE, XmlTrans(pReportStatusMsg->szAuthCode).data(), XML_NODE_NAME_MSG_PARAM_AUTHCODE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
#endif

#if RL_SUPPORT_ARMING
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ARMING, pReportStatusMsg->nArming, XML_NODE_NAME_MSG_PARAM_ARMING);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
#endif
#if RL_SUPPORT_FP_DOWNLOAD
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FPMD5, pReportStatusMsg->szFPMD5, XML_NODE_NAME_MSG_FPMD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
#endif
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "TzMD5", pReportStatusMsg->szTzMD5, "TzMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "TzDataMD5", pReportStatusMsg->szTzDataMD5, "TzDataMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "FaceSyncMD5", pReportStatusMsg->szFaceSyncMD5, "FaceSyncMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "ACMetaMD5", pReportStatusMsg->szACMetaMD5, "ACMetaMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "ACInfoMD5", pReportStatusMsg->szACInfoMD5, "ACInfoMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "ScheduleMD5", pReportStatusMsg->szScheduleMD5, "ScheduleMD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", "RelayStatus", pReportStatusMsg->szRelayStatus, "RelayStatus");
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	//rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

	LogSplit(pszBuf, rl_strlen(pszBuf)+1, __FUNCTION__);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByDefault(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>ReportConfig</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Item>Config.Account1.GENERAL.Enable</Item>
	<Item>Config.Account1.GENERAL.Label</Item>
	...
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportConfigMsg(char *pszBuf, int nSize, SOCKET_MSG_CONFIG *pReportConfigMsg, int *pDataSize, BOOL bForceMac)
{
	if((pszBuf == NULL) || (pReportConfigMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_CONFIG, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pReportConfigMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	for(int i=0; i<CONFIG_MODULE_ITEM_NUM; i++)
	{
		if(rl_str_isempty(pReportConfigMsg->module.szItem[i]))
		{
			break;
		}
		rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, XmlTrans(pReportConfigMsg->module.szItem[i]).data(), XML_NODE_NAME_MSG_PARAM_ITEM);
		rl_strcat_s(pszBuf, nSize, szTmpLine);
	}

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMac);
#endif
	return 0;
}

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgHandle::BuildReportConfigMsg(char *pszBuf, int nSize, SOCKET_MSG_CONFIG_FROM_DEVICE* pReportConfigMsg, int *pDataSize, BOOL bForceMac)
{
	if((pszBuf == NULL) || (pReportConfigMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_CONFIG, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	for(int i=0; i<CONFIG_MODULE_FROM_DEVICE_ITEM_NUM; i++)
	{
		if(rl_str_isempty(pReportConfigMsg->module.szItem[i]))
		{
			break;
		}
		rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, XmlTrans(pReportConfigMsg->module.szItem[i]).data(), XML_NODE_NAME_MSG_PARAM_ITEM);
		rl_strcat_s(pszBuf, nSize, szTmpLine);
	}

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMac);
#endif
	return 0;
}
#endif


/*
<Msg>
  <Type>Alarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
  	<MsgSeq>1234444</MsgSeq>
	<ID>74019876</ID>
	<Type>Normal</Type>
	<FromName>INDOOR_*******.2-1</FromName>
	<ToName>MANAGEMENT_1.1.1-1</ToName>
	<From>***********4</From>
	<To>***********5</To>
	<Time>2016-08-26 10:00:00</Time>
	<AlarmCode>1</AlarmCode>//(区别于Type，是alarm的msg)-------新增alarm的类型 1=门口机未关门，后期多语言会把室内机的各种alarm类型加入
  </Params>
</Msg>
*/
int CMsgHandle::BuildAlarmMsg(char *pszBuf, int nSize, SOCKET_MSG_ALARM *pAlarmMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pAlarmMsg == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_ALARM, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pAlarmMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pAlarmMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%.8d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ID, pAlarmMsg->id, XML_NODE_NAME_MSG_PARAM_ID);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pAlarmMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

/*
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, pAlarmMsg->szFromName, XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, pAlarmMsg->szToName, XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
*/
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pAlarmMsg->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pAlarmMsg->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(pAlarmMsg->szTime).data(), XML_NODE_NAME_MSG_PARAM_TIME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE, pAlarmMsg->unAlarmCustomize, XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_CODE, pAlarmMsg->nAlarmCode, XML_NODE_NAME_MSG_PARAM_ALARM_CODE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION, pAlarmMsg->unAlarmLocation, XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_ZONE, pAlarmMsg->unAlarmZone, XML_NODE_NAME_MSG_PARAM_ALARM_ZONE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}

/*
<Msg>
  <Type>DealAlarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID> (alarmid， 11位数字，在rest api中的处理消息： PUT /alarm/xx)
     <User>XXX</User>        (告警的处理人，室内机可以先填写本身的地址节点，eg:*******.1-2)
     <Result>XXX</Result>    (告警的处理内容)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
  </Params>
</Msg>
*/
int CMsgHandle::BuildAlarmDealMsg(char *pszBuf, int nSize, SOCKET_MSG_ALARM_DEAL *pAlarmDealMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pAlarmDealMsg == NULL))
	{
		return -1;
	}
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DEAL_ALARM, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pAlarmDealMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ID, pAlarmDealMsg->id, XML_NODE_NAME_MSG_PARAM_ID);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_USER, XmlTrans(pAlarmDealMsg->szUser).data(), XML_NODE_NAME_MSG_PARAM_USER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESULT, XmlTrans(pAlarmDealMsg->szResult).data(), XML_NODE_NAME_MSG_PARAM_RESULT);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pAlarmDealMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(pAlarmDealMsg->szTime).data(), XML_NODE_NAME_MSG_PARAM_TIME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}
#if RL_SUPPORT_ARMING_P2P
/*
<Msg>
  <Type>RequestArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>AppUser</FromName>
      <ToName> INDOOR_*******.2-1</ToName>
      <From>***********4</From>
      <To>***********5</To>
      <Action>Get<Action>
      <Mode>0</Mode>
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestArming(char *pszBuf, int nSize, SOCKET_MSG_REQUEST_ARMING *pRequestArming, int *pDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pRequestArming == NULL))
	{
		return -1;
	}

	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQUEST_ARMING, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pRequestArming->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, XmlTrans(pRequestArming->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, XmlTrans(pRequestArming->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pRequestArming->szFromIP).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pRequestArming->szToIP).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ACTION, XmlTrans(pRequestArming->szAction).data(), XML_NODE_NAME_MSG_PARAM_ACTION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MODE, pRequestArming->nMode, XML_NODE_NAME_MSG_PARAM_MODE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);


	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, bForceMAC);
#endif
	return 0;
}
#endif

/*
<Msg>
  <Type>TextMessage</Type>
  <Protocal>1.0</Protocal>
  <Params>
  	<MsgSeq>429496701</MsgSeq>
	<Title>Hello!</Title>
	<Content>Hello,Everyone!</Content>
	<FromName>INDOOR_*******.1-1</FromName>
	<ToName>INDOOR_*******.2-2</ToName>
	<From>***********3</From>
	<To>***********4</To>
	<Time>2016-08-26 10:00:00</Time>	
  </Params>
</Msg>
*/

int CMsgHandle::BuildTextMsg(char *pszBuf, int nSize, SOCKET_MSG_TEXT_MESSAGE *pTextMessage, int *pDataSize)
{
	if((pszBuf == NULL) || (pTextMessage == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_TEXT_MESSAGE, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pTextMessage->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pTextMessage->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, XmlTrans(pTextMessage->szType).data(), XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TITLE, XmlTrans(pTextMessage->szTitle).data(), XML_NODE_NAME_MSG_PARAM_TITLE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTENT, XmlTrans(pTextMessage->szContent).data(), XML_NODE_NAME_MSG_PARAM_CONTENT);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

//from_name和to_name去掉，无需发送
/*
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM_NAME, pTextMessage->szFromName, XML_NODE_NAME_MSG_PARAM_FROM_NAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
	
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO_NAME, pTextMessage->szToName, XML_NODE_NAME_MSG_PARAM_TO_NAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
*/
	
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pTextMessage->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pTextMessage->szTo).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(pTextMessage->szTime).data(), XML_NODE_NAME_MSG_PARAM_TIME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}

/*
<Msg>
  <Type>AccessInfo</Type>
  <Protocal>1.0</Protocal>
  <Params>
  	<code>********</code>
  </Params>
</Msg>
*/

int CMsgHandle::BuildAccessInfo(char *pszBuf, int nSize, SOCKET_MSG_ACCESS_INFO *pAccessInfo, int *pDataSize)
{
	if((pszBuf == NULL) || (pAccessInfo == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_ACCESS_INFO, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pAccessInfo->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CODE, XmlTrans(pAccessInfo->szCode).data(), XML_NODE_NAME_MSG_PARAM_CODE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#if RL_GLOBAL_SUPPORT_TMP_KEY
/*
<Msg>
  <Type>CheckTmpKey</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <TmpKey>01010102</TmpKey> 	
    <MsgSeq> 429496795</MsgSeq>
  </Params>
</Msg>
*/

int CMsgHandle::BuildCheckTmpKey(char *pszBuf, int nSize, SOCKET_MSG_CHECK_TMP_KEY *pCheckTmpKey, int *pDataSize)
{
	if((pszBuf == NULL) || (pCheckTmpKey == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_CHECK_TMP_KEY, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pCheckTmpKey->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TMPKEY, XmlTrans(pCheckTmpKey->szCheckTmpKeyCode).data(), XML_NODE_NAME_MSG_PARAM_TMPKEY);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, XmlTrans(pCheckTmpKey->szMsgSeqCode).data(), XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#endif

/*	
<Msg>
.	<Type>Discover</Type>
.<Protocal>1.0</Protocal>
.<Params>
..	<Type>Indoor</Type>
.	<DeviceID>*******.1</ DeviceID>
..	<Extension></Extension >
	<Flag></Flag>
	<MsgSeq></MsgSeq>
	<DiscoveMethod>3</DiscoveMethod>
.</Params>
</Msg>
*/
int CMsgHandle::BuildDiscoverMsg(char *pszBuf, int nSize, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pDiscoverMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DISCOVER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pDiscoverMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pDiscoverMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICEID, XmlTrans(pDiscoverMsg->szDeviceID).data(), XML_NODE_NAME_MSG_PARAM_DEVICEID);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_EXTENSION, XmlTrans(pDiscoverMsg->szExtension).data(), XML_NODE_NAME_MSG_PARAM_EXTENSION);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FLAG, XmlTrans(pDiscoverMsg->szFlag).data(), XML_NODE_NAME_MSG_PARAM_FLAG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pDiscoverMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD, XmlTrans(pDiscoverMsg->szDiscoverMethod).data(), XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	//rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}


/*	
<Msg>
.	<Type>DiscoverAck</Type>
.	<Protocal>1.0</Protocal>
.	<Params>
.		<Type>Indoor</Type>
.		<DeviceID>*******.1</ DeviceID>
.		<Extension>1</Extension >
 		<IP>***********3</IP>
 		<FW>***********</FW>
 		<DeviceName></DeviceName>		
 		<RTSP>rtsp://*************/live/ch00_0</RTSP>
 		<Flag>0</Flag>
		<MAC>0c1105000001</MAC>
		<Model>C313</Model>
.	</Params>
</Msg>

*/
int CMsgHandle::BuildDiscoverAckMsg(char *pszBuf, int nSize, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pDiscoverAckMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_DISCOVER_ACK, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pDiscoverAckMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pDiscoverAckMsg->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICEID, XmlTrans(pDiscoverAckMsg->szDeviceID).data(), XML_NODE_NAME_MSG_PARAM_DEVICEID);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_EXTENSION, XmlTrans(pDiscoverAckMsg->szExtension).data(), XML_NODE_NAME_MSG_PARAM_EXTENSION);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(pDiscoverAckMsg->szIPAddr).data(), XML_NODE_NAME_MSG_PARAM_IP);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FW, XmlTrans(pDiscoverAckMsg->szSWVer).data(), XML_NODE_NAME_MSG_PARAM_FW);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FLAG, XmlTrans(pDiscoverAckMsg->szFlag).data(), XML_NODE_NAME_MSG_PARAM_FLAG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICE_NAME, XmlTrans(pDiscoverAckMsg->szDeviceName).data(), XML_NODE_NAME_MSG_PARAM_DEVICE_NAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RTSP_URL, XmlTrans(pDiscoverAckMsg->szRTSP).data(), XML_NODE_NAME_MSG_PARAM_RTSP_URL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pDiscoverAckMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DEVICE_CODE, XmlTrans(pDiscoverAckMsg->szDeviceCode).data(), XML_NODE_NAME_MSG_PARAM_DEVICE_CODE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD, XmlTrans(pDiscoverAckMsg->szDiscoverMethod).data(), XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, XmlTrans(pDiscoverAckMsg->szMac).data(), XML_NODE_NAME_MSG_PARAM_MAC);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Model", XmlTrans(pDiscoverAckMsg->szModel).data(), "Model");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	//rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}
#if RL_SUPPORT_REPORT_ACTIVITY
int CMsgHandle::BuildMotionAlertMsg(char *pszBuf, int nSize, SOCKET_MSG_MOTION_ALERT *pMotionAlertMsg, INT *pDataSize)
{
	if((pszBuf == NULL) || (pMotionAlertMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_MOTION_ALERT, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pMotionAlertMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PICNAME, XmlTrans(pMotionAlertMsg->szPicName).data(), XML_NODE_NAME_MSG_PARAM_PICNAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif

	return 0;
}

/*
<Msg>

<Type>ReportActivity</Type>

<Protocal>2.0</Protocal>

<Params>

<Type>5</Type>

<PicName>0A0203200117-1616054761_0_DoorDev_4a2c83e2af7e5d4ea8e469328b2db0d9.jpg</PicName>

<Initiator>6504100200</Initiator>

<Response>0</Response>

<PerId>xxx</PerId>

<DepartMode>0</DepartMode>

</Params>

</Msg>
*/

int CMsgHandle::BuildReportActivityMsg(char *pszBuf, int nSize, DCLIENT_REPORT_ACTIVITY *pReportActivityMsg, INT *pDataSize)
{
	if((pszBuf == NULL) || (pReportActivityMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_ACTIVITY, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, pReportActivityMsg->nType, XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Seq", pReportActivityMsg->nSeq, "Seq");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PICNAME, XmlTrans(pReportActivityMsg->szPicName).data(), XML_NODE_NAME_MSG_PARAM_PICNAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_INITIATOR, XmlTrans(pReportActivityMsg->szInitiator).data(), XML_NODE_NAME_MSG_PARAM_INITIATOR);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RESPONSE, pReportActivityMsg->nResponse, XML_NODE_NAME_MSG_PARAM_RESPONSE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "PerID", pReportActivityMsg->szPerID, "PerID");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Time", pReportActivityMsg->nTimestamp, "Time");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif

	return 0;
}
#endif

/*
<Msg>
  <Params>
      <RemoteSipUser>sip账号</RemoteSipUser>--发出dtmf键的sip账号
     <MsgSeq>这次校验的随机码</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::BuildCheckDtmfMsg(char *pszBuf, int nSize, SOCKET_MSG_CHECK_DTMF*pCheckDtmfMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pCheckDtmfMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "CheckDTMF", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pCheckDtmfMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_REMOTE_SIP_USER, XmlTrans(pCheckDtmfMsg->szRemoteSip).data(), XML_NODE_NAME_MSG_PARAM_REMOTE_SIP_USER);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pCheckDtmfMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

#if RL_SUPPORT_DTMF_SET
/*
<Msg>
  <Type>SendDtmfSet</Type>
  <Protocal>1.0</Protocal>
  <From>*******.1</From>
  <To>*******.1</To>
  <Params>	
    <MsgSeq> 429496700</MsgSeq>
    <DTMF>1234</DTMF>
  </Params>
</Msg>
*/
int CMsgHandle::BuildSendDTMFSetMsg(char *pszBuf, int nSize, SOCKET_MSG_SEND_DTMF_SET*pSendDtmfSetMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pSendDtmfSetMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_SEND_DTMF_SET, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(pSendDtmfSetMsg->szProtocal).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM, XmlTrans(pSendDtmfSetMsg->szFrom).data(), XML_NODE_NAME_MSG_FROM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TO,XmlTrans(pSendDtmfSetMsg->szTo).data(), XML_NODE_NAME_MSG_TO);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DTMF, XmlTrans(pSendDtmfSetMsg->szDTMF).data(), XML_NODE_NAME_MSG_PARAM_DTMF);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%.10d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGSEQ, pSendDtmfSetMsg->nSequenceNum, XML_NODE_NAME_MSG_PARAM_MSGSEQ);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}
#endif

#if RL_SUPPORT_DEVICE_MAINTENANCE
/*
Seq:1/0
Content:
xxxx
*/
int CMsgHandle::BuildCliCommandRespMsg(char *pszBuf, int nSize, SOCKET_MSG_CLI_COMMAND_RESP *pSocketCliCommandRespMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pSocketCliCommandRespMsg == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE*2];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE*2, "%s:%s\r\n", XML_NODE_NAME_MSG_PARAM_SEQ, pSocketCliCommandRespMsg->szSeq);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE*2,"%s:\r\n%s\r\n", XML_NODE_NAME_MSG_PARAM_CONTENT, pSocketCliCommandRespMsg->szRespBuf);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
	
	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	//rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#endif

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
/*
<Msg>
  <Type>RequestConfig</Type>
  <FromIp>********</FromIp>
  <ToIp></ToIp>
  <Params>
    <Item>Config.Account1.GENERAL.Enable</Item>
    <Item>Config.Account1.GENERAL.Label</Item>
	...
  </Params>
</Msg>
*/

int CMsgHandle::BuildRequestCfgFromDeviceMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_CONFIG *requestCfgMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (requestCfgMsg == NULL))
	{
		return -1;
	}
	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQ_CONFIG, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_FROM, XmlTrans(requestCfgMsg->szFromIP).data(), XML_NODE_NAME_MSG_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TO, XmlTrans(requestCfgMsg->szToIP).data(), XML_NODE_NAME_MSG_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	for(int i=0; i<DCLIENT_CONFIG_MODULE_ITEM_NUM; i++)
	{
		if(rl_str_isempty(requestCfgMsg->module.szItem[i]))
		{
			break;
		}
		rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, XmlTrans(requestCfgMsg->module.szItem[i]).data(), XML_NODE_NAME_MSG_PARAM_ITEM);
		rl_strcat_s(pszBuf, nSize, szTmpLine);
	}

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
}
#endif


/*
<Msg>
  <Params>
     <PicName>0C110000000-10256546_1_CALL.jpg</PicName>(长度64) (图片名称统一用，eg:0C1100000001-1513232303_0_CALL.jpg)
     <Caller>712012511</Caller>(长度32)//主叫 呼出=自己的sip 呼入=对方sip 
     <Callee>712012000</Callee>(长度32)//被叫 呼出=对方sip(是自己真实呼出号码，不管Remote-party-id) 呼入=自己sip
     <DailOut>1</DailOut>//呼出=1  呼入=0
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportCallCaptureMsg(char *pszBuf, int nSize, DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportCallCapture == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PICNAME, XmlTrans(pReportCallCapture->szPicName).data(), XML_NODE_NAME_MSG_PARAM_PICNAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CALLER, XmlTrans(pReportCallCapture->szCaller).data(), XML_NODE_NAME_MSG_PARAM_CALLER);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CALLEE, XmlTrans(pReportCallCapture->szCallee).data(), XML_NODE_NAME_MSG_PARAM_CALLEE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DAILOUT, pReportCallCapture->nCallType, XML_NODE_NAME_MSG_PARAM_DAILOUT);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}


/*
<Msg>
	  <Type>ReportTrigger</Type>
	  <Protocal>2.0</Protocal>
	  <Params>
	    <Type>Wind</Type>
	  </Params>
</Msg>
*/
int CMsgHandle::BuildReportTriggerMsg(char *pszBuf, int nSize, DCLIENT_REPORT_TRIGGER *pReportTrigger, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportTrigger == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_TRIGGER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(PROTOCAL_NAME_DEFAULT).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, XmlTrans(pReportTrigger->szType).data(), XML_NODE_NAME_MSG_PARAM_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>BroadCastMsg</Type>
  <Params>
	<Title>通知</Title>
	<Content>明天不用上班</Content>
	<Time>2019-03-12 12:12:12</Time>
	<Nodes>301000121;3011021541;3011021542</Nodes>//发送给家庭列表，标识从联系人Group标签 Node字段取
  </Params>
</Msg>
*/
int CMsgHandle::BuildManageBroadcastMsg(char *pszBuf, int nSize, DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pManageBroadcastMsg == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_BROADCAST_MSG, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TITLE, XmlTrans(pManageBroadcastMsg->szTitle).data(), XML_NODE_NAME_MSG_PARAM_TITLE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTENT, XmlTrans(pManageBroadcastMsg->szContent).data(), XML_NODE_NAME_MSG_PARAM_CONTENT);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(pManageBroadcastMsg->szTime).data(), XML_NODE_NAME_MSG_PARAM_TIME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_NODES, XmlTrans(pManageBroadcastMsg->szNodes).data(), XML_NODE_NAME_MSG_PARAM_NODES);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>ReportHealthMsg</Type>
  <Params>
	<Status>************5</Status>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportHealthMsg(char *pszBuf, int nSize, DCLIENT_REPORT_HEALTH *pReportHealth, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportHealth == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_HEALTH, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_STATUS, pReportHealth->nStatus, XML_NODE_NAME_MSG_PARAM_STATUS);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>ResponseSensorTrigger</Type>
  <Params>
	<Home>0</Home>
	<Sleep>0</Sleep>
	<Away>1</Away>
  </Params>
</Msg>
*/

int CMsgHandle::BuildResponseSensorTriggerMsg(char *pszBuf, int nSize, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pSensorTriggerMsg == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_RESPONSE_SENSOR_TRIGGER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_HOME, pSensorTriggerMsg->nHomeTrigger, XML_NODE_NAME_MSG_PARAM_HOME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SLEEP, pSensorTriggerMsg->nSleepTrigger, XML_NODE_NAME_MSG_PARAM_SLEEP);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_AWAY, pSensorTriggerMsg->nAwayTrigger, XML_NODE_NAME_MSG_PARAM_AWAY);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;

}

/*
<Msg>
  <Type>UploadVideoNotify</Type>
  <Protocal>2.0</Protocal>
  <Params>
    <Name>xxxxx</Name>
    <CreateTime>2019-08-13 15:22:52</CreateTime>
    <Duration>22:15</Duration>
    <MD5>XXXXXXX</MD5>
    <RemoteMAC></RemoteMAC>
    <RemoteIP></RemoteIP>
  </Params>
</Msg>
*/
int CMsgHandle::BuildUploadVideoNotifyMsg(char *pszBuf, int nSize, DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pUploadVideoNotifyMsg == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_UPLOAD_VIDEO_NOTIFY, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(PROTOCAL_NAME_DEFAULT).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_NAME, XmlTrans(pUploadVideoNotifyMsg->szName).data(), XML_NODE_NAME_MSG_PARAM_NAME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CREATETIME, XmlTrans(pUploadVideoNotifyMsg->szCreateTime).data(), XML_NODE_NAME_MSG_PARAM_CREATETIME);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_DURATION, XmlTrans(pUploadVideoNotifyMsg->szVideoDuration).data(), XML_NODE_NAME_MSG_PARAM_DURATION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MD5, XmlTrans(pUploadVideoNotifyMsg->szMD5).data(), XML_NODE_NAME_MSG_PARAM_MD5);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "RemoteMAC", XmlTrans(pUploadVideoNotifyMsg->szRemoteMAC).data(), "RemoteMAC");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "RemoteIP", XmlTrans(pUploadVideoNotifyMsg->szRemoteIP).data(), "RemoteIP");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;

}

/*
<msg>
 <Type>UploadCaptureNotify</Type>
 <Protocal>2.0</Protocal>
 <Params>
  <PicName></PicName>
  <CreateTime>2019-08-13 15:22:52</CreateTime>
  <MD5>XXXXXXX</MD5>
  <RemoteMAC></RemoteMAC>
  <RemoteIP></RemoteIP>
 </Params>
</msg>
*/
int CMsgHandle::BuildUploadCaptureNotifyMsg(char *pszBuf, int nSize, DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg, int *pDataSize)
{
	if((pszBuf == NULL) || (pUploadCaptureNotifyMsg == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "UploadCaptureNotify", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(PROTOCAL_NAME_DEFAULT).data(), XML_NODE_NAME_MSG_PROTOCAL);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "PicName", XmlTrans(pUploadCaptureNotifyMsg->szName).data(), "PicName");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "CreateTime", XmlTrans(pUploadCaptureNotifyMsg->szCreateTime).data(), "CreateTime");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "MD5", XmlTrans(pUploadCaptureNotifyMsg->szMD5).data(), "MD5");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "RemoteMAC", XmlTrans(pUploadCaptureNotifyMsg->szRemoteMAC).data(), "RemoteMAC");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", "RemoteIP", XmlTrans(pUploadCaptureNotifyMsg->szRemoteIP).data(), "RemoteIP");
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;

}


/*
<Msg>
  <Type>RequestAllTriggerMsg</Type>
  <Params>
	<From>************4</From>
	<To>************5</To>
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestAllTriggerMsg(char *pszBuf, int nSize, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger, int *pDataSize)
{
	if((pszBuf == NULL) || (pRequestAllTrigger == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REQUEST_ALL_TRIGGER, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pRequestAllTrigger->szFromIP).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pRequestAllTrigger->szToIP).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}


/*
<Msg>
  <Type>ReportAllTriggerMsg</Type>
  <Params>
  	<AlarmChException>000011111111000010101010</AlarmChException>
	<From>************4</From>
	<To>************5</To>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportAllTriggerMsg(char *pszBuf, int nSize, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger, int *pDataSize)
{
	if((pszBuf == NULL) || (pRequestAllTrigger == NULL))
	{
		return -1;
	}	
	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportAllTriggerMsg", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, szTmpLine);
	
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	CHAR szAlarmChException[ARMING_ZOOM_NUM_MAX*3+1] = {0};
	for(int i=0; i<ARMING_ZOOM_NUM_MAX*3; i++)
	{
		szAlarmChException[i] = pRequestAllTrigger->bAlarmChException[i] ? '1' : '0';
	}
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ALARM_CH_EXCEPTION, XmlTrans(szAlarmChException).data(), XML_NODE_NAME_MSG_PARAM_ALARM_CH_EXCEPTION);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pRequestAllTrigger->szFromIP).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(pRequestAllTrigger->szToIP).data(), XML_NODE_NAME_MSG_PARAM_TO);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	*pDataSize = rl_strlen(pszBuf);

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}

#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
/*
<Msg>
  <Type>ReportDoorStatus</Type>
  <Params>
	<IP>*************</IP>
	<Status>0</Status>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportDoorStatusMsg(char *pszBuf, int nSize, DCLIENT_REPORT_DOORSTATUS *pDoorStatus, int *pDataSize)
{
	if((pszBuf == NULL) || (pDoorStatus == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_DOORSTATUS, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);


	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_IP, XmlTrans(pDoorStatus->szIPAddr).data(), XML_NODE_NAME_MSG_PARAM_IP);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_STATUS, pDoorStatus->nDoorState, XML_NODE_NAME_MSG_PARAM_STATUS);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
}
#endif

#if RL_SUPPORT_SEND_REPORT_GAS
/*
<Msg>
  <Type>ReportGAS</Type>
  <Params>
	<Mon>2019-06</Mon>
	<GAS>0</GAS>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportGasMsg(char *pszBuf, int nSize, DCLIENT_REPORT_GAS *pReportGas, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportGas == NULL))
	{
		return -1;
	}
	
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];

	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_GAS, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);


	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MON, XmlTrans(pReportGas->szMonth).data(), XML_NODE_NAME_MSG_PARAM_MON);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_GAS, pReportGas->nGasCount, XML_NODE_NAME_MSG_PARAM_GAS);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}
#endif

/*
<Msg>
  <Type>ReportVisitorInfo</Type>
  <Params>
    <Visitor>名字</Visitor>
    <Account>用户账户号</Account>
    <Email><EMAIL></Emali>
    <Phone>********</Phone>
    <Count>5</Count>    
    <From>China</From>
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportVisitorInfoMsg(char *pszBuf, int nSize, DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportVisitorInfo == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_VISITOR_INFO, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_VISITOR, XmlTrans(pReportVisitorInfo->szVisitor).data(), XML_NODE_NAME_MSG_PARAM_VISITOR);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ACCOUNT, XmlTrans(pReportVisitorInfo->szAccount).data(), XML_NODE_NAME_MSG_PARAM_ACCOUNT);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_EMAIL, XmlTrans(pReportVisitorInfo->szEmail).data(), XML_NODE_NAME_MSG_PARAM_EMAIL);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PHONE, XmlTrans(pReportVisitorInfo->szPhone).data(), XML_NODE_NAME_MSG_PARAM_PHONE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ID, XmlTrans(pReportVisitorInfo->szID).data(), XML_NODE_NAME_MSG_PARAM_ID);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_COUNT, pReportVisitorInfo->nCount, XML_NODE_NAME_MSG_PARAM_COUNT);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(pReportVisitorInfo->szFrom).data(), XML_NODE_NAME_MSG_PARAM_FROM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MODEL_NAME, XmlTrans(pReportVisitorInfo->szModelName).data(), XML_NODE_NAME_MSG_PARAM_MODEL_NAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>ReportVisitorAuth</Type>
  <Params>
    <SipAccount>*********</SipAccount> //通话对端的Sip账号	
    <Count>5</Count> //授权TempKey可用次数
  </Params>
</Msg>
*/
int CMsgHandle::BuildReportVisitorAuthInfoMsg(char *pszBuf, int nSize, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo, int *pDataSize)
{
	if((pszBuf == NULL) || (pReportVisitorAuthInfo == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_REPORT_VISITOR_AUTH, XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_SIP, XmlTrans(pReportVisitorAuthInfo->szSipAccout).data(), XML_NODE_NAME_MSG_PARAM_SIP);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_COUNT, pReportVisitorAuthInfo->nCount, XML_NODE_NAME_MSG_PARAM_COUNT);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByDefault(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>RequestOssSts</Type>
  <Params>
    <Name>FaceDebug</Name> //将要上传的文件夹名，先写死，后续可能是其他数据	
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestOssStsMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_OSS_STS *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestOssSts", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_NAME, XmlTrans(pData->szName).data(), XML_NODE_NAME_MSG_PARAM_NAME);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>RequestOpenDoor或者RequestOpenSecurityRelay</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MAC>0C11050A72E4</MAC> 	
    <Relay>1</Relay>	//relay_id，开哪个门
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestOpenDoor(char *pszBuf, int nSize, DCLIENT_REQUEST_OPENDOOR *pData, int *pDataSize, int nType)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	if(nType == 1)
	{
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestOpenSecurityRelay", XML_NODE_NAME_MSG_TYPE);
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
	}
	else
	{
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestOpenDoor", XML_NODE_NAME_MSG_TYPE);
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
	}

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MAC, XmlTrans(pData->szMAC).data(), XML_NODE_NAME_MSG_PARAM_MAC);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_RELAY, XmlTrans(pData->szRelay).data(), XML_NODE_NAME_MSG_PARAM_RELAY);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>  
	<Type>RequestACInfo</Type>  
	<Params>  
	  <Count>20</Count>
	  <ACID>1;2;3;4;5;6;7;8;9;10;11;12;14;15;345345;345435346;</ACID>  
	</Params>  
</Msg> 
*/
int CMsgHandle::BuildRequestACInfoMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_ACINFO *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE*4];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE*4);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestACInfo", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Count", pData->nCount, "Count");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
	
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE*4, "\t<%s>%s</%s>\r\n", "ACID", XmlTrans(pData->szACID).data(), "ACID");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

int CMsgHandle::BuildSendDeliveryMsg(char *pszBuf, int nSize, DCLIENT_SEND_DELIVERY *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "SendDeliveryMsg", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	for(int i=0; i<pData->nCount; i++)
	{
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", "Delivery");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Account", XmlTrans(pData->deliveryMsg[i].szAccount).data(), "Account");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Number", pData->deliveryMsg[i].nCountNum, "Number");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", "Delivery");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
	}
	
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>  
    <Type>SyncActivity</Type>  
    <Params>  
        <Seq>1234</Seq> //唯一ID  
        <FileName>aaa.json</FileName> //文件名称  
        <FileMD5>***************</FileMD5> //文件MD5  
    <Params>  
<Msg>  
*/
int CMsgHandle::BuildSyncActivityMsg(char *pszBuf, int nSize, DCLIENT_SYNC_ACTIVITY *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "SyncActivity", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Seq", pData->nSeq, "Seq");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "FileName", XmlTrans(pData->szFileName).data(), "FileName");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "FileMD5", XmlTrans(pData->szMD5).data(), "FileMD5");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>  
    <Type>ReportRelay</Type>  
    <Params>   
        <RelayStatus>123</RelayStatus> //代表relay12为开启 其它为关闭
    <Params>  
<Msg>  
*/
int CMsgHandle::BuildReportRelayStatusMsg(char *pszBuf, int nSize, DCLIENT_REPORT_RELAY_STATUS *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportRelay", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "RelayStatus", XmlTrans(pData->szRelay).data(), "RelayStatus");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>FlowOutOfLimit</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Percent>80</Percent>        //使用了百分多少
    <Limit>40</Limit>  //流量总额度多少   如40G
  </Params>
</Msg>
*/
int CMsgHandle::BuildFlowOutOfLimitMsg(char *pszBuf, int nSize, DCLIENT_FLOW_OUT_OF_LIMIT *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "FlowOutOfLimit", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%f</%s>\r\n", "Percent", pData->fPercent, "Percent");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%f</%s>\r\n", "Limit", pData->fLimit, "Limit");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>Call Log</Type>
  <Params>
	<Caller>************</Caller> //主叫号码
	<Callee>************</Callee> //被叫号码
	<CallerName></CallerName> //主叫的MAC
	<CalleeName></CalleeName> //如果是通过联系人列表里呼叫，需要填被叫的MAC，否则放空,如果呼叫SDMC则填写SDMC
	<CallType>2</CallType> //呼叫类别：1-SIP 2-IP 0-无效类别
	<Time>2021-08-17 13:02:03</Time> //呼叫时间
	<Duration>101</Duration> //通话时长，以秒为单位,0为没应答
</Params>
</Msg>
*/
int CMsgHandle::BuildReportCallLogMsg(char *pszBuf, int nSize, DCLIENT_REPORT_CALLLOG *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "Call Log", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Caller", XmlTrans(pData->szCaller).data(), "Caller");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Callee", XmlTrans(pData->szCallee).data(), "Callee");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "CallerName", XmlTrans(pData->szCallerName).data(), "CallerName");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "CalleeName", XmlTrans(pData->szCalleeName).data(), "CalleeName");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "CallType", pData->nCallType, "CallType");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Time", XmlTrans(pData->szTime).data(), "Time");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%d</%s>\r\n", "Duration", pData->nDuration, "Duration");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>  
  <Type>BackupConfigACK</Type>  
  <Params>  
    <FileName>20210816141055_856968.conf</FileName> //文件名称，按照时间戳+6位随机数命名，文件跟从web端导出来一样的加密方式
    <MD5>858585458545856965856585654852<MD5> //文件MD5
  </Params>  
</Msg>
*/
int CMsgHandle::BuildBackupConfigACKMsg(char *pszBuf, int nSize, DCLIENT_BACKUP_CONFIG_ACK *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "BackupConfigACK", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "FileName", XmlTrans(pData->szFileName).data(), "FileName");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "MD5", XmlTrans(pData->szMD5).data(), "MD5");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
  <Type>RequestRtspMonitor</Type>
  <Params>
    <FromIP>************</FromIP>        //本地IP
    <ToIP>************</ToIP>  //目标IP
	<Seq>1258596585</Seq>  //一个10位的随机数，用来区别唯一的监控
	<RTSP>rtsp://************/chr_1</RTSP>  //rtsp地址
	<User>admin</User> //用户名
	<Pwd>admin123</Pwd> //密码
  </Params>
</Msg>
*/
int CMsgHandle::BuildRequestRtspMonitor(char *pszBuf, int nSize, DCLIENT_REQUEST_RTSP_MONITOR *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestRtspMonitor", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "FromIP", XmlTrans(pData->szFromIP).data(), "FromIP");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "ToIP", XmlTrans(pData->szToIP).data(), "ToIP");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Seq", XmlTrans(pData->szSeq).data(), "Seq");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "RTSP", XmlTrans(pData->szRTSP).data(), "RTSP");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "User", XmlTrans(pData->szUser).data(), "User");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Pwd", XmlTrans(pData->szPassword).data(), "Pwd");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}

/*
<Msg>
  <Type>RtspMonitorStop</Type>
  <Params>
    <FromIP>************</FromIP>        //本地IP
    <ToIP>************</ToIP>  //目标IP
	<Seq>1258596585</Seq>  //一个10位的随机数，用来区别唯一的监控
  </Params>
</Msg>
*/
int CMsgHandle::BuildRtspMonitorStop(char *pszBuf, int nSize, DCLIENT_RTSP_MONITOR_STOP *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RtspMonitorStop", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "FromIP", XmlTrans(pData->szFromIP).data(), "FromIP");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "ToIP", XmlTrans(pData->szToIP).data(), "ToIP");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>%s</%s>\r\n", "Seq", XmlTrans(pData->szSeq).data(), "Seq");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize, TRUE);
#endif
	return 0;
}

/*
<Msg>
  <Type>RequestEndUserReg</Type>
  <Params>
    <MAC></MAC>        //设备MAC，暂时无用，就放空
  <Params>
</Msg>
*/
int CMsgHandle::BuildRequestEndUserReg(char *pszBuf, int nSize, int *pDataSize)
{
	if((pszBuf == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "RequestEndUserReg", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s></%s>\r\n", "MAC", "MAC");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
<Type></Type>
<Params>
<Item>   
<MAC>0A:02:03:20:01:17</MAC>
<Location> XXX </Location>
<Type> 1 </Type>  0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机;  5:SDMC;  50: 门禁
</Item>
....
<Item> 同上 </Item>
</Params>
</Msg>
*/
int CMsgHandle::BuildAddKitDevice(char *pszBuf, int nSize, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "AddKitDevice", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	for(int i=0; i<KIT_MAX_SEND_COUNT; i++)
	{
		if(rl_strlen(pData->deviceInfo[i].szMAC) == 0)
		{
			break;
		}
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", "Item");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "MAC", XmlTrans(pData->deviceInfo[i].szMAC).data(), "MAC");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "Location", XmlTrans(pData->deviceInfo[i].szLocation).data(), "Location");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", "Type", pData->deviceInfo[i].nType, "Type");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", "Item");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
	}

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
<Type></Type>
<Params>
<Item>   
<MAC>0A:02:03:20:01:17</MAC>
<Location> XXX </Location>
<Type> 1 </Type>  0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机;  5:SDMC;  50: 门禁
</Item>
....
<Item> 同上 </Item>
</Params>
</Msg>
*/
int CMsgHandle::BuildReportKitDevice(char *pszBuf, int nSize, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "AddKitDevice", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	for(int i=0; i<KIT_MAX_SEND_COUNT; i++)
	{
		if(rl_strlen(pData->deviceInfo[i].szMAC) == 0)
		{
			break;
		}
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", "Item");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "MAC", XmlTrans(pData->deviceInfo[i].szMAC).data(), "MAC");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);

		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "SWVer", XmlTrans(pData->deviceInfo[i].szSWVer).data(), "SWVer");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
		
		rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", "Item");
		rl_strcat_s(pszBuf, nSize, pszTmpLine);
	}

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
<Type>ReuqestKitDevices</Type>
</Msg>
*/
int CMsgHandle::BuildRequestKitDevice(char *pszBuf, int nSize, int *pDataSize)
{
	if(pszBuf == NULL)
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReuqestKitDevices", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}

/*
<Msg>
<Type>ReuqestModifyLocation</Type>
<Params>
<Location> XXX </Location>
<MAC> xxx </MAC>
</Params>
</Msg>
*/
int CMsgHandle::BuildModifyDeviceLocation(char *pszBuf, int nSize, KIT_DEVICE_BASE_INFO *pData, int *pDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
	CHAR *pszTmpLine = new CHAR[XML_NODE_LINE_SIZE];
	memset(pszTmpLine, 0, XML_NODE_LINE_SIZE);
	memset(pszBuf, 0 ,nSize);
	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReuqestModifyLocation", XML_NODE_NAME_MSG_TYPE);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "Location", XmlTrans(pData->szLocation).data(), "Location");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", "MAC", XmlTrans(pData->szMAC).data(), "MAC");
	rl_strcat_s(pszBuf, nSize, pszTmpLine);		

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	rl_sprintf_s(pszTmpLine, XML_NODE_LINE_SIZE,"</%s>\r\n", XML_NODE_NAME_MSG);
	rl_strcat_s(pszBuf, nSize, pszTmpLine);

	delete []pszTmpLine;

	*pDataSize = rl_strlen(pszBuf);
	rl_log_debug("pszBuf=%s", pszBuf);
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	//对MSG进行AES加密
	AesEncryptByMac(pszBuf, pszBuf, pDataSize);
#endif
	return 0;
}


/*	
<Msg>
.	<Type>Discover</Type>
.<Protocal>1.0</Protocal>
.<Params>
..	<Type>Indoor</Type>
.	<DeviceID>*******.1</ DeviceID>
..	<Extension></Extension >
.</Params>
</Msg>
*/
int CMsgHandle::ParseDiscoverMsg(char *pszBuf, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pDiscoverMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	//rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDiscoverMsg, 0, sizeof(SOCKET_MSG_DISCOVER_SEND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pDiscoverMsg->szProtocal, sizeof(pDiscoverMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pDiscoverMsg->szType, sizeof(pDiscoverMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICEID) == 0)
				{
					rl_strcpy_s(pDiscoverMsg->szDeviceID, sizeof(pDiscoverMsg->szDeviceID), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_EXTENSION) == 0)
				{
					rl_strcpy_s(pDiscoverMsg->szExtension, sizeof(pDiscoverMsg->szExtension), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FLAG) == 0)
				{
					rl_strcpy_s(pDiscoverMsg->szFlag, sizeof(pDiscoverMsg->szFlag), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pDiscoverMsg->nSequenceNum = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD) == 0)
				{
					rl_strcpy_s(pDiscoverMsg->szDiscoverMethod, sizeof(pDiscoverMsg->szDiscoverMethod), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}


/*	
<Msg>
.	<Type>DiscoverAck</Type>
.	<Protocal>1.0</Protocal>
.	<Params>
.		<Type>Indoor</Type>
.		<DeviceID>*******.1</ DeviceID>
.		<Extension>1</Extension >
 		<IP>***********3</IP>
 		<FW>***********</FW>
 		<Flag>0</Flag>
 		<DeviceName></DeviceName>
 		<RTSP>rtsp://*************/live/ch00_0</RTSP>
 		<MsgSeq></MsgSeq>
		<MAC>0C1105000001</MAC>
.	</Params>
</Msg>

*/
int CMsgHandle::ParseDiscoverAckMsg(char *pszBuf, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pDiscoverAckMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	//rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDiscoverAckMsg, 0, sizeof(SOCKET_MSG_DISCOVER_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pDiscoverAckMsg->szProtocal, sizeof(pDiscoverAckMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szType, sizeof(pDiscoverAckMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICEID) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szDeviceID, sizeof(pDiscoverAckMsg->szDeviceID), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_EXTENSION) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szExtension, sizeof(pDiscoverAckMsg->szExtension), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szIPAddr, sizeof(pDiscoverAckMsg->szIPAddr), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FW) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szSWVer, sizeof(pDiscoverAckMsg->szSWVer), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FLAG) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szFlag, sizeof(pDiscoverAckMsg->szFlag), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICE_NAME) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szDeviceName, sizeof(pDiscoverAckMsg->szDeviceName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RTSP_URL) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szRTSP, sizeof(pDiscoverAckMsg->szRTSP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pDiscoverAckMsg->nSequenceNum = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICE_CODE) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szDeviceCode, sizeof(pDiscoverAckMsg->szDeviceCode), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DISCOVER_METHOD) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szDiscoverMethod, sizeof(pDiscoverAckMsg->szDiscoverMethod), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
				{
					rl_strcpy_s(pDiscoverAckMsg->szMac, sizeof(pDiscoverAckMsg->szMac), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}



/*
<Msg>
  <Type>RequestConfigration</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Module>Account1</Module>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReqConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG *pReqConfigMsg, int nDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pReqConfigMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, bForceMAC);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReqConfigMsg, 0, sizeof(SOCKET_MSG_CONFIG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReqConfigMsg->szProtocal, sizeof(pReqConfigMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) == 0)
				{
					rl_strcpy_s(pReqConfigMsg->module.szItem[nItemIndex], sizeof(pReqConfigMsg->module.szItem[nItemIndex]), pSubNode->GetText());
					nItemIndex++;
				}
			}
		}
	}

	return 0;
}

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgHandle::ParseReqConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG_FROM_DEVICE *pReqConfigMsg, int nDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pReqConfigMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, bForceMAC);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReqConfigMsg, 0, sizeof(SOCKET_MSG_CONFIG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) == 0)
				{
					rl_strcpy_s(pReqConfigMsg->module.szItem[nItemIndex], sizeof(pReqConfigMsg->module.szItem[nItemIndex]), pSubNode->GetText());
					nItemIndex++;
				}
			}
		}
	}

	return 0;
}
#endif

/*
<Msg>
  <Type>UpdateConfig</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Item>Config.Account1.GENERAL.Enable=1</Item>
	<Item>Config.Account1.GENERAL.Label=1</Item>
	...
  </Params>
</Msg>
*/
int CMsgHandle::ParseUpdateConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG *pUpdateConfigMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pUpdateConfigMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pUpdateConfigMsg, 0, sizeof(SOCKET_MSG_CONFIG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_GM8138 || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	unlink(UPDATE_CONFIG_FILE_TMP_PATH);
	char szItemBuff[4096] = {0};
#endif
	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pUpdateConfigMsg->szProtocal, sizeof(pUpdateConfigMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) == 0)
				{
					rl_strcpy_s(pUpdateConfigMsg->module.szItem[nItemIndex], sizeof(pUpdateConfigMsg->module.szItem[nItemIndex]), pSubNode->GetText());
				#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_GM8138 || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
					rl_strcat_s(szItemBuff, sizeof(szItemBuff), pSubNode->GetText());
					rl_strcat_s(szItemBuff, sizeof(szItemBuff), "\r\n");				
				#endif
					nItemIndex++;
				}
			}
		}
	}
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_LINUX_GM8138 || DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_DOOR_ANDROID)
	//写文件
	FILE *file = fopen(UPDATE_CONFIG_FILE_TMP_PATH, "wb+");
	if(file == NULL)
	{
		printf("open %s error!\n", UPDATE_CONFIG_FILE_TMP_PATH);
	}
	fwrite(szItemBuff, rl_strlen(szItemBuff), 1, file);
    fclose(file);
#endif
	return 0;
}

/*
<Msg>
  <Type>UpgradeStart</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Path>/tmp/downoad/xx.bin</Path>
	<Size>1024</Size>
    <MD5>01ab1298afb23021</MD5>
  </Params>
</Msg>
*/
int CMsgHandle::ParseUpgradeStartMsg(char *pszBuf, SOCKET_MSG_UPGRADE_START *pUpgradeStartMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pUpgradeStartMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pUpgradeStartMsg, 0, sizeof(SOCKET_MSG_UPGRADE_START));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pUpgradeStartMsg->szProtocal, sizeof(pUpgradeStartMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PATH) == 0)
				{
					rl_strcpy_s(pUpgradeStartMsg->szFilePath, sizeof(pUpgradeStartMsg->szFilePath), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SIZE) == 0)
				{
					pUpgradeStartMsg->nFileSize = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MD5) == 0)
				{
					rl_strcpy_s(pUpgradeStartMsg->szFileMd5, sizeof(pUpgradeStartMsg->szFileMd5), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>FileEnd</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Size>1024</Size>
    <MD5>01ab1298afb23021</MD5>
  </Params>
</Msg>
*/
int CMsgHandle::ParseFileEndMsg(char *pszBuf, SOCKET_MSG_FILE_END *pFileEndMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pFileEndMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pFileEndMsg, 0, sizeof(SOCKET_MSG_FILE_END));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pFileEndMsg->szProtocal, sizeof(pFileEndMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SIZE) == 0)
				{
					pFileEndMsg->nFileSize = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MD5) == 0)
				{
					rl_strcpy_s(pFileEndMsg->szFileMd5, sizeof(pFileEndMsg->szFileMd5), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}


/*
<Msg>
  <Type>KeySend</Type>
  <Protocal>1.0</Protocal>
  <Params>
	<PrikeyUrl>http://**************/Download/Privatekey/privatekey_*******.1-2.xml</PrikeyURL>
	<PrikeyMD5>01ab1298afb23021</PrikeyMD5>
    <RfidUrl>http://**************/Download/Rfid/rfid_*******.1-2.xml</RfidUrl>
	<RfidMD5>01ab1298afb23021</RfidMD5>
	<SettingUrl>http://**************/Download/Config/config__*******.1-2.xml</SettingUrl>
	<SettingMD5>01ab1298afb23021</SettingMD5>
	<AddrUrl>http://**************/Download/Addr/addr.xml</AddrUrl>
	<AddrMD5>01ab1298afb23021</AddrMD5>
	<ContactMD5></ContactMD5>
	<ContactURL></ContactURL>
	
  	<FPUrl>http://**************/Download/FingerPrint/*******.1/FP-*******.1-2.tgz</FPUrl>
	<FPMD5>01ab1298afb23021</FPMD5>
	
	<TzUrl>http://**************/TimeZone.xml</TzUrl>
	<TzMD5>01ab1298afb23021</TzMD5>
	<TzDataUrl>http://**************/Do/tzdata.tar.gz</TzDataUrl>
	<TzDataMD5>01ab1298afb23021</TzDataMD5>
	<FaceSyncUrl>http://**************/facesync.xml</FaceSyncUrl>
	<FaceSyncMD5>01ab1298afb23021</FaceSyncMD5>
  </Params>
</Msg>
*/int CMsgHandle::ParseKeySendMsg(char *pszBuf, SOCKET_MSG_KEY_SEND *pKeySendMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pKeySendMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	//rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	LogSplit(pszBuf, rl_strlen(pszBuf)+1, __FUNCTION__);

	memset(pKeySendMsg, 0, sizeof(SOCKET_MSG_KEY_SEND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pKeySendMsg->szProtocal, sizeof(pKeySendMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szPrivatekeyMD5, sizeof(pKeySendMsg->szPrivatekeyMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PRIKEYURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szPrivatekeyUrl, sizeof(pKeySendMsg->szPrivatekeyUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RFIDMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szRfidMD5, sizeof(pKeySendMsg->szRfidMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RFIDURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szRfidUrl, sizeof(pKeySendMsg->szRfidUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ADDRMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szAddrMD5, sizeof(pKeySendMsg->szAddrMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ADDRURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szAddrUrl, sizeof(pKeySendMsg->szAddrUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szConfigMD5, sizeof(pKeySendMsg->szConfigMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONFIGURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szConfigUrl, sizeof(pKeySendMsg->szConfigUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ADMODULEMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szAdModuleMD5, sizeof(pKeySendMsg->szAdModuleMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ADMODULEURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szAdModuleUrl, sizeof(pKeySendMsg->szAdModuleUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szCommunityPhonebookMD5, sizeof(pKeySendMsg->szCommunityPhonebookMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_COMMUNITY_PHONEBOOKURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szCommunityPhonebookUrl, sizeof(pKeySendMsg->szCommunityPhonebookUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szContactMD5, sizeof(pKeySendMsg->szContactMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_CONTACTURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szContactUrl, sizeof(pKeySendMsg->szContactUrl), pSubNode->GetText());
				}
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC				
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FACEIDMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFaceIDMD5, sizeof(pKeySendMsg->szFaceIDMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FACEIDURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFaceIDUrl, sizeof(pKeySendMsg->szFaceIDUrl), pSubNode->GetText());
				}
#endif				
#if RL_SUPPORT_FP_DOWNLOAD
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FPURL) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFPUrl, sizeof(pKeySendMsg->szFPUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FPMD5) == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFPMD5, sizeof(pKeySendMsg->szFPMD5), pSubNode->GetText());
				}
#endif
				else if(rl_strcmp(pSubNode->Value(), "TzUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szTzUrl, sizeof(pKeySendMsg->szTzUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TzMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szTzMD5, sizeof(pKeySendMsg->szTzMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TzDataUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szTzDataUrl, sizeof(pKeySendMsg->szTzDataUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TzDataMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szTzDataMD5, sizeof(pKeySendMsg->szTzDataMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "FaceSyncUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFaceSyncUrl, sizeof(pKeySendMsg->szFaceSyncUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "FaceSyncMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szFaceSyncMD5, sizeof(pKeySendMsg->szFaceSyncMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ACInfoUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szACInfoUrl, sizeof(pKeySendMsg->szACInfoUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ACInfoMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szACInfoMD5, sizeof(pKeySendMsg->szACInfoMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ACMetaUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szACMetaUrl, sizeof(pKeySendMsg->szACMetaUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ACMetaMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szACMetaMD5, sizeof(pKeySendMsg->szACMetaMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ScheduleUrl") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szScheduleUrl, sizeof(pKeySendMsg->szScheduleUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ScheduleMD5") == 0)
				{
					rl_strcpy_s(pKeySendMsg->szScheduleMD5, sizeof(pKeySendMsg->szScheduleMD5), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}


/*
<Msg>
  <Type>UpgradeSend</Type>
  <Protocal>1.0</Protocal>
  <Params>
	<FirmwareVer>26.0.0.1</FirmwareVer>
	<FirmwareUrl>http://**************/Download/Firmware/R26/26.0.0.1.rom</FirmwareUrl>
  </Params>
</Msg>
*/
int CMsgHandle::ParseUpgradeSendMsg(char *pszBuf, SOCKET_MSG_UPGRADE_SEND *pUpgradeSendMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pUpgradeSendMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pUpgradeSendMsg, 0, sizeof(SOCKET_MSG_UPGRADE_SEND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pUpgradeSendMsg->szProtocal, sizeof(pUpgradeSendMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FIRMWAREVER) == 0)
				{
					rl_strcpy_s(pUpgradeSendMsg->szFirmwareVer, sizeof(pUpgradeSendMsg->szFirmwareVer), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FIRMWAREURL) == 0)
				{
					rl_strcpy_s(pUpgradeSendMsg->szFirmwareUrl, sizeof(pUpgradeSendMsg->szFirmwareUrl), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>AdSend</Type>
  <Protocal>1.0</Protocal>
  <Params>
	<Time>2016-08-26 10:00:00</Time>
	<Item Url="http://**************/download/ad/1.png"/, Type="Picture", Duration="10", Count="1">
	<Item Url="http://**************/download/ad/2.png"/, Type="Picture", Duration="10", Count="2">
	<Item Url="http://**************/download/ad/3.png"/, Type="Picture", Duration="10", Count="1">
	<Item Url="http://**************/download/ad/4.mp4"/, Type="Video", Duration="120", Count="1">
	...
  </Params>
</Msg>
*/
int CMsgHandle::ParseAdSendMsg(char *pszBuf, SOCKET_MSG_AD_SEND *pAdSendMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pAdSendMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pAdSendMsg, 0, sizeof(SOCKET_MSG_AD_SEND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pAdSendMsg->szProtocal, sizeof(pAdSendMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pAdSendMsg->szTime, sizeof(pAdSendMsg->szTime), pSubNode->GetText());
				}
				else if(rl_strstr(pSubNode->Value(),XML_NODE_NAME_MSG_PARAM_ITEM) != NULL)
				{
					if(nItemIndex >= AD_ITEM_MAX)
					{
						continue;
					}
					pAdSendMsg->nCount++;
					//取出属性Url, Duration, File
					rl_strcpy_s(pAdSendMsg->items[nItemIndex].szUrl, sizeof(pAdSendMsg->items[nItemIndex].szUrl), 
						xml_get_node_attribute(pSubNode, XML_NODE_NAME_MSG_PARAM_URL));
					
					rl_strcpy_s(pAdSendMsg->items[nItemIndex].szType, sizeof(pAdSendMsg->items[nItemIndex].szType), 
						xml_get_node_attribute(pSubNode, XML_NODE_NAME_MSG_PARAM_TYPE));

					CHAR *pszDuration = xml_get_node_attribute(pSubNode, XML_NODE_NAME_MSG_PARAM_DURATION);
					if(pszDuration != NULL)
					{
						pAdSendMsg->items[nItemIndex].nDuration = rl_atoi(pszDuration);
					}
					
					CHAR *pszCount = xml_get_node_attribute(pSubNode, XML_NODE_NAME_MSG_PARAM_COUNT);
					
					if(pszCount != NULL)
					{
						pAdSendMsg->items[nItemIndex].nCount = rl_atoi(pszCount);
					}

					nItemIndex++;
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>AlarmSend</Type>
  <Protocal>1.0</Protocal>
  <Params>
       <Type>Normal</Type>
        <AlarmType>1</AlarmType>(区别于Type，是alarm的msg)-------新增alarm的类型 1=门口机未关门，后期多语言会把室内机的各种alarm类型加入
	<ID>123</ID>
	<Address>*******.1</Address>
	<Extension>1</Extension>
	<DeviceType>1</DeviceType>
	<AlarmCode>0</AlarmCode>
	<MAC>0C1105252569</MAC>
	<AlarmCustomize>0</AlarmCustomize>
	<AlarmLocation>1</AlarmLocation>
	<AlarmZone>1</AlarmZone>(告警防区)
  </Params>
</Msg>
*/
int CMsgHandle::ParseAlarmSendMsg(char *pszBuf, SOCKET_MSG_ALARM_SEND *pAlarmSendMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pAlarmSendMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pAlarmSendMsg, 0, sizeof(SOCKET_MSG_ALARM_SEND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pAlarmSendMsg->szProtocal, sizeof(pAlarmSendMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pAlarmSendMsg->szType, sizeof(pAlarmSendMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ADDRESS) == 0)
				{
					rl_strcpy_s(pAlarmSendMsg->szAddress, sizeof(pAlarmSendMsg->szAddress), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pAlarmSendMsg->szTime, sizeof(pAlarmSendMsg->szTime), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ID) == 0)
				{
					pAlarmSendMsg->id = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICETYPE) == 0)
				{
					pAlarmSendMsg->nDeviceType = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_EXTENSION) == 0)
				{
					pAlarmSendMsg->nExtension = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DURATION) == 0)
				{
					pAlarmSendMsg->nDuration= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CODE) == 0)
				{
					pAlarmSendMsg->nAlarmCode= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
				{
					rl_strcpy_s(pAlarmSendMsg->szMAC, sizeof(pAlarmSendMsg->szMAC), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE) == 0)
				{
					pAlarmSendMsg->unAlarmCustomize= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION) == 0)
				{
					pAlarmSendMsg->unAlarmLocation= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_ZONE) == 0)
				{
					pAlarmSendMsg->unAlarmZone= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>Alarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
  	<MsgSeq>1234444</MsgSeq>
	<ID>74019876</ID>
	<Type>Normal</Type>
	<FromName>INDOOR_*******.2-1</FromName>
	<ToName>MANAGEMENT_1.1.1-1</ToName>
	<From>***********4</From>
	<To>***********5</To>
	<Time>2016-08-26 10:00:00</Time>
  </Params>
</Msg>
*/
int CMsgHandle::ParseAlarmMsg(char *pszBuf, SOCKET_MSG_ALARM *pAlarmMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pAlarmMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pAlarmMsg->szProtocal, sizeof(pAlarmMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szType, sizeof(pAlarmMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pAlarmMsg->nSequenceNum = rl_atoi(pSubNode->GetText());					
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szTime, sizeof(pAlarmMsg->szTime), pSubNode->GetText());
				}
/*去掉from_name和to_name的解析
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szFromName, sizeof(pAlarmMsg->szFromName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szToName, sizeof(pAlarmMsg->szToName), pSubNode->GetText());
				}
*/
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szFrom, sizeof(pAlarmMsg->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szTo, sizeof(pAlarmMsg->szTo), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CODE) == 0)
				{
					pAlarmMsg->nAlarmCode= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE) == 0)
				{
					pAlarmMsg->unAlarmCustomize= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION) == 0)
				{
					pAlarmMsg->unAlarmLocation= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_ZONE) == 0)
				{
					pAlarmMsg->unAlarmZone= rl_atoi(pSubNode->GetText());
				}

			}
		}
	}

	return 0;
}


/*
<Msg>
  <Type>AlarmAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>0000042949</ID> (alarmid， 10位数字)
     <MsgSeq>0123233000<MsgSeq> //10位数字
  </Params>
</Msg>

*/
int CMsgHandle::ParseAlarmAckMsg(char *pszBuf, SOCKET_MSG_ALARM *pAlarmMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pAlarmMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pAlarmMsg, 0, sizeof(SOCKET_MSG_ALARM));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pAlarmMsg->szProtocal, sizeof(pAlarmMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pAlarmMsg->szType, sizeof(pAlarmMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pAlarmMsg->nSequenceNum = (pTxt == NULL) ? 0   : rl_atoi(pTxt);
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ID) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pAlarmMsg->id = (pTxt == NULL) ? 0   : rl_atoi(pTxt);
				}
			}
		}
	}
	return 0;
}


/*
<Msg>
  <Type>DealAlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID> (alarmid， 11位数字，在rest api中的处理消息： PUT /alarm/xx)
     <User>XXX</User>        (告警的处理人)
     <Result>XXX</Result>    (告警的处理内容,长度最大512字节)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
     <AlarmCustomize></AlarmCustomize>
     <AlarmLocation></AlarmLocation>
     <AlarmZone></AlarmZone>
  </Params>
</Msg>
*/
int CMsgHandle::ParseAlarmDealMsg(char *pszBuf, SOCKET_MSG_ALARM_DEAL *pAlarmDealMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pAlarmDealMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pAlarmDealMsg, 0, sizeof(SOCKET_MSG_ALARM_DEAL));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pAlarmDealMsg->szProtocal, sizeof(pAlarmDealMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{				
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ID) == 0)
				{
					pAlarmDealMsg->id = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER) == 0)
				{
					rl_strcpy_s(pAlarmDealMsg->szUser, sizeof(pAlarmDealMsg->szUser), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					rl_strcpy_s(pAlarmDealMsg->szResult, sizeof(pAlarmDealMsg->szResult), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
				{
					rl_strcpy_s(pAlarmDealMsg->szType, sizeof(pAlarmDealMsg->szType), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pAlarmDealMsg->szTime, sizeof(pAlarmDealMsg->szTime), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CODE) == 0)
				{
					pAlarmDealMsg->nAlarmCode = rl_atoi(pSubNode->GetText());	
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE) == 0)
				{
					pAlarmDealMsg->unAlarmCustomize= rl_atoi(pSubNode->GetText());	
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION) == 0)
				{
					pAlarmDealMsg->unAlarmLocation= rl_atoi(pSubNode->GetText());	
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_ZONE) == 0)
				{
					pAlarmDealMsg->unAlarmZone= rl_atoi(pSubNode->GetText());	
				}

			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>HeartBeatPeriod</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Expire>60</Expire>
  </Params>
</Msg>
*/
int CMsgHandle::ParseHeartBeatPeriodMsg(char *pszBuf, SOCKET_MSG_HEARTBEAT_PERIOD *pHeartBeatPeriodMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pHeartBeatPeriodMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pHeartBeatPeriodMsg, 0, sizeof(SOCKET_MSG_HEARTBEAT_PERIOD));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pHeartBeatPeriodMsg->szProtocal, sizeof(pHeartBeatPeriodMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_EXPIRE) == 0)
				{
					rl_strcpy_s(pHeartBeatPeriodMsg->szExpire, sizeof(pHeartBeatPeriodMsg->szExpire), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}


#if RL_GLOBAL_SUPPORT_TMP_KEY
/*
<Msg>
  <Type>CheckTmpKeyAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>0</Result> 	(校验不成功：1)
    <MsgSeq>429496795</MsgSeq>
    <Relay> 1 </Relay>
	<SecurityRelay>1</SecurityRelay>
	<UnitApt> 101 </UnitApt>
	<PerID>123456</PerID>
  </Params>
</Msg>
*/

int CMsgHandle::ParseCheckTmpKeyMsg(char *pszBuf, SOCKET_MSG_CHECK_TMP_KEY *pCheckTmpKey, INT nDataSize)
{
	if((pszBuf == NULL) || (pCheckTmpKey == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pCheckTmpKey, 0, sizeof(SOCKET_MSG_CHECK_TMP_KEY));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pCheckTmpKey->szProtocal, sizeof(pCheckTmpKey->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					pCheckTmpKey->nResult= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					rl_strcpy_s(pCheckTmpKey->szMsgSeqCode, sizeof(pCheckTmpKey->szMsgSeqCode), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
				{
					rl_strcpy_s(pCheckTmpKey->szRelay, sizeof(pCheckTmpKey->szRelay), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "SecurityRelay") == 0)
				{
					rl_strcpy_s(pCheckTmpKey->szSecurityRelay, sizeof(pCheckTmpKey->szSecurityRelay), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_UNITAPT) == 0)
				{
					rl_strcpy_s(pCheckTmpKey->szUnitAPT, sizeof(pCheckTmpKey->szUnitAPT), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "PerID") == 0)
				{
					rl_strcpy_s(pCheckTmpKey->szPerID, sizeof(pCheckTmpKey->szPerID), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}
#endif
#if RL_GLOBAL_SUPPORT_VRTSP	
/*
<Msg>
  <Type>StartRTSP</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <RemoteIP>*************</RemoteIP>
    <RemotePort>1234</RemotePort>
	<Expire>60</Expire>
	<SSRC>4FCDB60F</SSRC>
  </Params>
</Msg>
*/
int CMsgHandle::ParseStartRtspMsg(char *pszBuf, SOCKET_MSG_START_RTSP *pStartRtspMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pStartRtspMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pStartRtspMsg, 0, sizeof(SOCKET_MSG_START_RTSP));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pStartRtspMsg->szProtocal, sizeof(pStartRtspMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_REMOTE_PORT) == 0)
				{
					pStartRtspMsg->nRemotePort= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_REMOTE_IP) == 0)
				{
					rl_strcpy_s(pStartRtspMsg->szRemoteIP, sizeof(pStartRtspMsg->szRemoteIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_EXPIRE) == 0)
				{
					pStartRtspMsg->nExpire = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SSRC) == 0)
				{
					if(pSubNode->GetText() != NULL)
					{
						pStartRtspMsg->nSSRC = strtol(pSubNode->GetText(), NULL, 16);
					}
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>StopRTSP</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <RemoteIP>*************</RemoteIP>
    <RemotePort>1234</RemotePort>
  </Params>
</Msg>
*/
int CMsgHandle::ParseStopRtspMsg(char *pszBuf, SOCKET_MSG_STOP_RTSP *pStopRtspMsg, int nDataSize)
{
	if((pszBuf == NULL) || (pStopRtspMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif	

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pStopRtspMsg, 0, sizeof(SOCKET_MSG_STOP_RTSP));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pStopRtspMsg->szProtocal, sizeof(pStopRtspMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PORT) == 0)
				{
					pStopRtspMsg->nRemotePort= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_REMOTE_IP) == 0)
				{
					rl_strcpy_s(pStopRtspMsg->szRemoteIP, sizeof(pStopRtspMsg->szRemoteIP), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

#endif
/*
<Msg>
  <Type>GetBindCodeAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>0</Result>   (绑定码生成失败：2, 绑定码已经超过数量限制：3；    下面的<AccessCode>的标签就不需要去校验了)
    <BindCode>01010102Xxxxx</BindCode> 	 //16位字符串形式的数字.
    <MsgSeq> 429496700</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::ParseGetBindCodeMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeCreate, int nDataSize)
{
	if((pszBuf == NULL) || (pBindCodeCreate == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pBindCodeCreate, 0, sizeof(DLCIENT_BIND_CODE_CREATE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pBindCodeCreate->szProtocal, sizeof(pBindCodeCreate->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeCreate->nResult = (pTxt == NULL) ? -1   : rl_atoi(pTxt);;
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_BIND_CODE) == 0)
				{
					rl_strcpy_s(pBindCodeCreate->szBindCode, sizeof(pBindCodeCreate->szBindCode), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeCreate->nSequenceNum = (pTxt == NULL) ? 0   : rl_atoi(pTxt);;
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>UnBindCodeAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>0</Result>   (解绑失败：4)
    <MsgSeq> 429496701</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::ParseDeleteBindCodeMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeDelete, int nDataSize)
{
	if((pszBuf == NULL) || (pBindCodeDelete == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pBindCodeDelete, 0, sizeof(DLCIENT_BIND_CODE_CREATE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pBindCodeDelete->szProtocal, sizeof(pBindCodeDelete->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeDelete->nResult = (pTxt == NULL) ? -1   : rl_atoi(pTxt);
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeDelete->nSequenceNum = (pTxt == NULL) ? 0   : rl_atoi(pTxt);
				}
			}
		}
	}

	return 0;
}
/*
<Msg>
  <Type>GetBindListAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>0</Result>   (获取绑定列表失败：5)
    <BindNum>10<BindNum>  (目前已经生成的绑定码数量，0--10之间)
    <MsgSeq> 429496702</MsgSeq>
    <!--  绑定列表 -->
    <Bind> 	
	<BindCode>xxxxxxxx</BindCode>
	<Status>1</Status> (1:已经被绑定，0：还未被绑定)
	<Time>2017-01-01 01:02:03</Time>
	<DeviceCode>xxxxxxxxxxx</DeviceCode> (手机串号)
    </Bind>
    <Bind> 	
	<BindCode>xxxxxxxx</BindCode>
	<Status>1</Status> (1:已经被绑定，0：还未被绑定)
	<Time>2017-01-01 01:02:03</Time>
	<DeviceCode>xxxxxxxxxxx</DeviceCode> (手机串号)
    </Bind>
    <Bind> 	
	.
	.
	.
    </Bind>  
  </Params>
</Msg>
*/
int CMsgHandle::ParseGetBindCodeListMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_LIST *pBindCodeList, int nDataSize)
{
	if((pszBuf == NULL) || (pBindCodeList == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pBindCodeList, 0, sizeof(SOCKET_MSG_BIND_CODE_LIST));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pBindCodeList->szProtocal, sizeof(pBindCodeList->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{			
			int nIndex = 0;
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeList->nResult =  (pTxt == NULL) ? -1 : rl_atoi(pTxt);
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeList->nSequenceNum =  (pTxt == NULL) ? 0 : rl_atoi(pTxt);
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_BIND_NUM) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					pBindCodeList->nBindCodeCount =  (pTxt == NULL) ? 0 : rl_atoi(pTxt);
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_BIND) == 0)
				{			
					TiXmlElement* pSubBindNode = NULL;
					for (pSubBindNode = pSubNode->FirstChildElement(); pSubBindNode; pSubBindNode = pSubBindNode->NextSiblingElement())
					{
						if(nIndex >= BIND_CODE_LIST_COUNT_MAX)
						{
							break;
						}
						if(rl_strcmp(pSubBindNode->Value(), XML_NODE_NAME_MSG_BIND_ITEM_BINDCODE) == 0)
						{
							rl_strcpy_s(pBindCodeList->bindCodeInfo[nIndex].szBindCode, sizeof(pBindCodeList->bindCodeInfo[nIndex].szBindCode), pSubBindNode->GetText());
						}
						else if(rl_strcmp(pSubBindNode->Value(), XML_NODE_NAME_MSG_BIND_ITEM_STATUS) == 0)
						{							
							pBindCodeList->bindCodeInfo[nIndex].nStatus = rl_atoi(pSubBindNode->GetText());
						}
						else if(rl_strcmp(pSubBindNode->Value(), XML_NODE_NAME_MSG_BIND_ITEM_TIME) == 0)
						{
							rl_strcpy_s(pBindCodeList->bindCodeInfo[nIndex].szTime, sizeof(pBindCodeList->bindCodeInfo[nIndex].szTime), pSubBindNode->GetText());
						}
						else if(rl_strcmp(pSubBindNode->Value(), XML_NODE_NAME_MSG_BIND_ITEM_DEVICECODE) == 0)
						{
							rl_strcpy_s(pBindCodeList->bindCodeInfo[nIndex].szDeviceCode, sizeof(pBindCodeList->bindCodeInfo[nIndex].szDeviceCode), pSubBindNode->GetText());
						}
					}
					nIndex++;
				}
			}
		}
	}
	return 0;
}


/*
<Msg>
  <Type>BindStatusChangeNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <MsgSeq>429496702</MsgSeq>
  </Params>
</Msg>
*/
int CMsgHandle::ParseNotifyBindCodeChangeMsg(char *pszBuf, int *pSequenceNum, int nDataSize)
{
	if((pszBuf == NULL) || (pSequenceNum == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					const char* pTxt = pSubNode->GetText();
					*pSequenceNum =  (pTxt == NULL) ? 0   : rl_atoi(pTxt);
				}
			}
		}
	}

	return 0;
}

#if RL_SUPPORT_ARMING
/*
<Msg>
  <Type>RequestArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>AppUser</FromName>
      <ToName> INDOOR_*******.2-1</ToName>
      <From>***********4</From>
	<To>***********5</To>
      <Action>Get<Action>
      <Mode>0</Mode>
  </Params>
</Msg>
*/
int CMsgHandle::ParseRequestArmingMsg(char *pszBuf, SOCKET_MSG_REQUEST_ARMING *pRequestArmingMsg, INT nDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pRequestArmingMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, bForceMAC);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pRequestArmingMsg, 0, sizeof(SOCKET_MSG_REQUEST_ARMING));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pRequestArmingMsg->szProtocal, sizeof(pRequestArmingMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pRequestArmingMsg->szFrom, sizeof(pRequestArmingMsg->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pRequestArmingMsg->szTo, sizeof(pRequestArmingMsg->szTo), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
				{
					rl_strcpy_s(pRequestArmingMsg->szFromIP, sizeof(pRequestArmingMsg->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
				{
					rl_strcpy_s(pRequestArmingMsg->szToIP, sizeof(pRequestArmingMsg->szToIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ACTION) == 0)
				{
					rl_strcpy_s(pRequestArmingMsg->szAction, sizeof(pRequestArmingMsg->szAction), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MODE) == 0)
				{
					pRequestArmingMsg->nMode = rl_atoi(pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}
#endif

#if RL_SUPPORT_ARMING_P2P
/*
<Msg>
  <Type>ReportArming</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>INDOOR_*******.2-1</FromName>
      <ToName>AppUser</ToName>           //如果AppUser=空转发给所有同节点App
      <From>***********3</From>
      <To>***********4</To>
      <Mode>0</Mode>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReportArmingMsg(char *pszBuf, SOCKET_MSG_REPORT_ARMING *pReportArming, int nDataSize, BOOL bForceMAC)
{
	if((pszBuf == NULL) || (pReportArming == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, bForceMAC);
#endif
	memset(pReportArming, 0, sizeof(SOCKET_MSG_REPORT_ARMING));
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);
	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReportArming->szProtocal, sizeof(pReportArming->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pReportArming->szFrom, sizeof(pReportArming->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pReportArming->szTo, sizeof(pReportArming->szTo), pSubNode->GetText());
				}	
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM) == 0)
				{
					rl_strcpy_s(pReportArming->szFromIP, sizeof(pReportArming->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO) == 0)
				{
					rl_strcpy_s(pReportArming->szToIP, sizeof(pReportArming->szToIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MODE) == 0)
				{
					if(!rl_str_isempty(pSubNode->GetText()))
					{
						pReportArming->nMode = atoi(pSubNode->GetText());
					}
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SYNC) == 0)
				{
					if(!rl_str_isempty(pSubNode->GetText()))
					{
						pReportArming->nSync= atoi(pSubNode->GetText());
					}
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ACTION) == 0)
				{
					if(!rl_str_isempty(pSubNode->GetText()))
					{
						pReportArming->nActionType= atoi(pSubNode->GetText());
					}
				}
			}
		}
	}
	return 0;
}

#endif

#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
/*
<Msg>
  <Type>ReportNoAnswerFwdNumber</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <FromName>OUTDOOR_*******.2-1</FromName>
	<ToName>INDOOR_*******.2-2</ToName>
    <Group0>220,221,222</Group0>
	<Group1>330,331,332</Group1>
	<Group2>440,441,442</Group2>
  </Params>
</Msg>

*/
int CMsgHandle::ParseReportForwardNumberMsg(char *pszBuf, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumberMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReportForwardNumberMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReportForwardNumberMsg, 0, sizeof(SOCKET_MSG_REPORT_FORWARD_NUMBER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReportForwardNumberMsg->szProtocal, sizeof(pReportForwardNumberMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP0) == 0)
				{
					rl_strcpy_s(pReportForwardNumberMsg->szGroup0, sizeof(pReportForwardNumberMsg->szGroup0), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP1) == 0)
				{
					rl_strcpy_s(pReportForwardNumberMsg->szGroup1, sizeof(pReportForwardNumberMsg->szGroup1), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP2) == 0)
				{
					rl_strcpy_s(pReportForwardNumberMsg->szGroup2, sizeof(pReportForwardNumberMsg->szGroup2), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pReportForwardNumberMsg->szFrom, sizeof(pReportForwardNumberMsg->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pReportForwardNumberMsg->szTo, sizeof(pReportForwardNumberMsg->szTo), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
/*
<Msg>
  <Type>PushNoAnswerFwdNumber</Type>
  <Protocal>1.0</Protocal>
  <Params>
      <FromName>INDOOR_*******.2-1</FromName>
	  <ToName>OUTDOOR_*******.2-2</ToName>
	  <Action>Set<Action>
      <Group0>220,221,222</Group0>
	  <Group1>330,331,332</Group1>
      <Group2>440,441,442</Group2>
  </Params>
</Msg>
*/
int CMsgHandle::ParsePushForwardNumberMsg(char *pszBuf, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumberMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pPushForwardNumberMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pPushForwardNumberMsg, 0, sizeof(SOCKET_MSG_PUSH_FORWARD_NUMBER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pPushForwardNumberMsg->szProtocal, sizeof(pPushForwardNumberMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP0) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szGroup0, sizeof(pPushForwardNumberMsg->szGroup0), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP1) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szGroup1, sizeof(pPushForwardNumberMsg->szGroup1), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_GROUP2) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szGroup2, sizeof(pPushForwardNumberMsg->szGroup2), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ACTION) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szAction, sizeof(pPushForwardNumberMsg->szAction), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szFrom, sizeof(pPushForwardNumberMsg->szFrom), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TO_NAME) == 0)
				{
					rl_strcpy_s(pPushForwardNumberMsg->szTo, sizeof(pPushForwardNumberMsg->szTo), pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}
#endif

/*
<Msg>
  <Type>ContactsUrl</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Url>http://ip:port/xx/${mac}.xml</Url> 	
  </Params>
</Msg>
*/
int CMsgHandle::ParseContactUrlMsg(char *pszBuf, SOCKET_MSG_CONTACT_URL *pContactUrlMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pContactUrlMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pContactUrlMsg, 0, sizeof(SOCKET_MSG_CONTACT_URL));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pContactUrlMsg->szProtocal, sizeof(pContactUrlMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_URL) == 0)
				{
					rl_strcpy_s(pContactUrlMsg->szContactUrl, sizeof(pContactUrlMsg->szContactUrl), pSubNode->GetText());
				}			
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>ReconnectRps</Type>
  <Params>
  <ServerAddr>http://rps.akuvox.com:8080/redirect?mac=</ServerAddr>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReconnectRPSMsg(char *pszBuf, SOCKET_MSG_RECONNECT_RPS *pReconnectRPSMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReconnectRPSMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByDefault(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReconnectRPSMsg, 0, sizeof(SOCKET_MSG_RECONNECT_RPS));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReconnectRPSMsg->szProtocal, sizeof(pReconnectRPSMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_ADDR) == 0)
				{
					rl_strcpy_s(pReconnectRPSMsg->szRPSServer, sizeof(pReconnectRPSMsg->szRPSServer), pSubNode->GetText());
				}			
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>reconnectGateway</Type>
  <Params>
  <ServerAddr>ip:port</ServerAddr>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReconnectGateWayMsg(char *pszBuf, SOCKET_MSG_RECONNECT_GATEWAY *pReconnectGateWayMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReconnectGateWayMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByDefault(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReconnectGateWayMsg, 0, sizeof(SOCKET_MSG_RECONNECT_GATEWAY));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReconnectGateWayMsg->szProtocal, sizeof(pReconnectGateWayMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_ADDR) == 0)
				{
					rl_strcpy_s(pReconnectGateWayMsg->szGateWayServer, sizeof(pReconnectGateWayMsg->szGateWayServer), pSubNode->GetText());
				}			
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Type>reconnectAccessServer</Type>
  <Params>
  <ServerAddr>ip:port</ServerAddr>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReconnectAccessMsg(char *pszBuf, SOCKET_MSG_RECONNECT_ACCESS_SERVER *pReconnectAccessMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pReconnectAccessMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByDefault(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReconnectAccessMsg, 0, sizeof(SOCKET_MSG_RECONNECT_ACCESS_SERVER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pReconnectAccessMsg->szProtocal, sizeof(pReconnectAccessMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_ADDR) == 0)
				{
					rl_strcpy_s(pReconnectAccessMsg->szAccessServer, sizeof(pReconnectAccessMsg->szAccessServer), pSubNode->GetText());
				}			
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Params>
	  <Result>1</Result>---1校验成功，别的校验失败
          <RemoteSipUser>sip账号</RemoteSipUser>------最好判断跟这个sip的通话是否还在
          <MsgSeq>这次校验的随机码</MsgSeq>---跟之前发送的一致
  </Params>
</Msg>
*/
int CMsgHandle::ParseCheckDtmfAckMsg(char *pszBuf, SOCKET_MSG_CHECK_DTMF_ACK* pCheckDtmfAckMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pCheckDtmfAckMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pCheckDtmfAckMsg, 0, sizeof(SOCKET_MSG_CHECK_DTMF_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pCheckDtmfAckMsg->szProtocal, sizeof(pCheckDtmfAckMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					pCheckDtmfAckMsg->nResult = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_REMOTE_SIP_USER) == 0)
				{
					rl_strcpy_s(pCheckDtmfAckMsg->szRemoteSip, sizeof(pCheckDtmfAckMsg->szRemoteSip), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
				{
					pCheckDtmfAckMsg->nSequenceNum = rl_atoi(pSubNode->GetText());
				}
			}
		}
	}

	return 0;
}

/*
<Msg>
  <Params>
      <DeviceCode>0311000000</DeviceCode>  (10位纯数字的字符串)
  </Params>
</Msg>
*/
int CMsgHandle::ParseDeviceCodeMsg(char *pszBuf, SOCKET_MSG_DEVICE_CODE* pDeviceCodeMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pDeviceCodeMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDeviceCodeMsg, 0, sizeof(SOCKET_MSG_DEVICE_CODE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			rl_strcpy_s(pDeviceCodeMsg->szProtocal, sizeof(pDeviceCodeMsg->szProtocal), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DEVICE_CODE) == 0)
				{
					rl_strcpy_s(pDeviceCodeMsg->szDeviceCode, sizeof(pDeviceCodeMsg->szDeviceCode), pSubNode->GetText());
				}				
			}
		}
	}

	return 0;
}

#if RL_SUPPORT_DEVICE_MAINTENANCE

/*
<Msg> 
  <Type>GetDevLog</Type>
  <Params>
	<ServerUrl>ftp://severip:port/logDir/</ServerUrl>--(上传url)
	<Username>xxx</Username>
	<Password>xxx</Password>
	<Filename>fffffff.log</Filename>--(上传的名称)
  </Params>
</Msg>
*/
int CMsgHandle::ParseMaintenanceGetLogMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_GETLOG* pMaintenanceGetLog, INT nDataSize)
{
	if((pszBuf == NULL) || (pMaintenanceGetLog == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pMaintenanceGetLog, 0, sizeof(SOCKET_MSG_MAINTENANCE_GETLOG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_URL) == 0)
				{
					rl_strcpy_s(pMaintenanceGetLog->szServerUrl, sizeof(pMaintenanceGetLog->szServerUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceGetLog->szUserName, sizeof(pMaintenanceGetLog->szUserName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PASSWORD) == 0)
				{
					rl_strcpy_s(pMaintenanceGetLog->szPasswd, sizeof(pMaintenanceGetLog->szPasswd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FILE_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceGetLog->szFileName, sizeof(pMaintenanceGetLog->szFileName), pSubNode->GetText());
				}				
			}
		}
	}

	return 0;
}


/*
<Msg>
  <Type>StartPcap</Type>
  <Params>
	<ServerUrl>ftp://severip:port/logDir/</ServerUrl>--(上传url)
	<Username>xxx</Username>
	<Password>xxx</Password>
	<Filename>fffffff.log</Filename>--(上传的名称)
 <Duration>120</Duration>
  </Params>
</Msg>
*/
int CMsgHandle::ParseMaintenanceStartPcapMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_START_PCAP* pMaintenanceStartPcap, INT nDataSize)
{
	if((pszBuf == NULL) || (pMaintenanceStartPcap == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pMaintenanceStartPcap, 0, sizeof(SOCKET_MSG_MAINTENANCE_GETLOG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_URL) == 0)
				{
					rl_strcpy_s(pMaintenanceStartPcap->szServerUrl, sizeof(pMaintenanceStartPcap->szServerUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceStartPcap->szUserName, sizeof(pMaintenanceStartPcap->szUserName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PASSWORD) == 0)
				{
					rl_strcpy_s(pMaintenanceStartPcap->szPasswd, sizeof(pMaintenanceStartPcap->szPasswd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FILE_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceStartPcap->szFileName, sizeof(pMaintenanceStartPcap->szFileName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DURATION) == 0)
				{
					pMaintenanceStartPcap->nDuration = rl_atoi(pSubNode->GetText());
				}	
			}
		}
	}
	return 0;
}


/*
<Msg>
  <Type>StopPcap</Type>
  <Params>
	<ServerUrl>ftp://severip:port/logDir/</ServerUrl>--(上传url)
	<Username>xxx</Username>
	<Password>xxx</Password>
	<Filename>fffffff.log</Filename>--(上传的名称)
  </Params>
</Msg>
*/
int CMsgHandle::ParseMaintenanceStopPcapMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_STOP_PCAP* pMaintenanceStopPcap, INT nDataSize)
{
	if((pszBuf == NULL) || (pMaintenanceStopPcap == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pMaintenanceStopPcap, 0, sizeof(SOCKET_MSG_MAINTENANCE_GETLOG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_URL) == 0)
				{
					rl_strcpy_s(pMaintenanceStopPcap->szServerUrl, sizeof(pMaintenanceStopPcap->szServerUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceStopPcap->szUserName, sizeof(pMaintenanceStopPcap->szUserName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PASSWORD) == 0)
				{
					rl_strcpy_s(pMaintenanceStopPcap->szPasswd, sizeof(pMaintenanceStopPcap->szPasswd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FILE_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceStopPcap->szFileName, sizeof(pMaintenanceStopPcap->szFileName), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>GetDevConfig</Type>
  <Params>
	<ServerUrl>ftp://severip:port/logDir/</ServerUrl>--(上传url)
	<Username>xxx</Username>
	<Password>xxx</Password>
	<Filename>fffffff.log</Filename>--(上传的名称)
  </Params>
</Msg> 
*/
int CMsgHandle::ParseMaintenanceGetDevConfig(char *pszBuf, SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG* pMaintenanceGetDevConfig, INT nDataSize)
{
	if((pszBuf == NULL) || (pMaintenanceGetDevConfig == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pMaintenanceGetDevConfig, 0, sizeof(SOCKET_MSG_MAINTENANCE_GETLOG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SERVER_URL) == 0)
				{
					rl_strcpy_s(pMaintenanceGetDevConfig->szServerUrl, sizeof(pMaintenanceGetDevConfig->szServerUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceGetDevConfig->szUserName, sizeof(pMaintenanceGetDevConfig->szUserName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PASSWORD) == 0)
				{
					rl_strcpy_s(pMaintenanceGetDevConfig->szPasswd, sizeof(pMaintenanceGetDevConfig->szPasswd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FILE_NAME) == 0)
				{
					rl_strcpy_s(pMaintenanceGetDevConfig->szFileName, sizeof(pMaintenanceGetDevConfig->szFileName), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}


/*<Msg>
  <Params>
      <Command>cat /xx/xx/xxx</Command>(256字节)
  </Params>
</Msg>
*/
int CMsgHandle::ParseCliCommandMsg(char *pszBuf, SOCKET_MSG_CLI_COMMAND *pCliCommandMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pCliCommandMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pCliCommandMsg, 0, sizeof(SOCKET_MSG_CLI_COMMAND));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_COMMAND) == 0)
				{
					rl_strcpy_s(pCliCommandMsg->szCommand, sizeof(pCliCommandMsg->szCommand), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

#endif

/*
<Msg>
  <Params>
     <MAC>60</MAC>---门口机的mac
  </Params>
</Msg
*/
int CMsgHandle::ParseDoorMotionAlertMsg(char *pszBuf, SOCKET_MSG_DOOR_MOTION_ALERT* pDoorMotionAlert, INT nDataSize)
{
	if((pszBuf == NULL) || (pDoorMotionAlert == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByDefault(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDoorMotionAlert, 0, sizeof(SOCKET_MSG_DOOR_MOTION_ALERT));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
				{
					rl_strcpy_s(pDoorMotionAlert->szMAC, sizeof(pDoorMotionAlert->szMAC), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

int CMsgHandle::ParseCommonTransferMsg(char *pszBuf, SOCKET_MSG_COMMON_TRANSFER* pCommonTransferMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pCommonTransferMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pCommonTransferMsg, 0, sizeof(SOCKET_MSG_COMMON_TRANSFER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			rl_strcpy_s(pCommonTransferMsg->szMsgType, sizeof(pCommonTransferMsg->szMsgType), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_FROM) == 0)
		{
			rl_strcpy_s(pCommonTransferMsg->szFrom, sizeof(pCommonTransferMsg->szFrom), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TO) == 0)
		{
			rl_strcpy_s(pCommonTransferMsg->szTo, sizeof(pCommonTransferMsg->szTo), pNode->GetText());
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())
			{
				//根据消息id解析出相应的结构体
				if(rl_strcmp(pCommonTransferMsg->szMsgType, SOCKET_MSG_TYPE_NAME_SEND_DTMF_SET) == 0)
				{
					DCLIENT_SET_DTMF *pSendDtmfSetMsg = (DCLIENT_SET_DTMF *)pCommonTransferMsg->szCommonBuf;
					if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
					{
						pSendDtmfSetMsg->nSequenceNum = atoi(pSubNode->GetText());
					}
					if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_DTMF) == 0)
					{
						rl_strcpy_s(pSendDtmfSetMsg->szDTMF, sizeof(pSendDtmfSetMsg->szDTMF), pSubNode->GetText());
					}
				}				
			}
		}
	}
	return 0;
}

#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
/*
<Msg>
.	<Type>DownloadFacePic</Type>
.<Protocal>2.0</Protocal>
.<Params>
..<Url>tftp://192.168.11.100/Download/Device/Mac(具体)/Face.xml</Url>
<Md5>xxxxxxxx</XmlMd5>
.</Params>
</Msg>
*/
int CMsgHandle::ParseDownUploadFacePicMsg(char *pszBuf, SOCKET_MSG_DOWNUPLOAD_FACE_PIC *pDownUploadFacePicMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pDownUploadFacePicMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDownUploadFacePicMsg, 0, sizeof(SOCKET_MSG_DOWNUPLOAD_FACE_PIC));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_URL) == 0)
				{
					rl_strcpy_s(pDownUploadFacePicMsg->szUrl, sizeof(pDownUploadFacePicMsg->szUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MD5) == 0)
				{
					rl_strcpy_s(pDownUploadFacePicMsg->szMD5, sizeof(pDownUploadFacePicMsg->szMD5), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}
#endif

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CMsgHandle::ParseReqCfgFromDeviceMsg(char *pszBuf, SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pRequestConfigMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize,TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pRequestConfigMsg, 0, sizeof(SOCKET_MSG_CONFIG_FROM_DEVICE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_FROM) == 0)
		{		
			rl_strcpy_s(pRequestConfigMsg->szFromIP, sizeof(pRequestConfigMsg->szFromIP), pNode->GetText());			
		}
		else if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TO) == 0)
		{		
			rl_strcpy_s(pRequestConfigMsg->szToIP, sizeof(pRequestConfigMsg->szToIP), pNode->GetText());		
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) == 0)
				{
					rl_strcpy_s(pRequestConfigMsg->module.szItem[nItemIndex], sizeof(pRequestConfigMsg->module.szItem[nItemIndex]), pSubNode->GetText());
					nItemIndex++;
				}
			}
		}
	}
	return 0;
}
#endif

int CMsgHandle::ParseManageAlarmMsg(char *pszBuf, SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pManageAlarmMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pManageAlarmMsg, 0, sizeof(SOCKET_MSG_MANAGE_ALARM_MSG));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{		
			rl_strcpy_s(pManageAlarmMsg->szType, sizeof(pManageAlarmMsg->szType), pNode->GetText());						
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
//			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ID) == 0)
				{
					pManageAlarmMsg->nID = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_MSG) == 0)
				{
					rl_strcpy_s(pManageAlarmMsg->szAlarmMsg, sizeof(pManageAlarmMsg->szAlarmMsg), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_FROM_NAME) == 0)
				{
					rl_strcpy_s(pManageAlarmMsg->szFromName, sizeof(pManageAlarmMsg->szFromName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_APT) == 0)
				{
					rl_strcpy_s(pManageAlarmMsg->szApt, sizeof(pManageAlarmMsg->szApt), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TIME) == 0)
				{
					rl_strcpy_s(pManageAlarmMsg->szTime, sizeof(pManageAlarmMsg->szTime), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
				{
					rl_strcpy_s(pManageAlarmMsg->szMAC, sizeof(pManageAlarmMsg->szMAC), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CODE) == 0)
				{
					pManageAlarmMsg->unAlarmCode= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CUSTOMIZE) == 0)
				{
					pManageAlarmMsg->unAlarmCustomize= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_LOCATION) == 0)
				{
					pManageAlarmMsg->unAlarmLocation= rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_ZONE) == 0)
				{
					pManageAlarmMsg->unAlarmZone= rl_atoi(pSubNode->GetText());
				}
				
			}
		}
	}
	return 0;
}

int CMsgHandle::ParseMaintenaceServerChange(char *pszBuf, SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange, INT nDataSize)
{
	if((pszBuf == NULL) || (pMaintenanceSrvChange == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pMaintenanceSrvChange, 0, sizeof(SOCKET_MSG_MAINTENANCE_SERVER_CHANGE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
//			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_SERVER_TYPE) == 0)
				{
					rl_strcpy_s(pMaintenanceSrvChange->szSrvType, sizeof(pMaintenanceSrvChange->szSrvType), pSubNode->GetText());
				}				
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>RequestSensorTrigger</Type>
  <Params>
	<Mode>0</Mode>
  </Params>
</Msg>
*/
int CMsgHandle::ParseRequestSensorTrigger(char *pszBuf, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, INT nDataSize)
{
	if((pszBuf == NULL) || (pSensorTriggerMsg == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pSensorTriggerMsg, 0, sizeof(DCLIENT_SENSOR_TRIGGER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
//			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_MODE) == 0)
				{
					pSensorTriggerMsg->nMode = rl_atoi(pSubNode->GetText());
				}	
			}
		}
	}
	return 0;
}

int CMsgHandle::ParseRequestAllTrigger(char *pszBuf, SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger, int nDataSize)
{
	if((pszBuf == NULL) || (pRequestAllTrigger == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pRequestAllTrigger, 0, sizeof(SOCKET_MSG_REQUEST_ALL_TRIGGER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
//			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_FROM) == 0)
				{
					rl_strcpy_s(pRequestAllTrigger->szFromIP, sizeof(pRequestAllTrigger->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_TO) == 0)
				{
					rl_strcpy_s(pRequestAllTrigger->szToIP, sizeof(pRequestAllTrigger->szToIP), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

int CMsgHandle::ParseReportAllTrigger(char *pszBuf, SOCKET_MSG_REPORT_ALL_TRIGGER *pReportAllTrigger, int nDataSize)
{
	if((pszBuf == NULL) || (pReportAllTrigger == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReportAllTrigger, 0, sizeof(SOCKET_MSG_REPORT_ALL_TRIGGER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
//			int nItemIndex = 0;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_FROM) == 0)
				{
					rl_strcpy_s(pReportAllTrigger->szFromIP, sizeof(pReportAllTrigger->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_TO) == 0)
				{
					rl_strcpy_s(pReportAllTrigger->szToIP, sizeof(pReportAllTrigger->szToIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ALARM_CH_EXCEPTION) == 0)
				{
					rl_strcpy_s(pReportAllTrigger->szAlarmChException, sizeof(pReportAllTrigger->szAlarmChException), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
/*
<Msg>
  <Type>ReportDoorStatus</Type>
  <Params>
	<IP>*************</IP>
	<Status>0</Status>
  </Params>
</Msg>
*/
int CMsgHandle::ParseReportDoorStatus(char *pszBuf, DCLIENT_REPORT_DOORSTATUS *pDoorStatus, INT nDataSize)
{
	if((pszBuf == NULL) || (pDoorStatus == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pDoorStatus, 0, sizeof(DCLIENT_REPORT_DOORSTATUS));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_STATUS) == 0)
				{
					pDoorStatus->nDoorState = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
				{
					rl_strcpy_s(pDoorStatus->szIPAddr, sizeof(pDoorStatus->szIPAddr), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}
#endif

/*
<Msg>
  <Type>VisitorAuthNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Count>5</Count> //授权TempKey可用次数
  </Params>
</Msg>
*/
int CMsgHandle::ParseNotifyVisitorAuthMsg(char *pszBuf, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuth, INT nDataSize)
{
	if((pszBuf == NULL) || (pReportVisitorAuth == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pReportVisitorAuth, 0, sizeof(DCLIENT_REPORT_VISITOR_AUTH_INFO));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_COUNT) == 0)
				{
					pReportVisitorAuth->nCount= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*<Msg>
  <Type>FaceDataMsg</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <Url>http://10.66.95.45:8090/group1/M00/00/E3/CkJfLV2kRn2AAZAnAAACFIWl0DU.tar.gz</Url> //人脸模型数据下载链接
  </Params>
</Msg>
*/
int CMsgHandle::ParseFaceDataForwardMsg(char *pszBuf, DCLIENT_FACE_DATA_FORWARD *pFaceDataForward, INT nDataSize)
{
	if((pszBuf == NULL) || (pFaceDataForward == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByDefault(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pFaceDataForward, 0, sizeof(DCLIENT_FACE_DATA_FORWARD));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_URL) == 0)
				{
					rl_strcpy_s(pFaceDataForward->szUrl, sizeof(pFaceDataForward->szUrl), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>VisitorTempKeyAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <TmpKey>91234567</TmpKey> 
  </Params>
</Msg>
*/
int CMsgHandle::ParseSendTempKeyMsg(char *pszBuf, DCLIENT_SEND_TEMP_KEY *pSendTempKey, INT nDataSize)
{
	if((pszBuf == NULL) || (pSendTempKey == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pSendTempKey, 0, sizeof(DCLIENT_SEND_TEMP_KEY));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_TMPKEY) == 0)
				{
					rl_strcpy_s(pSendTempKey->szTempKey, sizeof(pSendTempKey->szTempKey), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>SendOssSts</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AccessKeySecret>FWhYyABStQjhzCN2GEJkcYjdo74HorwiHRwGwpdyL5oE</AccessKeySecret>
     <AccessKeyId>STS.NUTpZMxReR9PRA9noet1DC2ZZ</AccessKeyId>
     <SecurityToken>CAISpgJ1q6Ft5B2yfSjIr5bhO+D5lY1E5fu7UGeIim8weL5orPfxuDz2IH1NdHFhBusevvozm25Y6f8flqJ4T55IQ1Dza8J148yvAoVM4M+T1fau5Jko1bexewHKeQGZsebWZ+LmNpy/Ht6md1HDkAJq3LL+bk/Mdle5MJqP+/kFC9MMRVuAcCZhDtVbLRcYha18D3bKMuu3ORPHm3fZCFES2jBxkmRi86+ysIz+pRPVlw/90fRH5dazcIOpdddhJtJ7VNjw1uV9de3v2SVdxxVA6agxzo48oG6X44DEUwMOvE/ZbbqKrucCdlEpOvIIfIdft+X5mPFCvejeqp/60R4lP5sODn2PHNH/m5OURr/2aIZmLOvhWXPWyduIMIXvtAc0ybPqsYrwnrAagAFFJMRmWPy1UAQcdtTXUXQSklrdxJPrZZ+dSxV9avYL7uyBjwY5hQ4w3ptajO+Uuns4XD1T0Y0B8BuAJLUVtvGIwJOAsiMwTmj5pDHmRUGTUVyO+OogcxafDSNfhzsXKbGw1LqxAJdZxuFlxscgHapJqdF8pR6mvMMESsZoNB8Enw==</SecurityToken>
     <Bucket>server-log-back</Bucket>
     <Endpoint>https://oss-ap-southeast-1.aliyuncs.com</Endpoint>
  </Params>
</Msg>
*/
int CMsgHandle::ParseSendOssStsMsg(char *pszBuf, DCLIENT_SEND_OSS_STS *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_SEND_OSS_STS));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ACCESS_KEYSECRET) == 0)
				{
					rl_strcpy_s(pData->szAccessKeySecret, sizeof(pData->szAccessKeySecret), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ACCESS_KEYID) == 0)
				{
					rl_strcpy_s(pData->szAccessKeyId, sizeof(pData->szAccessKeyId), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_TOKEN) == 0)
				{
					rl_strcpy_s(pData->szSecurityToken, sizeof(pData->szSecurityToken), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_BUCKET) == 0)
				{
					rl_strcpy_s(pData->szBucket, sizeof(pData->szBucket), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_ENDPOINT) == 0)
				{
					rl_strcpy_s(pData->szEndpoint, sizeof(pData->szEndpoint), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*	
<Msg>
  <Params>
  <Port>1080</Port>
  <Password>232dchdnfddxxxx</Password>
  <Username>root</Username>
  <SSHPort>222</SSHPort>
  </Params>
</Msg>
*/
int CMsgHandle::ParseRemoteAccessWeb(char *pszBuf, DCLIENT_REMOTE_ACCESS_WEB *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REMOTE_ACCESS_WEB));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PORT) == 0)
				{
					pData->nPort = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_PASSWORD) == 0)
				{
					rl_strcpy_s(pData->szPassword, sizeof(pData->szPassword), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_USER_NAME) == 0)
				{
					rl_strcpy_s(pData->szUserName, sizeof(pData->szUserName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_SSHPORT) == 0)
				{
					pData->nSSHPort= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*	
<Msg>
  <Type>OpenDoorAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <result>1</result>  //是否开门成功
  </Params>
</Msg>

*/
int CMsgHandle::ParseOpenDoorACK(char *pszBuf, DCLIENT_OPENDOOR_ACK *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_OPENDOOR_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
				{
					pData->nResult= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>  
    <Type>RegisterFace</Type>  
    <Params>  
        <Url>ftp://************8/photo/xxx.jpg</Url> 
         <PICMD5>xxxxxxxxxxxx</PICMD5>  
        <Name>Jeffrey</Name>  
        <DoorNum>1234</DoorNum>  
        <Week>1111011</Week>  
        <TimeStart>00:01</TimeStart>  
        <TimeEnd>23:59</TimeEnd>  
        <ID>123</ID>  
    </Params>  
</Msg>
*/
int CMsgHandle::ParseRegisterFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_OPENDOOR_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_URL) == 0)
				{
					rl_strcpy_s(pData->szURL, sizeof(pData->szURL), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "PICMD5") == 0)
				{
					rl_strcpy_s(pData->szPICMD5, sizeof(pData->szPICMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Name") == 0)
				{
					rl_strcpy_s(pData->szName, sizeof(pData->szName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "DoorNum") == 0)
				{
					rl_strcpy_s(pData->szDoorNum, sizeof(pData->szDoorNum), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Week") == 0)
				{
					rl_strcpy_s(pData->szWeek, sizeof(pData->szWeek), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TimeStart") == 0)
				{
					rl_strcpy_s(pData->szTimeStart, sizeof(pData->szTimeStart), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TimeEnd") == 0)
				{
					rl_strcpy_s(pData->szTimeEnd, sizeof(pData->szTimeEnd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ID") == 0)
				{
					pData->nID= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>  
    <Type>ModifyFace</Type>  
    <Params>  
        <Url>ftp://************8/photo/xxx.jpg</Url> 
         <PICMD5>xxxxxxxxxxxx</PICMD5>  
        <Name>Jeffrey</Name>  
        <DoorNum>1234</DoorNum>  
        <Week>1111011</Week>  
        <TimeStart>00:01</TimeStart>  
        <TimeEnd>23:59</TimeEnd>  
        <ID>123</ID>  
    </Params>  
</Msg>
*/
int CMsgHandle::ParseModifyFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_OPENDOOR_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), XML_NODE_NAME_MSG_PARAM_URL) == 0)
				{
					rl_strcpy_s(pData->szURL, sizeof(pData->szURL), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "PICMD5") == 0)
				{
					rl_strcpy_s(pData->szPICMD5, sizeof(pData->szPICMD5), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Name") == 0)
				{
					rl_strcpy_s(pData->szName, sizeof(pData->szName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "DoorNum") == 0)
				{
					rl_strcpy_s(pData->szDoorNum, sizeof(pData->szDoorNum), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Week") == 0)
				{
					rl_strcpy_s(pData->szWeek, sizeof(pData->szWeek), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TimeStart") == 0)
				{
					rl_strcpy_s(pData->szTimeStart, sizeof(pData->szTimeStart), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "TimeEnd") == 0)
				{
					rl_strcpy_s(pData->szTimeEnd, sizeof(pData->szTimeEnd), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ID") == 0)
				{
					pData->nID= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>  
    <Type>DeleteFace</Type>  
    <Params>  
        <Name>Jeffrey</Name>  
        <ID>123</ID>  
    </Params>  
</Msg>
*/
int CMsgHandle::ParseDeleteFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT	
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_OPENDOOR_ACK));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Name") == 0)
				{
					rl_strcpy_s(pData->szName, sizeof(pData->szName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ID") == 0)
				{
					pData->nID= rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
	<Type>GSFaceLogin</Type>
	<Params>
		<HttpApi>http://192.168.12.214/gsface/v1/login</HttpApi>
	</Params>
</Msg>
*/
int CMsgHandle::ParseGSFaceHttpApiLogin(char *pszBuf, SOCKET_MSG_GSFACE_HTTPAPI *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(SOCKET_MSG_GSFACE_HTTPAPI));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
		{
			
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "HttpApi") == 0)
				{
					rl_strcpy_s(pData->szURL, sizeof(pData->szURL), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
    <Type>RequestPersonelData</Type>
    <Params>
    		<Url>tftp://************/Upload/[MAC]/</Url>
    </Params>
</Msg>
*/
int CMsgHandle::ParseRequestPersonelData(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Url") == 0)
				{
					rl_strcpy_s(pData->szUrl, sizeof(pData->szUrl), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
    <Type>SyncPersonelData</Type>
    <Params>     
        <Url>tftp://************/Upload/[MAC]/PersonelData.tar.gz</Url> 
    </Params>
</Msg>

*/
int CMsgHandle::ParseSyncPersonelData(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Url") == 0)
				{
					rl_strcpy_s(pData->szUrl, sizeof(pData->szUrl), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}


/*
<Msg>
    <Type>RequestFingerPrint</Type>
    <Params>
    		<Url>tftp://************/Upload/[MAC]/</Url>
    </Params>
</Msg>
*/
int CMsgHandle::ParseRequestFingerPrint(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Url") == 0)
				{
					rl_strcpy_s(pData->szUrl, sizeof(pData->szUrl), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
    <Type>SyncFingerPrint</Type>
    <Params>     
        <Url>tftp://************/Upload/[MAC]/FingerPrint.tar.gz</ Url> 
    </Params>
</Msg>
*/
int CMsgHandle::ParseSyncFingerPrint(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Url") == 0)
				{
					rl_strcpy_s(pData->szUrl, sizeof(pData->szUrl), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
    <Type>NotifyAttendanceService</Type>
    <Params>     
        <Service>192.168.12.214</Service> 
    </Params>
</Msg>
*/
int CMsgHandle::ParseNotifyAttendanceSrv(char *pszBuf, DCLIENT_NOTIFY_ATTENDANCE_SERVICE *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_NOTIFY_ATTENDANCE_SERVICE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Service") == 0)
				{
					rl_strcpy_s(pData->szIP, sizeof(pData->szIP), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
    <Type>RequestKeepOpenRelay</Type>
    <Params>     
        <Relay>1234</Relay> 
    </Params>
</Msg>
*/
int CMsgHandle::ParseRequestKeepRelayOpenClose(char *pszBuf, DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Relay") == 0)
				{
					rl_strcpy_s(pData->szRelay, sizeof(pData->szRelay), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>  
  <Type>BackupConfig</Type>  
  <Params>  
    <UploadPath>tftp://************:2069/Upload/Backup/0C1105152685/</UploadPath> //上传的路径  
    <FileName>20210816141055_856968.conf</FileName>  // 文件名称，按照时间戳+6位随机数命名
  </Params>  
</Msg>
*/
int CMsgHandle::ParseBackupConfig(char *pszBuf, DCLIENT_BACKUP_CONFIG *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "UploadPath") == 0)
				{
					rl_strcpy_s(pData->szUploadPath, sizeof(pData->szUploadPath), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "FileName") == 0)
				{
					rl_strcpy_s(pData->szFileName, sizeof(pData->szFileName), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>  
  <Type>BackupConfigRecovery</Type>  
  <Params>  
    <DownloadPath>tftp://************:2069/Upload/Backup/0C1105152685/xxxx.tgz</UploadPath> //下载的文件  
    <MD5>202108161410558585458545548545</MD5>  //下载的文件MD5
  </Params>  
</Msg>
*/
int CMsgHandle::ParseBackupConfigRecovery(char *pszBuf, SOCKET_MSG_BACKUP_CONFIG_RECOVERY *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "DownloadPath") == 0)
				{
					rl_strcpy_s(pData->szURL, sizeof(pData->szURL), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "MD5") == 0)
				{
					rl_strcpy_s(pData->szMD5, sizeof(pData->szMD5), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>RequestRtspMonitor</Type>
  <Params>
    <FromIP>************</FromIP>        //本地IP
    <ToIP>************</ToIP>  //目标IP
 <Seq>1258596585</Seq>  //一个10位的随机数，用来区别唯一的监控
 <RTSP>rtsp://************/chr_1</RTSP>  //rtsp地址
 <User>admin</User> //用户名
 <Pwd>admin123</Pwd> //密码
  </Params>
</Msg>
*/
int CMsgHandle::ParseRequestRtspMonitor(char *pszBuf, DCLIENT_REQUEST_RTSP_MONITOR *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REQUEST_RTSP_MONITOR));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "FromIP") == 0)
				{
					rl_strcpy_s(pData->szFromIP, sizeof(pData->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ToIP") == 0)
				{
					rl_strcpy_s(pData->szToIP, sizeof(pData->szToIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Seq") == 0)
				{
					rl_strcpy_s(pData->szSeq, sizeof(pData->szSeq), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "RTSP") == 0)
				{
					rl_strcpy_s(pData->szRTSP, sizeof(pData->szRTSP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "User") == 0)
				{
					rl_strcpy_s(pData->szUser, sizeof(pData->szUser), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Pwd") == 0)
				{
					rl_strcpy_s(pData->szPassword, sizeof(pData->szPassword), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>RtspMonitorStop</Type>
  <Params>
    <FromIP>************</FromIP>        //本地IP
    <ToIP>************</ToIP>  //目标IP
 <Seq>1258596585</Seq>  //一个10位的随机数，用来区别唯一的监控
  </Params>
</Msg>
*/
int CMsgHandle::ParseRtspMonitorStop(char *pszBuf, DCLIENT_RTSP_MONITOR_STOP *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize, TRUE);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_RTSP_MONITOR_STOP));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "FromIP") == 0)
				{
					rl_strcpy_s(pData->szFromIP, sizeof(pData->szFromIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "ToIP") == 0)
				{
					rl_strcpy_s(pData->szToIP, sizeof(pData->szToIP), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Seq") == 0)
				{
					rl_strcpy_s(pData->szSeq, sizeof(pData->szSeq), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
  <Type>RegEndUser</Type>
  <Params>
    <Account>1234</Account> //enduser 账号
    <RegUrl>xxxx/stdemo.html?account=xxxxxxx</RegUrl>  //注册地址
  </Params>
</Msg>

*/
int CMsgHandle::ParseRegEndUser(char *pszBuf, DCLIENT_REG_END_USER *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REG_END_USER));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Account") == 0)
				{
					rl_strcpy_s(pData->szAccount, sizeof(pData->szAccount), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "RegUrl") == 0)
				{
					rl_strcpy_s(pData->szRegUrl, sizeof(pData->szRegUrl), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Status") == 0)
				{
					pData->nStatus = rl_atoi(pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "AccountName") == 0)
				{
					rl_strcpy_s(pData->szAccountName, sizeof(pData->szAccountName), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "Email") == 0)
				{
					rl_strcpy_s(pData->szEmail, sizeof(pData->szEmail), pSubNode->GetText());
				}
				else if(rl_strcmp(pSubNode->Value(), "MobileNumber") == 0)
				{
					rl_strcpy_s(pData->szMobileNum, sizeof(pData->szMobileNum), pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
<Type>RequestIsKit</Type>
<Params>

<IsKit>0</IsKit> //0:不是kit方案;1:是kit方案
</Params>
</Msg>
*/
int CMsgHandle::ParseRequestIsKit(char *pszBuf, INT& nKitFlag, INT nDataSize)
{
	if((pszBuf == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}

	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "IsKit") == 0)
				{
					nKitFlag = rl_atoi(pSubNode->GetText());
				}
			}
		}
	}
	return 0;
}

/*
<Msg>
<Type>ReportKitDevices</Type>
<Params>
<Item>   
<MAC>0A:02:03:20:01:17</MAC>
<Location> XXX </Location>
<Type> 1 </Type>  0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机;  5:SDMC;  50: 门禁
</Item>
....
<Item> 同上 </Item>
</Params>
</Msg>
*/
int CMsgHandle::ParseReportKitDevices(char *pszBuf, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, INT nDataSize)
{
	if((pszBuf == NULL) || (pData == NULL))
	{
		return -1;
	}
#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
	AesDecryptByMac(pszBuf, pszBuf, nDataSize);
#endif
	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszBuf);

	memset(pData, 0, sizeof(DCLIENT_REPORT_KIT_DEVICE_LIST));

	TiXmlDocument doc;
	if (!doc.LoadBuffer(pszBuf))
	{
		rl_log_err("%s:XML LoadBuffer failed.", __FUNCTION__);
		return -1;
	}
	TiXmlElement* pRootNode = doc.RootElement();
	TiXmlElement* pNode = NULL;
	if (NULL == pRootNode)
	{
		rl_log_err("%s: ROOT Node is NULL", __FUNCTION__);
		return -1;
	}

	//主节点的名称如果不是Msg则跳过
	if(rl_strcmp(pRootNode->Value(), XML_NODE_NAME_MSG) != 0)
	{
		rl_log_err("%s: mismatched %s", __FUNCTION__, XML_NODE_NAME_MSG);
		return -1;
	}
	
	int nIndex = 0;
	for (pNode = pRootNode->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())  
	{
		if(rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
		{
			//暂不需要处理
		}
		else if (rl_strcmp(pNode->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
		{
			TiXmlElement* pSubNode = NULL;
			for (pSubNode = pNode->FirstChildElement(); pSubNode; pSubNode = pSubNode->NextSiblingElement())  
			{
				if(rl_strcmp(pSubNode->Value(), "Item") == 0)
				{
					if(nIndex >= KIT_MAX_SEND_COUNT)
					{
						break;
					}
					TiXmlElement* pSubItemNode = NULL;
					for (pSubItemNode = pSubNode->FirstChildElement(); pSubItemNode; pSubItemNode = pSubItemNode->NextSiblingElement())
					{					
						if(rl_strcmp(pSubItemNode->Value(), "MAC") == 0)
						{
							rl_strcpy_s(pData->deviceInfo[nIndex].szMAC, sizeof(pData->deviceInfo[nIndex].szMAC), pSubItemNode->GetText());
						}
						else if(rl_strcmp(pSubItemNode->Value(), "Location") == 0)
						{
							rl_strcpy_s(pData->deviceInfo[nIndex].szLocation, sizeof(pData->deviceInfo[nIndex].szLocation), pSubItemNode->GetText());
						}
						else if(rl_strcmp(pSubItemNode->Value(), "Type") == 0)
						{
							pData->deviceInfo[nIndex].nType = rl_atoi(pSubItemNode->GetText());
						}						
					}
				}
				nIndex++;
			}
		}
	}
	return 0;
}

int CMsgHandle::AesEncryptByMac(char *pIn, char *pOut, INT *pDataSize, BOOL bForceMac)
{
	if(pIn == NULL || pOut == NULL || pDataSize == NULL)
	{
		return -1;
	}

	//对MSG进行AES加密
	int nLenth = strlen(pIn);
	if(nLenth%16 != 0)
	{
		nLenth -= nLenth%16;
		nLenth += 16;
	}	
	*pDataSize = nLenth;
	char *pszOutBuf = new char[*pDataSize + 1];//AES加密会补齐16字节，所以需要+16，防止出错
	memset(pszOutBuf, 0, *pDataSize + 1);
	//获取Mac地址
	char szMac[MAC_SIZE] = {0};
	if(bForceMac)
	{
		rl_sprintf_s(szMac, sizeof(szMac), "%s", AES_KEY_DEFAULT_MAC);
	}
	else
	{
		char szMacTmp[MAC_SIZE] = {0};
		GetSettingHandleInstance()->GetMAC(szMacTmp, sizeof(szMacTmp));
		StrtokString(szMacTmp, szMac, MAC_SIZE, ":");
	}
	//生成key
	char szKey[KEY_LENGTH+1] = {0};
	char szTmpKey[KEY_LENGTH+1] = {0};
	rl_sprintf_s(szTmpKey, sizeof(szTmpKey), "%s%s", szMac, AES_KEY_DEFAULT_MASK);
	for(int i=0; i<KEY_LENGTH+1; i++)
	{
		szKey[i] = toupper(szTmpKey[i]);
	}
	AES_256_ENCRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)szKey, *pDataSize);
	memcpy(pOut, pszOutBuf, *pDataSize + 1);
	delete []pszOutBuf;
	return 0;
}

int CMsgHandle::AesEncryptByDefault(char *pIn, char *pOut, INT *pDataSize)
{
	if(pIn == NULL || pOut == NULL || pDataSize == NULL)
	{
		return -1;
	}

	//对MSG进行AES加密
	int nLenth = rl_strlen(pIn);
	if(nLenth%16 != 0)
	{
		nLenth -= nLenth%16;
		nLenth += 16;
	}	
	*pDataSize = nLenth;
	char *pszOutBuf = new char[*pDataSize + 1];//AES加密会补齐16字节，所以需要+16，防止出错
	memset(pszOutBuf, 0, *pDataSize + 1);
	
	AES_256_ENCRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, *pDataSize);
	memcpy(pOut, pszOutBuf, *pDataSize + 1);
	delete []pszOutBuf;	
	return 0;
}

int CMsgHandle::AesDecryptByMac(char *pIn, char *pOut, INT nDataSize, BOOL bForceMac)
{
	if(pIn == NULL || pOut == NULL)
	{
		return -1;
	}
	//对MSG进行AES解密
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);
	
	//获取Mac地址
	char szMac[MAC_SIZE] = {0};
	if(bForceMac)
	{
		rl_sprintf_s(szMac, sizeof(szMac), "%s", AES_KEY_DEFAULT_MAC);
	}
	else
	{
		char szMacTmp[MAC_SIZE] = {0};
		GetSettingHandleInstance()->GetMAC(szMacTmp, sizeof(szMacTmp));
		StrtokString(szMacTmp, szMac, MAC_SIZE, ":");
	}
	//生成key
	char szKey[KEY_LENGTH+1] = {0};
	char szTmpKey[KEY_LENGTH+1] = {0};
	rl_sprintf_s(szTmpKey, sizeof(szTmpKey), "%s%s", szMac, AES_KEY_DEFAULT_MASK);
	for(int i=0; i<KEY_LENGTH+1; i++)
	{
		szKey[i] = toupper(szTmpKey[i]);
	}
	AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)szKey, nDataSize);
	
	memcpy(pOut, pszOutBuf, rl_strlen(pszOutBuf)+1);
	delete []pszOutBuf;
	return 0;
}

int CMsgHandle::AesDecryptByDefault(char *pIn, char *pOut, INT nDataSize, char *pOffset)
{
	if(pIn == NULL || pOut == NULL)
	{
		return -1;
	}

	//对MSG进行AES解密
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);
	if(pOffset == NULL)
	{
		AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, nDataSize);	
	}
	else
	{
		AES_256_DECRYPT_OFFSET((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, pOffset, nDataSize);
	}
	memcpy(pOut, pszOutBuf, rl_strlen(pszOutBuf)+1);
	delete []pszOutBuf;
	return 0;
}


/*
写设备地址表到文件中
<Devices>
	<Item Name="">5_0.0.0.0.0-1=***********</Item>
	<Item Name="">1_*******.1-1=***********0</Item>
	<Item Name="">1_*******.2-1=***********1</Item>
</Devices>
*/
int CMsgHandle::CreateAddressFile(const char *pszFile, DISCOVER_DEVICE_ADDR *pDeviceList)
{
	if(pszFile == NULL)
	{
		return -1;
	}
	
	int nSize = XML_ADDR_FILE_SIZE;
	char *pszFileBuf = new char[nSize];
	memset(pszFileBuf, 0, nSize);


	char szTmpLine[XML_NODE_LINE_SIZE] = {0};
	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "<Devices>\r\n");
	rl_strcat_s(pszFileBuf, nSize, szTmpLine);

	DISCOVER_DEVICE_ADDR *pCurNode = pDeviceList;
	while(pCurNode != NULL)
	{
		rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "\t<Item Name=\"%s\" MAC=\"%s\">%d_%s-%d=%s</Item>\r\n", 
			XmlTrans(pCurNode->szName).data(),
			XmlTrans(pCurNode->szMac).data(),
			pCurNode->nType,
			XmlTrans(pCurNode->szDeviceID).data(),
			pCurNode->nExtension,
			XmlTrans(pCurNode->szIP).data());

		rl_strcat_s(pszFileBuf, nSize, szTmpLine);

		pCurNode = pCurNode->next;
	}

	rl_sprintf_s(szTmpLine, sizeof(szTmpLine), "</Devices>\r\n");
	rl_strcat_s(pszFileBuf, nSize, szTmpLine);

	rl_log_debug("%s:\r\n%s", __FUNCTION__, pszFileBuf);


	//写入文件
	int fd = open(pszFile, O_RDWR | O_CREAT | O_TRUNC);
	if (fd < 0 )
	{
		rl_log_err("%s:open %s failed.", __FUNCTION__, pszFile);
		delete []pszFileBuf;
		return -1;
	}

	if(write(fd, pszFileBuf, rl_strlen(pszFileBuf) + 1) < 0)
	{
		rl_log_err("%s: write failed.", __FUNCTION__);
		close(fd);
		delete []pszFileBuf;
		return -1;
	}
	close(fd);
	
	delete []pszFileBuf;

#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
	char cmd[1024] = {0};
	rl_sprintf_s(cmd, sizeof(cmd), "chmod 777 \"%s\"", pszFile);
	if(rl_system_100ms_ex(cmd, 3, 1) < 0)
	{
		return -1;
	}
#endif
	return 0;
}

std::string CMsgHandle::XmlTrans(char *pszItem)
{
	char szItem[XML_NODE_LINE_SIZE*4] = {0};
	if(pszItem == NULL)
	{
		return szItem;
	}
	int nSize = rl_strlen(pszItem);
	int nItemPos = 0;
	for(int i=0; i<nSize; i++)
	{
		CHAR cTmp = pszItem[i];
		switch(cTmp)
		{
		case '&':
			{
				rl_strcat_s(szItem+nItemPos, sizeof(szItem), "&amp;");
				nItemPos+=5;
			}
			break;
		case '<':
			{
				rl_strcat_s(szItem+nItemPos, sizeof(szItem), "&lt;");
				nItemPos+=4;
			}
			break;
		case '>':
			{
				rl_strcat_s(szItem+nItemPos, sizeof(szItem), "&gt;");
				nItemPos+=4;
			}
			break;
		case '\'':
			{
				rl_strcat_s(szItem+nItemPos, sizeof(szItem), "&apos;");
				nItemPos+=6;
			}
			break;
		case '\"':
			{
				rl_strcat_s(szItem+nItemPos, sizeof(szItem), "&quot;");
				nItemPos+=6;
			}
			break;
		default:
			rl_strcat_s(szItem+nItemPos, 2, pszItem+i);
			nItemPos+=1;
			break;			
		}
	}
	std::string strItem = szItem;
	return strItem;
}

