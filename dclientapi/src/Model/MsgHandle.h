#ifndef __MSG_HANDLE_H__
#define __MSG_HANDLE_H__

#include "DclientIncludes.h"
#include "dclient_ipc.h"
#include <string.h>
#include <string>
using namespace std;

#pragma once

class CMsgHandle
{
public:
	CMsgHandle();
	~CMsgHandle();
	
	int BuildBootupMsg(char *pszBuf, int nSize, SOCKET_MSG_BOOTUP *pBootupMsg, INT *pDataSize);
	int BuildReportStatusMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_STATUS *pReportStatusMsg, int *pDataSize);
	int BuildReportConfigMsg(char *pszBuf, int nSize, SOCKET_MSG_CONFIG *pReportConfigMsg, int *pDataSize, BOOL bForceMac = FALSE);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int BuildReportConfigMsg(char *pszBuf, int nSize, SOCKET_MSG_CONFIG_FROM_DEVICE* pReportConfigMsg, int *pDataSize, BOOL bForceMac = FALSE);
#endif
	int BuildAlarmMsg(char *pszBuf, int nSize, SOCKET_MSG_ALARM *pAlarmMsg, int *pDataSize);
	int BuildAlarmDealMsg(char *pszBuf, int nSize, SOCKET_MSG_ALARM_DEAL *pAlarmDealMsg, int *pDataSize);
	int BuildTextMsg(char *pszBuf, int nSize, SOCKET_MSG_TEXT_MESSAGE *pTextMessage, int *pDataSize);
	int BuildAccessInfo(char *pszBuf, int nSize, SOCKET_MSG_ACCESS_INFO *pAccessInfo, int *pDataSize);
#if RL_GLOBAL_SUPPORT_TMP_KEY	
	int BuildCheckTmpKey(char *pszBuf, int nSize, SOCKET_MSG_CHECK_TMP_KEY *pCheckTmpKey, int *pDataSize);
#endif	
	int ParseReqConnMsg(char *pszBuf, SOCKET_MSG_REQ_CONN *pReqConnMsg, INT nDataSize);
	int ParseReqStatusMsg(char *pszBuf, SOCKET_MSG_REQ_STATUS *pReqStatusMsg, INT nDataSize);
	int ParseRemoteControlMsg(char *pszBuf, SOCKET_MSG_REMOTE_CONTROL *pRemoteControlMsg, INT nDataSize);
	int ParseReqConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG *pReqConfigMsg, INT nDataSize, BOOL bForceMac = FALSE);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int ParseReqConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG_FROM_DEVICE *pReqConfigMsg, int nDataSize, BOOL bForceMAC = FALSE);
#endif
	int ParseUpdateConfigMsg(char *pszBuf, SOCKET_MSG_CONFIG *pUpdateConfigMsg, INT nDataSize);
	int ParseUpgradeStartMsg(char *pszBuf, SOCKET_MSG_UPGRADE_START *pUpgradeStartMsg, INT nDataSize);
	int ParseFileEndMsg(char *pszBuf, SOCKET_MSG_FILE_END *pFileEndMsg, INT nDataSize);
	int BuildCheckKeyMsg(char *pszBuf, int nSize, SOCKET_MSG_CHECK_KEY *pCheckKeyMsg, int *pDataSize);
	int BuildACKMsg(char *pszBuf, int nSize, SOCKET_MSG_ACK *pAckMsg, int *pDataSize);
	int BuildGetBindCodeRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, INT *pDataSize);
	int BuildUnBindCodeRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, char *pszBindCode, INT *pDataSize);
	int BuildGetBindCodeListRequestMsg(char *pszBuf, int nSize, char *pszSerialNum, INT *pDataSize);
	int BuildDiscoverMsg(char *pszBuf, int nSize, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, int *pDataSize);
	int BuildDiscoverAckMsg(char *pszBuf, int nSize, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, int *pDataSize);	
#if RL_SUPPORT_CLOUD_DEV_INFO	
	int BuildRequestDeviceListMsg(char *pszBuf, int nSize, SOCKET_MSG_REQUEST_DEVICE_LIST *pRequestDeviceListMsg, INT *pDataSize);
#endif
#if RL_SUPPORT_ARMING || RL_SUPPORT_ARMING_P2P
	int BuildReportArmingMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_ARMING *pReportArmingMsg, INT *pDataSize, BOOL bForceMAC = FALSE);
#endif
#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	int BuildPushForwardNumberMsg(char *pszBuf, int nSize, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumerMsg, INT *pDataSize);
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER	
	int BuildReportForwardNumberMsg(char *pszBuf, int nSize, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumber, INT *pDataSize);
#endif	
#if RL_SUPPORT_REPORT_ACTIVITY
	int BuildMotionAlertMsg(char *pszBuf, int nSize, SOCKET_MSG_MOTION_ALERT *pMotionAlertMsg, INT *pDataSize);
	int BuildReportActivityMsg(char *pszBuf, int nSize, DCLIENT_REPORT_ACTIVITY *pReportActivityMsg, INT *pDataSize);
#endif
	int BuildRequestArming(char *pszBuf, int nSize, SOCKET_MSG_REQUEST_ARMING *pRequestArming, int *pDataSize, BOOL bForceMAC = FALSE);
	int BuildCheckDtmfMsg(char *pszBuf, int nSize, SOCKET_MSG_CHECK_DTMF*pCheckDtmfMsg, int *pDataSize);
#if RL_SUPPORT_DTMF_SET
	int BuildSendDTMFSetMsg(char *pszBuf, int nSize, SOCKET_MSG_SEND_DTMF_SET*pSendDtmfSetMsg, int *pDataSize);
#endif
#if RL_SUPPORT_DEVICE_MAINTENANCE
	int BuildCliCommandRespMsg(char *pszBuf, int nSize, SOCKET_MSG_CLI_COMMAND_RESP *pSocketCliCommandRespMsg, int *pDataSize);
#endif	
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int BuildRequestCfgFromDeviceMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_CONFIG *requestCfgMsg, int *pDataSize);
#endif
	int BuildReportCallCaptureMsg(char *pszBuf, int nSize, DCLIENT_REPORT_CALL_CAPTURE *pReportCallCapture, int *pDataSize);
	int BuildReportTriggerMsg(char *pszBuf, int nSize, DCLIENT_REPORT_TRIGGER *pReportTrigger, int *pDataSize);	
	int BuildManageBroadcastMsg(char *pszBuf, int nSize, DCLIENT_MANAGE_BROADCAST_MSG *pManageBroadcastMsg, int *pDataSize);
	int BuildReportHealthMsg(char *pszBuf, int nSize, DCLIENT_REPORT_HEALTH *pReportHealth, int *pDataSize);
	int BuildResponseSensorTriggerMsg(char *pszBuf, int nSize, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, int *pDataSize);
	int BuildUploadVideoNotifyMsg(char *pszBuf, int nSize, DCLIENT_UPLOAD_VIDEO_NOTIFY *pUploadVideoNotifyMsg, int *pDataSize);
	int BuildUploadCaptureNotifyMsg(char *pszBuf, int nSize, DCLIENT_UPLOAD_CAPTURE_NOTIFY *pUploadCaptureNotifyMsg, int *pDataSize);
	int BuildReportAllTriggerMsg(char *pszBuf, int nSize, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger, int *pDataSize);
	int BuildRequestAllTriggerMsg(char *pszBuf, int nSize, DCLIENT_ALL_TRIGGER_STATUS *pRequestAllTrigger, int *pDataSize);
#if RL_SUPPORT_SEND_REPORT_DOOR_STATUS
	int BuildReportDoorStatusMsg(char *pszBuf, int nSize, DCLIENT_REPORT_DOORSTATUS *pDoorStatus, int *pDataSize);
#endif
#if RL_SUPPORT_SEND_REPORT_GAS
	int BuildReportGasMsg(char *pszBuf, int nSize, DCLIENT_REPORT_GAS *pReportGas, int *pDataSize);
#endif
	int BuildReportVisitorInfoMsg(char *pszBuf, int nSize, DCLIENT_REPORT_VISITOR_INFO *pReportVisitorInfo, int *pDataSize);
	int BuildReportVisitorAuthInfoMsg(char *pszBuf, int nSize, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuthInfo, int *pDataSize);	
	int BuildRequestOssStsMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_OSS_STS *pDate, int *pDataSize);	
	int BuildAKCSACKMsg(char *pszBuf, int nSize, SOCKET_MSG_AKCS_ACK *pAkcsAckMsg, int *pDataSize);
	int BuildRequestOpenDoor(char *pszBuf, int nSize, DCLIENT_REQUEST_OPENDOOR *pData, int *pDataSize, int nType = 0);
	int BuildRequestACInfoMsg(char *pszBuf, int nSize, DCLIENT_REQUEST_ACINFO *pData, int *pDataSize);
	int BuildSendDeliveryMsg(char *pszBuf, int nSize, DCLIENT_SEND_DELIVERY *pData, int *pDataSize);
	int BuildSyncActivityMsg(char *pszBuf, int nSize, DCLIENT_SYNC_ACTIVITY *pData, int *pDataSize);
	int BuildReportRelayStatusMsg(char *pszBuf, int nSize, DCLIENT_REPORT_RELAY_STATUS *pData, int *pDataSize);
	int BuildFlowOutOfLimitMsg(char *pszBuf, int nSize, DCLIENT_FLOW_OUT_OF_LIMIT *pData, int *pDataSize);
	int BuildReportCallLogMsg(char *pszBuf, int nSize, DCLIENT_REPORT_CALLLOG *pData, int *pDataSize);
	int BuildBackupConfigACKMsg(char *pszBuf, int nSize, DCLIENT_BACKUP_CONFIG_ACK *pData, int *pDataSize);
	int BuildRequestRtspMonitor(char *pszBuf, int nSize, DCLIENT_REQUEST_RTSP_MONITOR *pData, int *pDataSize);
	int BuildRtspMonitorStop(char *pszBuf, int nSize, DCLIENT_RTSP_MONITOR_STOP *pData, int *pDataSize);
	int BuildRequestEndUserReg(char *pszBuf, int nSize, int *pDataSize);
	int BuildAddKitDevice(char *pszBuf, int nSize, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int *pDataSize);
	int BuildReportKitDevice(char *pszBuf, int nSize, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, int *pDataSize);
	int BuildRequestKitDevice(char *pszBuf, int nSize, int *pDataSize);
	int BuildModifyDeviceLocation(char *pszBuf, int nSize, KIT_DEVICE_BASE_INFO *pData, int *pDataSize);
#if RL_SUPPORT_ARMING_P2P
	int ParseRequestArmingMsg(char *pszBuf, SOCKET_MSG_REQUEST_ARMING *pRequestArmingMsg, INT nDataSize, BOOL bForceMAC = FALSE);
	int ParseReportArmingMsg(char *pszBuf, SOCKET_MSG_REPORT_ARMING *pReportArming, int nDataSize, BOOL bForceMAC = FALSE);
#endif
	int ParseDiscoverMsg(char *pszBuf, SOCKET_MSG_DISCOVER_SEND *pDiscoverMsg, int nDataSize);
	int ParseDiscoverAckMsg(char *pszBuf, SOCKET_MSG_DISCOVER_ACK *pDiscoverAckMsg, int nDataSize);
	int ParseACKMsg(char *pszBuf, SOCKET_MSG_ACK*pACKMsg, INT nDataSize);
	int ParseOwnerMsg(char *pszBuf, SOCKET_MSG_OWNER_MESSAGE*pOwnerMsg, INT nDataSize);
	int ParseTextMsg(char *pszBuf, SOCKET_MSG_TEXT_MESSAGE*pTextMsg, INT nDataSize);
	int ParseKeySendMsg(char *pszBuf, SOCKET_MSG_KEY_SEND *pKeySendMsg, INT nDataSize);
	int ParseUpgradeSendMsg(char *pszBuf, SOCKET_MSG_UPGRADE_SEND *pUpgradeSendMsg, INT nDataSize);
	int ParseAdSendMsg(char *pszBuf, SOCKET_MSG_AD_SEND *pAdSendMsg, INT nDataSize);
	int ParseAlarmSendMsg(char *pszBuf, SOCKET_MSG_ALARM_SEND *pAlarmSendMsg, INT nDataSize);
	int ParseAlarmMsg(char *pszBuf, SOCKET_MSG_ALARM *pAlarmMsg, int nDataSize);
	int ParseAlarmDealMsg(char *pszBuf, SOCKET_MSG_ALARM_DEAL *pAlarmDealMsg, int nDataSize);
	int ParseAlarmAckMsg(char *pszBuf, SOCKET_MSG_ALARM *pAlarmMsg, int nDataSize);
	int ParseHeartBeatPeriodMsg(char *pszBuf, SOCKET_MSG_HEARTBEAT_PERIOD *pHeartBeatPeriodMsg, int nDataSize);
	int ParseGetBindCodeMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeCreate, int nDataSize);
	int ParseDeleteBindCodeMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_CREATE *pBindCodeDelete, int nDataSize);
	int ParseGetBindCodeListMsg(char *pszBuf, SOCKET_MSG_BIND_CODE_LIST *pBindCodeList, int nDataSize);
	int ParseNotifyBindCodeChangeMsg(char *pszBuf, int *pSequenceNum, int nDataSize);
#if RL_SUPPORT_ARMING	
	int ParseRequestArmingMsg(char *pszBuf, SOCKET_MSG_REQUEST_ARMING *pRequestArmingMsg, INT nDataSize);
#endif
#if RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER
	int ParseReportForwardNumberMsg(char *pszBuf, SOCKET_MSG_REPORT_FORWARD_NUMBER *pReportForwardNumberMsg, INT nDataSize);
#endif
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER	
	int ParsePushForwardNumberMsg(char *pszBuf, SOCKET_MSG_PUSH_FORWARD_NUMBER *pPushForwardNumberMsg, INT nDataSize);
#endif	
#if RL_GLOBAL_SUPPORT_TMP_KEY
	int ParseCheckTmpKeyMsg(char *pszBuf, SOCKET_MSG_CHECK_TMP_KEY *pCheckTmpKey, INT nDataSize);
#endif	
#if RL_GLOBAL_SUPPORT_VRTSP	
	int ParseStartRtspMsg(char *pszBuf, SOCKET_MSG_START_RTSP *pStartRtspMsg, int nDataSize);
	int ParseStopRtspMsg(char *pszBuf, SOCKET_MSG_STOP_RTSP *pStopRtspMsg, int nDataSize);
#endif
	
	int ParseContactUrlMsg(char *pszBuf, SOCKET_MSG_CONTACT_URL *pContactUrlMsg, INT nDataSize);
	int ParseReconnectRPSMsg(char *pszBuf, SOCKET_MSG_RECONNECT_RPS *pReconnectRPSMsg, int nDataSize);
	int ParseReconnectGateWayMsg(char *pszBuf, SOCKET_MSG_RECONNECT_GATEWAY *pReconnectGateWayMsg, int nDataSize);
	int ParseReconnectAccessMsg(char *pszBuf, SOCKET_MSG_RECONNECT_ACCESS_SERVER *pReconnectAccessMsg, int nDataSize);
	int ParseCheckDtmfAckMsg(char *pszBuf, SOCKET_MSG_CHECK_DTMF_ACK* pCheckDtmfAckMsg, INT nDataSize);
	int ParseDeviceCodeMsg(char *pszBuf, SOCKET_MSG_DEVICE_CODE* pDeviceCodeMsg, INT nDataSize);
#if RL_SUPPORT_DEVICE_MAINTENANCE	
	int ParseMaintenanceGetLogMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_GETLOG* pMaintenanceGetLog, INT nDataSize);
	int ParseMaintenanceStartPcapMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_START_PCAP* pMaintenanceStartPcap, INT nDataSize);
	int ParseMaintenanceStopPcapMsg(char *pszBuf, SOCKET_MSG_MAINTENANCE_STOP_PCAP* pMaintenanceStopPcap, INT nDataSize);
	int ParseMaintenanceGetDevConfig(char *pszBuf, SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG* pMaintenanceGetDevConfig, INT nDataSize);
	int ParseCliCommandMsg(char *pszBuf, SOCKET_MSG_CLI_COMMAND *pCliCommandMsg, INT nDataSize);
#endif
	int ParseDoorMotionAlertMsg(char *pszBuf, SOCKET_MSG_DOOR_MOTION_ALERT* pDoorMotionAlert, INT nDataSize);
	int ParseCommonTransferMsg(char *pszBuf, SOCKET_MSG_COMMON_TRANSFER* pCommonTransferMsg, INT nDataSize);	
	
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC
	int ParseDownUploadFacePicMsg(char *pszBuf, SOCKET_MSG_DOWNUPLOAD_FACE_PIC *pDownUploadFacePicMsg, INT nDataSize);
#endif	
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int ParseReqCfgFromDeviceMsg(char *pszBuf, SOCKET_MSG_CONFIG_FROM_DEVICE *pRequestConfigMsg, INT nDataSize);
#endif
	int ParseManageAlarmMsg(char *pszBuf, SOCKET_MSG_MANAGE_ALARM_MSG *pManageAlarmMsg, INT nDataSize);
	int ParseMaintenaceServerChange(char *pszBuf, SOCKET_MSG_MAINTENANCE_SERVER_CHANGE *pMaintenanceSrvChange, INT nDataSize);
	int ParseRequestSensorTrigger(char *pszBuf, DCLIENT_SENSOR_TRIGGER *pSensorTriggerMsg, INT nDataSize);
	int ParseRequestAllTrigger(char *pszBuf, SOCKET_MSG_REQUEST_ALL_TRIGGER *pRequestAllTrigger, int nDataSize);
	int ParseReportAllTrigger(char *pszBuf, SOCKET_MSG_REPORT_ALL_TRIGGER *pReportAllTrigger, int nDataSize);
#if RL_SUPPORT_RECV_REPORT_DOOR_STATUS
	int ParseReportDoorStatus(char *pszBuf, DCLIENT_REPORT_DOORSTATUS *pDoorStatus, INT nDataSize);
#endif
	int ParseNotifyVisitorAuthMsg(char *pszBuf, DCLIENT_REPORT_VISITOR_AUTH_INFO *pReportVisitorAuth, INT nDataSize);
	int ParseFaceDataForwardMsg(char *pszBuf, DCLIENT_FACE_DATA_FORWARD *pFaceDataForward, INT nDataSize);
	int ParseSendTempKeyMsg(char *pszBuf, DCLIENT_SEND_TEMP_KEY *pSendTempKey, INT nDataSize);
	int ParseSendOssStsMsg(char *pszBuf, DCLIENT_SEND_OSS_STS *pData, INT nDataSize);	
	int ParseRemoteAccessWeb(char *pszBuf, DCLIENT_REMOTE_ACCESS_WEB *pData, INT nDataSize);	
	int ParseOpenDoorACK(char *pszBuf, DCLIENT_OPENDOOR_ACK *pData, INT nDataSize);
	int ParseRegisterFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseModifyFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseDeleteFace(char *pszBuf, DCLIENT_FACE_INFO *pData, INT nDataSize);
	int ParseGSFaceHttpApiLogin(char *pszBuf, SOCKET_MSG_GSFACE_HTTPAPI *pData, INT nDataSize);
	int ParseRequestPersonelData(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, INT nDataSize);
	int ParseSyncPersonelData(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_PERSONEL_DATA *pData, INT nDataSize);
	int ParseRequestFingerPrint(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, INT nDataSize);
	int ParseSyncFingerPrint(char *pszBuf, DCLIENT_REQUEST_AND_SYNC_FINGER_PRINT *pData, INT nDataSize);
	int ParseNotifyAttendanceSrv(char *pszBuf, DCLIENT_NOTIFY_ATTENDANCE_SERVICE *pData, INT nDataSize);
	int ParseRequestKeepRelayOpenClose(char *pszBuf, DCLIENT_REQUEST_KEEP_RELAY_OPEN_CLOSE *pData, INT nDataSize);
	int ParseBackupConfig(char *pszBuf, DCLIENT_BACKUP_CONFIG *pData, INT nDataSize);
	int ParseBackupConfigRecovery(char *pszBuf, SOCKET_MSG_BACKUP_CONFIG_RECOVERY *pData, INT nDataSize);
	int ParseRequestRtspMonitor(char *pszBuf, DCLIENT_REQUEST_RTSP_MONITOR *pData, INT nDataSize);
	int ParseRtspMonitorStop(char *pszBuf, DCLIENT_RTSP_MONITOR_STOP *pData, INT nDataSize);
	int ParseRegEndUser(char *pszBuf, DCLIENT_REG_END_USER *pData, INT nDataSize);
	int ParseRequestIsKit(char *pszBuf, INT& nKitFlag, INT nDataSize);
	int ParseReportKitDevices(char *pszBuf, DCLIENT_REPORT_KIT_DEVICE_LIST *pData, INT nDataSize);
		
	int AesEncryptByMac(char *pIn, char *pOut, INT *pDataSize, BOOL bForceMac=FALSE);
	int AesEncryptByDefault(char *pIn, char *pOut, INT *pDataSize);
	int AesDecryptByMac(char *pIn, char *pOut, INT nDataSize, BOOL bForceMac=FALSE);
	int AesDecryptByDefault(char *pIn, char *pOut, INT nDataSize, char* pOffset = NULL);

	/*
	写设备地址表到文件中
	<Devices>
		<Item Name="">5_0.0.0.0.0-1=***********</Item>
		<Item Name="">1_*******.1-1=************</Item>
		<Item Name="">1_*******.2-1=************</Item>
	</Devices>
	*/
	int CreateAddressFile(const char *pszFile, DISCOVER_DEVICE_ADDR *pDeviceList);

	std::string XmlTrans(char *pszItem);
	
	static CMsgHandle *GetInstance();
private:
	
	static CMsgHandle *instance;
	
};

CMsgHandle *GetMsgHandleInstance();

#endif

