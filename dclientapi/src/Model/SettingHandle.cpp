
#include "SettingHandle.h"
#include "Utility.h"
#include "revision.h"
#include <pthread.h>
#include "DClient.h"
#include "DclientDefine.h"

pthread_mutex_t g_mutex = PTHREAD_MUTEX_INITIALIZER;

CSettingHandle *GetSettingHandleInstance()
{
	return CSettingHandle::GetInstance();
}

CSettingHandle::CSettingHandle()
{
	pthread_mutex_init(&g_mutex, NULL);
}

CSettingHandle::~CSettingHandle()
{
	
}

CSettingHandle *CSettingHandle::instance = NULL;

CSettingHandle *CSettingHandle::GetInstance()
{
	if(instance == NULL)
	{
		instance = new CSettingHandle();
	}

	return instance;
}

int CSettingHandle::GetLanIPAddr(CHAR *pszLanIPAddr, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_NETWORK_LANIPADDR, pszLanIPAddr, nSize, "0.0.0.0");
#endif // 0
	return -1;
}

int CSettingHandle::GetSubnetMask(CHAR *pszSubnetMask, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_NETWORK_LANSUBNETMASK, pszSubnetMask, nSize, "*************");
#endif // 0
	return -1;
}
int CSettingHandle::GetGateway(CHAR *pszGateway, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_NETWORK_LANGATEWAY, pszGateway, nSize, "0.0.0.0");
#endif // 0
	return -1;
}
int CSettingHandle::GetPrimaryDNS(CHAR *pszPrimaryDNS, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_NETWORK_LANDNS1, pszPrimaryDNS, nSize, "0.0.0.0");
#endif // 0
	return -1;
}
int CSettingHandle::GetSecondaryDNS(CHAR *pszSecondaryDNS, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_NETWORK_LANDNS2, pszSecondaryDNS, nSize, "0.0.0.0");
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceID(CHAR *pszDeviceID, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_ADDRESS, pszDeviceID, nSize, CFG_DOORSETTING_DEVICENODE_ADDRESS_DEFAULT);
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceExtension(CHAR *pszExtension, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_EXTENSION, pszExtension, nSize, CFG_DOORSETTING_DEVICENODE_EXTENSION_DEFAULT);
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceDownloadServer(CHAR *pszDownloadServer, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_DOWNLOADSERVER, pszDownloadServer, nSize, CFG_DOORSETTING_DEVICENODE_DOWNLOADSERVER_DEFAULT);
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceUploadServer(CHAR *pszUploadServer, int nSize)
{	
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_UPLOADSERVER, pszUploadServer, nSize, "");
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceRemoteBook(CHAR *pszRemoteBook, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_REMOTEBOOK, REMOTEBOOK_KEY_BOOK0_URL, pszRemoteBook, nSize, "");
#endif // 0
	return -1;
}

int CSettingHandle::GetDeviceType(CHAR *pszType, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_TYPE, pszType, nSize, CFG_DOORSETTING_DEVICENODE_TYPE_DEFAULT);
#endif // 0
	return -1;
}

int CSettingHandle::GetMAC(CHAR *pszMAC, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_STATUS, STATUS_KEY_NETWORK_MAC, pszMAC, nSize, "0C1105000000") ? 0 : -1;
}

int CSettingHandle::SetMAC(const CHAR* pszMAC)
{
	return GetDClientInstance()->CfgSetString(CFG_ID_STATUS, STATUS_KEY_NETWORK_MAC, pszMAC) ? 0 : -1;
}

int CSettingHandle::GetStatus(CHAR *pszStatus, int nSize)
{
#if 0
	cfg_get_string(CFG_ID_STATUS, STATUS_KEY_GENERAL_PHONE, pszStatus, nSize, "0");
    //rl_log_debug("pszStatus=%s", pszStatus);
	return 0;
#endif // 0
	return -1;
}

int CSettingHandle::GetSWVer(CHAR *pszSWVer, int nSize)
{
#if 0
#if (RL_PLATFORMID == RL_PLATFORMID_ANDROID)
    CHAR tmpBuf[DEVICE_SWVER_SIZE] = {0};
    __system_property_get("ro.hardware.model", tmpBuf);
    if (0 == rl_strcasecmp(tmpBuf, "R47G")) {
        cfg_get_string(CFG_ID_STATUS, STATUS_KEY_GENERAL_FIRMWARE, tmpBuf, sizeof(tmpBuf), "0.0.0.0");
        CHAR *pFirstDot = rl_strchr(tmpBuf, '.');
        if (NULL != pFirstDot) {
            rl_sprintf_s(pszSWVer, nSize, "%s%s", "47g", pFirstDot);
            return 0;
        }
    }
#endif
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_GENERAL_FIRMWARE, pszSWVer, nSize, "0.0.0.0");
#endif // 0
	return -1;
}

int CSettingHandle::GetHWVer(CHAR *pszHWVer, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_GENERAL_HARDWARE, pszHWVer, nSize, "0.0.0.0");
#endif // 0
	return -1;
}

int CSettingHandle::GetModel(CHAR *pszModel, int nSize)
{
	if(pszModel == NULL)
	{
		return -1;
	}
#ifdef  DC_STATUS_KEY_GENERAL_MODEL || STATUS_KEY_GENERAL_MODEL
	return cfg_get_string(CFG_ID_STATUS,  STATUS_KEY_GENERAL_MODEL, pszModel, nSize, "");
#else
	return 0;
#endif
}

int CSettingHandle::GetPrivatekeyMD5(CHAR *pszPrivatekeyMD5, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_PRIVATEKEYMD5, pszPrivatekeyMD5, nSize, "");
#endif // 0
	return -1;
}

int CSettingHandle::GetRfidMD5(CHAR *pszRfidMD5, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_RFIDMD5, pszRfidMD5, nSize, "");
#endif // 0
	return -1;
}

int CSettingHandle::GetAddrMD5(CHAR *pszAddrMD5, int nSize)
{
#if 0
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ADDRMD5, pszAddrMD5, nSize, "");
#endif // 0
	return -1;
}

int CSettingHandle::GetConfigMD5(CHAR *pszConfigMD5, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_CONFIGMD5, pszConfigMD5, nSize, "") ? 0 : -1;
}

int CSettingHandle::GetAdModuleMD5(CHAR *pszAdModuleMD5, int nSize)
{
	return 0;
}


int CSettingHandle::GetCommunityPhonebookMD5(CHAR *pszCommunityPhonebookMD5, int nSize)
{
	return 0;
}


int CSettingHandle::GetContactMD5(CHAR *pszContactMD5, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_CONTACTMD5, pszContactMD5, nSize, "") ? 0 : -1;
}

int CSettingHandle::SetContactMD5(CHAR *pszContactMD5)
{
	return GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_CONTACTMD5, pszContactMD5) ? 0 : -1;
}

int CSettingHandle::GetFaceIDMD5(CHAR *pszFaceIDMD5, int nSize)
{
	return 0;
}

int CSettingHandle::SetFaceIDMD5(CHAR *pszFaceIDMD5)
{
	return 0;
}

int CSettingHandle::GetDownLoadFacePicMD5(CHAR *pszDownLoadFacePicMD5, int nSize)
{
	return 0;
}

int CSettingHandle::SetDownLoadFacePicMD5(CHAR *pszDownLoadFacePicMD5)
{
	return 0;
}

int CSettingHandle::GetCloudServerEnable()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_ENABLE, 0);
}

int CSettingHandle::SetCloudServerEnable(int nEnable)
{
	GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_ENABLE, nEnable);
	return 0;
}

int CSettingHandle::GetCloudServerIp(CHAR *pszCloudServerIp, int nSize)
{
	bool bGet = GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_SERVER, pszCloudServerIp, nSize, "");
	return bGet ? 0 : -1;
}

int CSettingHandle::GetCloudServerPort()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_PORT, 0);
}

int CSettingHandle::SetCloudServerIp(CHAR *pszCloudServerIp)
{
	GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_SERVER, pszCloudServerIp);
	return 0;
}

int CSettingHandle::SetCloudServerPort(INT nPort)
{
	GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_PORT, nPort);
	return 0;
}

int CSettingHandle::SetCloudServerRpsEnable(int nEnable)
{	
	return GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_RPSENABLE, nEnable) ? 0 : -1;
}

int CSettingHandle::GetCloudServerRpsEnable()
{	
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_RPSENABLE, 0);
}

int CSettingHandle::SetCloudServerRpsServer(CHAR *pszRpsServer)
{
	return GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_RPSSERVER, pszRpsServer) ? 0 : -1;	
}

int CSettingHandle::GetCloudServerRpsServer(CHAR *pszRpsServer, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_RPSSERVER, pszRpsServer, nSize, "http://rps.akuvox.com:8080/redirect?mac=000000000000") ? 0 : -1;
}

int CSettingHandle::SetCloudServerGate(CHAR *pszGateServerIp, INT nGateServerPort)
{
	GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_GATESERVER, pszGateServerIp);
	GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_GATEPORT, nGateServerPort);
	return 0;
}

int CSettingHandle::GetCloudServerGateServer(CHAR *pszGateServerIp, int nSize)
{
	bool bGet = GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_GATESERVER, pszGateServerIp, nSize, "");
	return bGet ? 0 : -1;
}

int CSettingHandle::GetCloudServerGatePort()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_GATEPORT, 80);
}

int CSettingHandle::SetCloudServerToken(CHAR *pszGateToken)
{
	GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_TOKEN, pszGateToken);
	return 0;
}

int CSettingHandle::GetCloudServerToken(CHAR *pszGateToken, int nSize)
{	
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_TOKEN, pszGateToken, nSize, "");
}

int CSettingHandle::SetPrivatekeyMD5(CHAR *pszPrivatekeyMD5)
{
#if 0
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_PRIVATEKEYMD5, pszPrivatekeyMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#endif // 0
	return -1;
}

int CSettingHandle::SetRfidMD5(CHAR *pszRfidMD5)
{
#if 0
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_RFIDMD5, pszRfidMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#endif // 0
	return -1;
}

int CSettingHandle::SetAddrMD5(CHAR *pszAddrMD5)
{
#if 0
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ADDRMD5, pszAddrMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#endif // 0
	return -1;
}

int CSettingHandle::SetConfigMD5(CHAR *pszConfigMD5)
{
	GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_CONFIGMD5, pszConfigMD5);
	return 0;
}

int CSettingHandle::SetCommunityPhoneBookMD5(CHAR *pszCommunityPhonebookMD5)
{

	return -1;
}

int CSettingHandle::SetAdModuleMD5(CHAR *pszAdModuleMD5)
{

	return -1;
}

int CSettingHandle::SetRemoteBook(CHAR *pszRemoteBook)
{
#if 0
	cfg_set_string(CFG_ID_REMOTEBOOK, REMOTEBOOK_KEY_BOOK0_URL, pszRemoteBook);
	return cfg_save(CFG_ID_REMOTEBOOK);
#endif // 0
	return -1;
}

int CSettingHandle::SetConnectMode(INT nConnectMode)
{
	GetDClientInstance()->CfgSetInt(CFG_ID_STATUS, STATUS_KEY_DOORSETTING_CONNECT_MODE, nConnectMode);	
	GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CONNECT_SERVER_MODE, nConnectMode);	
	return 0;
}

int CSettingHandle::GetConnectMode()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CONNECT_SERVER_MODE, DOORSETTING_CONNECT_SERVER_MODE_NONE);
}

int CSettingHandle::GetDiscoveryMode()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CONNECT_DISCOVERY_MODE, 0);
}

int CSettingHandle::GetDeviceCode(CHAR *pszDeviceCode, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_STATUS, STATUS_KEY_DOORSETTING_DEVICE_CODE, pszDeviceCode, nSize, "") ? 0 : -1;
}

int CSettingHandle::SetDeviceCode(CHAR *pszDeviceCode)
{
	return GetDClientInstance()->CfgSetString(CFG_ID_STATUS, STATUS_KEY_DOORSETTING_DEVICE_CODE, pszDeviceCode) ? 0 : -1;
}

int CSettingHandle::GetDeviceLocation(CHAR *pszDeviceLocation, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_LOCATION, pszDeviceLocation, nSize, "") ? 0 : -1;
}

int CSettingHandle::GetDeviceName(CHAR *pszDeviceName, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_GENERAL_DISPLAYNAME, pszDeviceName, nSize, "") ? 0 : -1;
}

int CSettingHandle::SetDeviceAccountStatus(bool bAccountStatus)
{
	return GetDClientInstance()->CfgSetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_GENERAL_ENABLE, bAccountStatus);
}

int CSettingHandle::GetDeviceSipRegStatus()
{
	return -1;
}

int CSettingHandle::GetDeviceSipUser(CHAR *pszSipUser, int nSize)
{
	return -1;
}

#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
int CSettingHandle::GetNoAnswerFwdNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pNoAnswerFwdNumber)
{
	if(pNoAnswerFwdNumber == NULL)
	{
		return  -1;
	}

	return 0;
}

int CSettingHandle::SetNoAnswerFwdNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pNoAnswerFwdNumber)
{
	return 0;
}
#endif

#if RL_SUPPORT_AUTH_CODE
int CSettingHandle::GetAuthKey(CHAR *pszAuthKey, int nSize)
{
	return 0;
}

int CSettingHandle::SetAuthKey(CHAR *pszAuthKey)
{
	return 0;
}
#endif

int CSettingHandle::SetSDMCServer(CHAR *pszSDMCServerIp, INT nSDMCServerPort)
{
	GetDClientInstance()->CfgSetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_SERVER, pszSDMCServerIp);	
	GetDClientInstance()->CfgSetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_PORT, nSDMCServerPort);
	return 0;
}

int CSettingHandle::GetSDMCServerIP(CHAR *pszSDMCServerIp, int nSize)
{
	return GetDClientInstance()->CfgGetString(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_SERVER, pszSDMCServerIp, nSize, "");
}

int CSettingHandle::GetSDMCServerPort()
{
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_PORT, 8501);
}

int CSettingHandle::SetWebSrv(CHAR *pszServer)
{
	if(pszServer == NULL)
	{
		return  -1;
	}

	GetDClientInstance()->CfgSetString(CFG_ID_STATUS, STATUS_KEY_MAINTENANCE_SERVER_WEB, pszServer);
	return 0;
}

int CSettingHandle::GetWebSrv(CHAR *pszServer, int nSize)
{
	if(pszServer == NULL || nSize <= 0)
	{
		return  -1;
	}

	bool bGet = GetDClientInstance()->CfgGetString(CFG_ID_STATUS, STATUS_KEY_MAINTENANCE_SERVER_WEB, pszServer, nSize, "");
	return bGet ? 0 : -1;
}

int CSettingHandle::SetPBXSrv(CHAR *pszServer)
{
	if(pszServer == NULL)
	{
		return -1;
	}
	CHAR szAccessServerIp[VALUE_SIZE] = {0};	
	CHAR szAccessPort[INT_SIZE] = {0};
	
	if(sscanf(pszServer, "%[^:]:%[0-9]", szAccessServerIp, szAccessPort) == 2)
	{
		if((!rl_str_isempty(szAccessServerIp)) && (!rl_str_isempty(szAccessPort)))
		{
			GetDClientInstance()->CfgSetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_SERVER, szAccessServerIp);
			if(GetDClientInstance()->CfgGetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_TRANSTYPE, 0) == 2)//if the mode is tls ,set the port as port+1
			{
				GetDClientInstance()->CfgSetInt(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, rl_atoi(szAccessPort)+1);
			}
			else
			{
				GetDClientInstance()->CfgSetString(CFG_ID_ACCOUNT_01, ACCOUNT_KEY_SIP_PORT, szAccessPort);
			}

			GetDClientInstance()->OnMessage(DCLIENT_IPC_NOTIFY_CONFIG_CHANGED, 0, 0);
			return 0;
		}
	}
	return -1;
}

int CSettingHandle::SetFtpSrv(CHAR *pszServer)
{
	if(pszServer == NULL)
	{
		return  -1;
	}

	bool bSet = GetDClientInstance()->CfgSetString(CFG_ID_STATUS, STATUS_KEY_MAINTENANCE_SERVER_FTP, pszServer);
	return bSet ? 0 : -1;
}

int CSettingHandle::SetVrtspSrv(CHAR *pszServer)
{
	if(pszServer == NULL)
	{
		return  -1;
	}
	
	bool bSet = GetDClientInstance()->CfgSetString(CFG_ID_STATUS, STATUS_KEY_MAINTENANCE_SERVER_VRTSP, pszServer);
	return bSet ? 0 : -1;
}

int CSettingHandle::SetFPMD5(CHAR *pszFPD5)
{	
	return -1;
}

int CSettingHandle::GetFPMD5(CHAR *pszFPD5, int nSize)
{	
	return -1;
}

int CSettingHandle::SetSrvName(CHAR *pszSrvName)
{
	return -1;
}

int CSettingHandle::GetSrvName(CHAR *pszSrvName, int nSize)
{
	return -1;
}


#if RL_SUPPORT_ARMING
int CSettingHandle::GetArmingType()
{
	return 0;
}
#endif

#if 0
static SETTING_TEXT_ID_MAP g_settingMapStatus[] =
{
	{"Config.Status.Account1", CFG_ID_STATUS, STATUS_KEY_ACCOUNT_01},
	{"Config.Status.Account2", CFG_ID_STATUS, STATUS_KEY_ACCOUNT_02},
	{"Config.Status.Account3", CFG_ID_STATUS, STATUS_KEY_ACCOUNT_03},
	{"Config.Status.Firmware", CFG_ID_STATUS, STATUS_KEY_GENERAL_FIRMWARE},
	{"Config.Status.Hardware", CFG_ID_STATUS, STATUS_KEY_GENERAL_HARDWARE},
	{"Config.Status.MAC", CFG_ID_STATUS, STATUS_KEY_NETWORK_MAC},
	{"Config.Status.Model", CFG_ID_STATUS, STATUS_KEY_GENERAL_MODEL},
	{"Config.Status.Link", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANLINK},
	{"Config.Status.IP", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANIPADDR},
	{"Config.Status.Gateway", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANGATEWAY},
	{"Config.Status.Netmask", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANSUBNETMASK},
	{"Config.Status.PrimaryDNS", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANDNS1},
	{"Config.Status.SecondaryDNS", CFG_ID_STATUS, STATUS_KEY_NETWORK_LANDNS2},
};
#endif // 0

int CSettingHandle::GetConfigModule(CONFIG_MODULE *pConfigModule)
{
#if 0
	for(UINT i=0; i<sizeof(pConfigModule->szItem)/sizeof(pConfigModule->szItem[0]); i++)
	{
		if(rl_str_isempty(pConfigModule->szItem[i]))
		{
			break;
		}
        INT nKeyID, nCfgID;
		CHAR szTmpKey[CONFIG_MODULE_ITEM_SIZE];
        CHAR szTmpVal[CONFIG_MODULE_ITEM_SIZE] = {0};
		
		rl_strcpy_s(szTmpKey, sizeof(szTmpKey), pConfigModule->szItem[i]);

        if(cfg_get_item_id(szTmpKey, &nCfgID, &nKeyID) == 0)
		{
			 cfg_get_string(nCfgID, nKeyID, szTmpVal, sizeof(szTmpVal), "");
		}
		else
		{
			//STATUS特殊处理
			if(strncasecmp(pConfigModule->szItem[i], "Config.Status.", rl_strlen("Config.Status.")) == 0)
			{
				for(UINT j=0; j<sizeof(g_settingMapStatus)/sizeof(SETTING_TEXT_ID_MAP); j++)
				{
					if(rl_strcasecmp(pConfigModule->szItem[i], g_settingMapStatus[j].pszText) == 0)
					{
						cfg_get_string(g_settingMapStatus[j].nCfgID, g_settingMapStatus[j].nKeyID, szTmpVal, sizeof(szTmpVal), "");
					}
				}
			}
		}

		rl_sprintf_s(szTmpKey, sizeof(szTmpKey), "%s=%s", pConfigModule->szItem[i], szTmpVal);

        rl_log_debug("TODO: Get %s  value %s", pConfigModule->szItem[i],szTmpVal);

		rl_strcpy_s(pConfigModule->szItem[i], sizeof(pConfigModule->szItem[i]), szTmpKey);
	}

	return 0;
#endif // 0

	return -1;
}

#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
int CSettingHandle::GetConfigModule(CONFIG_MODULE_FROM_DEVICE* pConfigModule)
{
	for(int i=0; i<sizeof(pConfigModule->szItem)/sizeof(pConfigModule->szItem[0]); i++)
	{
		if(rl_str_isempty(pConfigModule->szItem[i]))
		{
			break;
		}
        INT nKeyID, nCfgID;
		CHAR szTmpKey[CONFIG_MODULE_FROM_DEVICE_ITEM_SIZE];
        CHAR szTmpVal[CONFIG_MODULE_FROM_DEVICE_ITEM_SIZE] = {0};
		
		rl_strcpy_s(szTmpKey, sizeof(szTmpKey), pConfigModule->szItem[i]);

        if(cfg_get_item_id(szTmpKey, &nCfgID, &nKeyID) == 0)
		{
			 cfg_get_string(nCfgID, nKeyID, szTmpVal, sizeof(szTmpVal), "");
		}
		else
		{
			//STATUS特殊处理
			if(strncasecmp(pConfigModule->szItem[i], "Config.Status.", rl_strlen("Config.Status.")) == 0)
			{
				for(int j=0; j<sizeof(g_settingMapStatus)/sizeof(SETTING_TEXT_ID_MAP); j++)
				{
					if(rl_strcasecmp(pConfigModule->szItem[i], g_settingMapStatus[j].pszText) == 0)
					{
						cfg_get_string(g_settingMapStatus[j].nCfgID, g_settingMapStatus[j].nKeyID, szTmpVal, sizeof(szTmpVal), "");
					}
				}
			}
		}

		rl_sprintf_s(szTmpKey, sizeof(szTmpKey), "%s=%s", pConfigModule->szItem[i], szTmpVal);

        rl_log_debug("TODO: Get %s  value %s", pConfigModule->szItem[i],szTmpVal);

		rl_strcpy_s(pConfigModule->szItem[i], sizeof(pConfigModule->szItem[i]), szTmpKey);
	}

	return 0;
}
#endif

int CSettingHandle::AddCfgIDToList(int *pnList, int nSize, int nCfgID)
{
	if(pnList == NULL)
	{
		return -1;
	}
	
	for (INT i=0; i <nSize; i++)
	{
		if (nCfgID == pnList[i])
		{
			return 0;
		}
		else if (-1 == pnList[i])
		{
			pnList[i] = nCfgID;
			return 0;
		}
	}

	return -1;
}

int CSettingHandle::SetConfigModule(CONFIG_MODULE *pConfigModule, int *pnCfgIDList, int nSize)
{
#if 0
	for(UINT i=0; i<sizeof(pConfigModule->szItem)/sizeof(pConfigModule->szItem[0]); i++)
	{
		if(rl_str_isempty(pConfigModule->szItem[i]))
		{
			break;
		}

		rl_log_debug("TODO: Set %s", pConfigModule->szItem[i]);

		CHAR szTmpKey[CONFIG_MODULE_ITEM_SIZE] = {0};
		CHAR szTmpVal[CONFIG_MODULE_ITEM_SIZE] = {0};
	
		ParseConfigItemText(pConfigModule->szItem[i], szTmpKey, sizeof(szTmpKey), szTmpVal, sizeof(szTmpVal));
	
		INT nKeyID, nCfgID;

        if(cfg_get_item_id(szTmpKey, &nCfgID, &nKeyID) == 0)
		{
			CHAR szOriVal[CONFIG_MODULE_ITEM_SIZE] = {0};
			cfg_get_string(nCfgID, nKeyID, szOriVal, sizeof(szOriVal), "");
			if(rl_strcmp(szOriVal, szTmpVal) != 0)
			{
				cfg_set_string(nCfgID, nKeyID, szTmpVal);
				AddCfgIDToList(pnCfgIDList, nSize, nCfgID);
			}
		}
	}

	for(int i=0; i<nSize; i++)
	{
		if(pnCfgIDList[i] != -1)
		{
			cfg_save(pnCfgIDList[i]);
		}
	}

	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::SetConfigModule(CHAR *pszConfigModule)
{
#if 0
	if(pszConfigModule == NULL || rl_str_isempty(pszConfigModule))
	{
		return -1;
	}

	rl_log_debug("TODO: Set %s", pszConfigModule);

	CHAR szTmpKey[VALUE_SIZE] = {0};
	CHAR szTmpVal[BUF_SIZE] = {0};

	ParseConfigItemText(pszConfigModule, szTmpKey, sizeof(szTmpKey), szTmpVal, sizeof(szTmpVal));

	INT nKeyID, nCfgID;
    if(cfg_get_item_id(szTmpKey, &nCfgID, &nKeyID) == 0)
	{
	#if(RL_PLATFORMID == RL_PLATFORMID_ANDROID) && RL_MODEL_INDOOR
		if(nKeyID == SETTING_KEY_HANDFREE_MICVOL)
		{
			nKeyID = SETTING_KEY_HANDFREE_MICMIDVOL;
		}
	#endif
		CHAR szOriVal[BUF_SIZE] = {0};
		cfg_get_string(nCfgID, nKeyID, szOriVal, sizeof(szOriVal), "");
		if(rl_strcmp(szOriVal, szTmpVal) != 0)
		{
			cfg_set_string(nCfgID, nKeyID, szTmpVal);
		}
	}
	if(nCfgID != -1)
	{
		cfg_save(nCfgID);
		ipc_broadcast(BROAD_ID_CONFIG_CHANGED, nCfgID, 0, NULL, 0);
	}
	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::GetConfigModule(CHAR *pszConfigModule, int nSize)
{
#if 0
	if(pszConfigModule == NULL || rl_str_isempty(pszConfigModule))
	{
		return -1;
	}
    INT nKeyID, nCfgID;
	CHAR szTmpKey[VALUE_SIZE];
    CHAR szTmpVal[BUF_SIZE] = {0};
	
	rl_strcpy_s(szTmpKey, sizeof(szTmpKey), pszConfigModule);

    if(cfg_get_item_id(szTmpKey, &nCfgID, &nKeyID) == 0)
	{
	#if(RL_PLATFORMID == RL_PLATFORMID_ANDROID) && RL_MODEL_INDOOR
		if(nKeyID == SETTING_KEY_HANDFREE_MICVOL)
		{
			nKeyID = SETTING_KEY_HANDFREE_MICMIDVOL;
		}
	#endif
		 cfg_get_string(nCfgID, nKeyID, szTmpVal, sizeof(szTmpVal), "");
	}
	else
	{
		//STATUS特殊处理
		if(strncasecmp(pszConfigModule, "Config.Status.", rl_strlen("Config.Status.")) == 0)
		{
			for(int j=0; j<sizeof(g_settingMapStatus)/sizeof(SETTING_TEXT_ID_MAP); j++)
			{
				if(rl_strcasecmp(pszConfigModule, g_settingMapStatus[j].pszText) == 0)
				{
					cfg_get_string(g_settingMapStatus[j].nCfgID, g_settingMapStatus[j].nKeyID, szTmpVal, sizeof(szTmpVal), "");
				}
			}
		}
	}

	rl_sprintf_s(szTmpKey, sizeof(szTmpKey), "%s=%s", pszConfigModule, szTmpVal);

    rl_log_debug("TODO: Get %s  value %s", pszConfigModule,szTmpVal);

	rl_strcpy_s(pszConfigModule, nSize, szTmpKey);
	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::SetRemoteSyslog(int on, CHAR *pszServer, int nLogLevel)
{
#if 0
	cfg_set_int(CFG_ID_SETTING, SETTING_KEY_REMOTE_SYSLOG, on);
	cfg_set_string(CFG_ID_SETTING, SETTING_KEY_REMOTE_SYSSERVER, pszServer);
	cfg_set_int(CFG_ID_SETTING, SETTING_KEY_LOG_LEVEL, nLogLevel);
	cfg_save(CFG_ID_SETTING);
	ipc_broadcast(BROAD_ID_CONFIG_CHANGED, CFG_ID_SETTING, 0, NULL, 0);

	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::GetSSHPassSrv(CHAR *pszSSHPassSrv, int nSize)
{
#if 0
	if(pszSSHPassSrv == NULL || nSize <= 0)
	{
		return  -1;
	}
#if DC_DOORSETTING_KEY_DEVICENODE_SSHPASSSRV || RL_GLOBAL_SUPPORT_SSHPASSSRV
	cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_SSHPASSSRV, pszSSHPassSrv, nSize, "remoteconfig.akuvox.com");
#endif
	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::GetMaintenanceAlarm()
{
	//return cfg_get_int(CFG_ID_DOORSETTING, DOORSETTING_KEY_CLOUDSERVER_MAINTENANCEALARM, 1);
	return -1;
}

int CSettingHandle::GetTzMD5(CHAR *pszTzMD5, int nSize)
{
#if 0
	if(pszTzMD5 == NULL)
	{
		return -1;
	}
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_TZMD5, pszTzMD5, nSize, "");
#endif // 0

	return -1;
}

int CSettingHandle::GetTzDataMD5(CHAR *pszTzDataMD5, int nSize)
{
#if 0
	if(pszTzDataMD5 == NULL)
	{
		return -1;
	}
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_TZDATAMD5, pszTzDataMD5, nSize, "");
#endif // 0

	return -1;
}

int CSettingHandle::GetFaceSyncMD5(CHAR *pszFaceSyncMD5, int nSize)
{
#if 0
	if(pszFaceSyncMD5 == NULL)
	{
		return -1;
	}
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_FACESYNCMD5, pszFaceSyncMD5, nSize, "");
#endif // 0

	return -1;
}

int CSettingHandle::SetFaceSyncPicMD5(CHAR *pszMD5)
{
#if 0
	if(pszMD5 == NULL)
	{
		return -1;
	}
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_FACESYNCMD5, pszMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#endif // 0

	return -1;
}

int CSettingHandle::GetACInfoMD5(CHAR *pszACInfoMD5, int nSize)
{
#if 0
	if(pszACInfoMD5 == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_ACINFOMD5 || DOORSETTING_KEY_UPDATE_ACINFOMD5
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ACINFOMD5, pszACInfoMD5, nSize, "");
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::SetACInfoMD5(CHAR *pszMD5)
{
#if 0
	if(pszMD5 == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_ACINFOMD5 || DOORSETTING_KEY_UPDATE_ACINFOMD5
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ACINFOMD5, pszMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::GetACMetaMD5(CHAR *pszACMetaMD5, int nSize)
{
#if 0
	if(pszACMetaMD5 == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_ACMETAMD5 || DOORSETTING_KEY_UPDATE_ACMETAMD5
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ACMETAMD5, pszACMetaMD5, nSize, "");
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::SetACMetaMD5(CHAR *pszMD5)
{
#if 0
	if(pszMD5 == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_ACMETAMD5 || DOORSETTING_KEY_UPDATE_ACMETAMD5
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_ACMETAMD5, pszMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::SetAttendanceSrv(CHAR *pszService)
{
#if 0
	if(pszService == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV || DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV, pszService);
	return cfg_save(CFG_ID_DOORSETTING);
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::GetAttendanceSrv(CHAR *pszService, int nSize)
{
#if 0
	if(pszService  == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV || DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_DEVICENODE_ATTENDANCESRV, pszService, nSize, "");
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::SetScheduleMD5(CHAR *pszMD5)
{
#if 0
	if(pszMD5 == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_SCHEDULEMD5 || DOORSETTING_KEY_UPDATE_SCHEDULEMD5
	cfg_set_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_SCHEDULEMD5, pszMD5);
	return cfg_save(CFG_ID_DOORSETTING);
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::GetScheduleMD5(CHAR *pszMD5, int nSize)
{
#if 0
	if(pszMD5  == NULL)
	{
		return -1;
	}
#if DC_DOORSETTING_KEY_UPDATE_SCHEDULEMD5 || DOORSETTING_KEY_UPDATE_SCHEDULEMD5
	return cfg_get_string(CFG_ID_DOORSETTING, DOORSETTING_KEY_UPDATE_SCHEDULEMD5, pszMD5, nSize, "");
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::GetModelName(CHAR *pszModelName, int nSize)
{
#if 0
	if(pszModelName  == NULL)
	{
		return -1;
	}
#if DC_STATUS_KEY_GENERAL_MODEL || STATUS_KEY_GENERAL_MODEL
	return cfg_get_string(CFG_ID_STATUS, STATUS_KEY_GENERAL_MODEL, pszModelName, nSize, "");
#else
	return 0;
#endif
#endif // 0

	return -1;
}

int CSettingHandle::GetRelayStatus(CHAR *pszData, int nSize)
{
#if 0
	if(pszData  == NULL)
	{
		return -1;
	}
	int nFlag = 0;
#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
	for(int i=0; i<4; i++)
	{
		int nStatus = 0;
		devctrl_get_relay_state(i, &nStatus);
		if(nStatus == 1)
		{
			nFlag = nFlag*10 + (i+1);
		}
	}
	rl_sprintf_s(pszData, nSize, "%d", nFlag);
#elif(DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID)
	int nRelayA = devctrl_get_relaya_status();
	int nRelayB = devctrl_get_relayb_status();
	nFlag = nRelayA * 10 + nRelayB * 2;
	rl_sprintf_s(pszData, nSize, "%d", nFlag);
#elif DC_STATUS_KEY_GENERAL_RELAYA || DC_STATUS_KEY_GENERAL_RELAYA
	for(int i=0; i<4; i++)
	{
		int nStatus = cfg_get_int(CFG_ID_STATUS, STATUS_KEY_GENERAL_RELAYA + i, 0);
		if(nStatus == 1)
		{
			nFlag = nFlag*10 + (i+1);
		}
	}
	rl_sprintf_s(pszData, nSize, "%d", nFlag);
#endif
	return 0;
#endif // 0

	return -1;
}

int CSettingHandle::GetSubServerMode()
{
#ifdef DC_DOORSETTING_KEY_CONNECT_SUBMODE || DOORSETTING_KEY_CONNECT_SUBMODE
	return GetDClientInstance()->CfgGetInt(CFG_ID_DOORSETTING, DOORSETTING_KEY_CONNECT_SUBMODE, 0);
#else
	return 0;
#endif
}


