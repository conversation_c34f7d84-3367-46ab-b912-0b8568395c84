#ifndef __SETTING_HANDLE_H__
#define __SETTING_HANDLE_H__

#include "DclientIncludes.h"
#include "dclient_ipc.h"

typedef struct SETTING_TEXT_ID_MAP_T
{
	CHAR *pszText;
	UINT nCfgID;
	UINT nKeyID;
}SETTING_TEXT_ID_MAP;


#pragma once

class CSettingHandle
{
public:
	CSettingHandle();
	~CSettingHandle();
	
	int GetLanIPAddr(CHAR *pszLanIPAddr, int nSize);
	int GetSubnetMask(CHAR *pszSubnetMask, int nSize);
	int GetGateway(CHAR *pszGateway, int nSize);
	int GetPrimaryDNS(CHAR *pszPrimaryDNS, int nSize);
	int GetSecondaryDNS(CHAR *pszSecondaryDNS, int nSize);
	int GetDeviceExtension(CHAR *pszExtension, int nSize);
	int GetDeviceDownloadServer(CHAR *pszDownloadServer, int nSize);
	int GetDeviceUploadServer(CHAR *pszUploadServer, int nSize);
	int GetDeviceRemoteBook(CHAR *pszRemoteBook, int nSize);
	int GetDeviceID(CHAR *pszDeviceID, int nSize);
	int GetDeviceType(CHAR *pszType, int nSize);
	int GetMAC(CHAR *pszMAC, int nSize);
	int SetMAC(const CHAR* pszMAC);
	int GetStatus(CHAR *pszStatus, int nSize);
	int GetSWVer(CHAR *pszSWVer, int nSize);
	int GetHWVer(CHAR *pszHWVer, int nSize);
	int GetModel(CHAR *pszModel, int nSize);
	int GetPrivatekeyMD5(CHAR *pszPrivatekeyMD5, int nSize);
	int GetRfidMD5(CHAR *pszRfidMD5, int nSize);
	int GetAddrMD5(CHAR *pszAddrMD5, int nSize);
	int GetConfigMD5(CHAR *pszConfigMD5, int nSize);
	int GetFaceIDMD5(CHAR *pszFaceIDMD5, int nSize);
	int SetFaceIDMD5(CHAR *pszFaceIDMD5);
	int GetDownLoadFacePicMD5(CHAR *pszDownLoadFacePicMD5, int nSize);
	int SetDownLoadFacePicMD5(CHAR *pszDownLoadFacePicMD5);
	int GetAdModuleMD5(CHAR *pszAdModuleMD5, int nSize);
	int GetCommunityPhonebookMD5(CHAR *pszCommunityPhonebookMD5, int nSize);
	int GetContactMD5(CHAR *pszContactMD5, int nSize);
	int GetCloudServerEnable();
	int SetCloudServerEnable(int nEnable);
	int GetCloudServerIp(CHAR *pszCloudServerIp, int nSize);
	int GetCloudServerPort();
	int SetCloudServerIp(CHAR *pszCloudServerIp);
	int SetCloudServerPort(INT nPort);
	int SetCloudServerRpsEnable(int nEnable);
	int GetCloudServerRpsEnable();
	int SetCloudServerRpsServer(CHAR *pszRpsServer);
	int GetCloudServerRpsServer(CHAR *pszRpsServer, int nSize);	
	int SetCloudServerGate(CHAR *pszGateServerIp, INT nGateServerPort);
	int GetCloudServerGateServer(CHAR *pszGateServerIp, int nSize);
	int GetCloudServerGatePort();
	int SetCloudServerToken(CHAR *pszGateToken);
	int GetCloudServerToken(CHAR *pszGateToken, int nSize);
	int SetPrivatekeyMD5(CHAR *pszPrivatekeyMD5);
	int SetRfidMD5(CHAR *pszRfidMD5);
	int SetAddrMD5(CHAR *pszAddrMD5);
	int SetConfigMD5(CHAR *pszConfigMD5);
	int SetAdModuleMD5(CHAR *pszAdModuleMD5);
	int SetContactMD5(CHAR *pszContactMD5);
	int SetCommunityPhoneBookMD5(CHAR *pszCommunityPhonebookMD5);
	int GetConfigModule(CONFIG_MODULE *pConfigModule);
#if RL_SUPPORT_REPORT_CONFIG_TO_DEVICE
	int GetConfigModule(CONFIG_MODULE_FROM_DEVICE* pConfigModule);
#endif
	int GetConfigModule(CHAR *pszConfigModule, int nSize);
	int SetConfigModule(CONFIG_MODULE *pConfigModule, int *pnCfgIDList, int nSize);
	int SetConfigModule(CHAR *pszConfigModule);
	int SetRemoteBook(CHAR *pszRemoteBook);
	int SetConnectMode(INT nConnectMode);
	int GetConnectMode();
	int GetDiscoveryMode();
	int GetDeviceName(CHAR *pszDeviceName, int nSize);
	int SetDeviceAccountStatus(bool bAccountStatus);
	int GetDeviceCode(CHAR *pszDeviceCode, int nSize);
	int SetDeviceCode(CHAR *pszDeviceCode);
	int GetDeviceLocation(CHAR *pszDeviceLocation, int nSize);
#if RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER
	int GetNoAnswerFwdNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pNoAnswerFwdNumber);
	int SetNoAnswerFwdNumber(DCLIENT_REPORT_NOANSWER_FWD_NUMBER *pNoAnswerFwdNumber);
#endif
#if RL_SUPPORT_AUTH_CODE
	int GetAuthKey(CHAR *pszAuthKey, int nSize);
	int SetAuthKey(CHAR *pszAuthKey);
#endif
	int SetSDMCServer(CHAR *pszSDMCServerIp, INT nSDMCServerPort);
	int GetSDMCServerIP(CHAR *pszSDMCServerIp, int nSize);
	int GetSDMCServerPort();
	int SetWebSrv(CHAR *pszServer);
	int GetWebSrv(CHAR *pszServer, int nSize);
	int SetPBXSrv(CHAR *pszServer);
	int SetFtpSrv(CHAR *pszServer);
	int SetVrtspSrv(CHAR *pszServer);
	int SetFPMD5(CHAR *pszFPD5);
	int GetFPMD5(CHAR *pszFPD5, int nSize);
	int SetSrvName(CHAR *pszSrvName);
	int GetSrvName(CHAR *pszSrvName, int nSize);
#if RL_SUPPORT_ARMING
	int GetArmingType();
#endif

	int SetRemoteSyslog(int on, CHAR *pszServer, int nLogLevel);
	int GetSSHPassSrv(CHAR *pszSSHPassSrv, int nSize);
	int GetDeviceSipUser(CHAR *pszSipUser, int nSize);
	int GetDeviceSipRegStatus();
	int GetMaintenanceAlarm();
	int GetTzMD5(CHAR *pszTzMD5, int nSize);
	int GetTzDataMD5(CHAR *pszTzDataMD5, int nSize);
	int GetFaceSyncMD5(CHAR *pszFaceSyncMD5, int nSize);
	int SetFaceSyncPicMD5(CHAR *pszMD5);
	int GetACInfoMD5(CHAR *pszACInfoMD5, int nSize);
	int SetACInfoMD5(CHAR *pszMD5);
	int GetACMetaMD5(CHAR *pszACMetaMD5, int nSize);
	int SetACMetaMD5(CHAR *pszMD5);
	int SetAttendanceSrv(CHAR *pszService);
	int GetAttendanceSrv(CHAR *pszService, int nSize);
	int SetScheduleMD5(CHAR *pszMD5);
	int GetScheduleMD5(CHAR *pszMD5, int nSize);
	int GetRelayStatus(CHAR *pszData, int nSize);
	int GetSubServerMode();
	int GetModelName(CHAR *pszModelName, int nSize);
	static CSettingHandle *GetInstance();
private:
	int AddCfgIDToList(int *pnList, int nSize, int nCfgID);
	static CSettingHandle *instance;
	
};

CSettingHandle *GetSettingHandleInstance();

#endif

