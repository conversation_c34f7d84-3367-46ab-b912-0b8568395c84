/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */


#ifndef SWIGJAVA
#define SWIGJAVA
#endif

#define SWIG_DIRECTORS


#ifdef __cplusplus
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigMovePointer {
    T *ptr;
    SwigMovePointer(T *p) : ptr(p) { }
    ~SwigMovePointer() { delete ptr; }
    SwigMovePointer& operator=(SwigMovePointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigMovePointer tmp(new T(t)); pointer = tmp; return *this; }
  operator T&() const { return *pointer.ptr; }
  T *operator&() { return pointer.ptr; }
};

template <typename T> T SwigValueInit() {
  return T();
}
#endif

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif


/* Fix for jlong on some versions of gcc on Windows */
#if defined(__GNUC__) && !defined(__INTEL_COMPILER)
  typedef long long __int64;
#endif

/* Fix for jlong on 64-bit x86 Solaris */
#if defined(__x86_64)
# ifdef _LP64
#   undef _LP64
# endif
#endif

#include <jni.h>
#include <stdlib.h>
#include <string.h>


/* Support for throwing Java exceptions */
typedef enum {
  SWIG_JavaOutOfMemoryError = 1,
  SWIG_JavaIOException,
  SWIG_JavaRuntimeException,
  SWIG_JavaIndexOutOfBoundsException,
  SWIG_JavaArithmeticException,
  SWIG_JavaIllegalArgumentException,
  SWIG_JavaNullPointerException,
  SWIG_JavaDirectorPureVirtual,
  SWIG_JavaUnknownError,
  SWIG_JavaIllegalStateException,
} SWIG_JavaExceptionCodes;

typedef struct {
  SWIG_JavaExceptionCodes code;
  const char *java_exception;
} SWIG_JavaExceptions_t;


static void SWIGUNUSED SWIG_JavaThrowException(JNIEnv *jenv, SWIG_JavaExceptionCodes code, const char *msg) {
  jclass excep;
  static const SWIG_JavaExceptions_t java_exceptions[] = {
    { SWIG_JavaOutOfMemoryError, "java/lang/OutOfMemoryError" },
    { SWIG_JavaIOException, "java/io/IOException" },
    { SWIG_JavaRuntimeException, "java/lang/RuntimeException" },
    { SWIG_JavaIndexOutOfBoundsException, "java/lang/IndexOutOfBoundsException" },
    { SWIG_JavaArithmeticException, "java/lang/ArithmeticException" },
    { SWIG_JavaIllegalArgumentException, "java/lang/IllegalArgumentException" },
    { SWIG_JavaNullPointerException, "java/lang/NullPointerException" },
    { SWIG_JavaDirectorPureVirtual, "java/lang/RuntimeException" },
    { SWIG_JavaUnknownError,  "java/lang/UnknownError" },
    { SWIG_JavaIllegalStateException, "java/lang/IllegalStateException" },
    { (SWIG_JavaExceptionCodes)0,  "java/lang/UnknownError" }
  };
  const SWIG_JavaExceptions_t *except_ptr = java_exceptions;

  while (except_ptr->code != code && except_ptr->code)
    except_ptr++;

  jenv->ExceptionClear();
  excep = jenv->FindClass(except_ptr->java_exception);
  if (excep)
    jenv->ThrowNew(excep, msg);
}


/* Contract support */

#define SWIG_contract_assert(nullreturn, expr, msg) if (!(expr)) {SWIG_JavaThrowException(jenv, SWIG_JavaIllegalArgumentException, msg); return nullreturn; } else

/* -----------------------------------------------------------------------------
 * director_common.swg
 *
 * This file contains support for director classes which is common between
 * languages.
 * ----------------------------------------------------------------------------- */

/*
  Use -DSWIG_DIRECTOR_STATIC if you prefer to avoid the use of the
  'Swig' namespace. This could be useful for multi-modules projects.
*/
#ifdef SWIG_DIRECTOR_STATIC
/* Force anonymous (static) namespace */
#define Swig
#endif
/* -----------------------------------------------------------------------------
 * director.swg
 *
 * This file contains support for director classes so that Java proxy
 * methods can be called from C++.
 * ----------------------------------------------------------------------------- */

#if defined(DEBUG_DIRECTOR_OWNED) || defined(DEBUG_DIRECTOR_EXCEPTION) || defined(DEBUG_DIRECTOR_THREAD_NAME)
#include <iostream>
#endif

#include <exception>

#if defined(SWIG_JAVA_USE_THREAD_NAME)

#if !defined(SWIG_JAVA_GET_THREAD_NAME)
namespace Swig {
  SWIGINTERN int GetThreadName(char *name, size_t len);
}

#if defined(__linux__)

#include <sys/prctl.h>
SWIGINTERN int Swig::GetThreadName(char *name, size_t len) {
  (void)len;
#if defined(PR_GET_NAME)
  return prctl(PR_GET_NAME, (unsigned long)name, 0, 0, 0);
#else
  (void)name;
  return 1;
#endif
}

#elif defined(__unix__) || defined(__APPLE__)

#include <pthread.h>
SWIGINTERN int Swig::GetThreadName(char *name, size_t len) {
  return pthread_getname_np(pthread_self(), name, len);
}

#else

SWIGINTERN int Swig::GetThreadName(char *name, size_t len) {
  (void)len;
  (void)name;
  return 1;
}
#endif

#endif

#endif

namespace Swig {

  /* Java object wrapper */
  class JObjectWrapper {
  public:
    JObjectWrapper() : jthis_(NULL), weak_global_(true) {
    }

    ~JObjectWrapper() {
      jthis_ = NULL;
      weak_global_ = true;
    }

    bool set(JNIEnv *jenv, jobject jobj, bool mem_own, bool weak_global) {
      if (!jthis_) {
        weak_global_ = weak_global || !mem_own; // hold as weak global if explicitly requested or not owned
        if (jobj)
          jthis_ = weak_global_ ? jenv->NewWeakGlobalRef(jobj) : jenv->NewGlobalRef(jobj);
#if defined(DEBUG_DIRECTOR_OWNED)
        std::cout << "JObjectWrapper::set(" << jobj << ", " << (weak_global ? "weak_global" : "global_ref") << ") -> " << jthis_ << std::endl;
#endif
        return true;
      } else {
#if defined(DEBUG_DIRECTOR_OWNED)
        std::cout << "JObjectWrapper::set(" << jobj << ", " << (weak_global ? "weak_global" : "global_ref") << ") -> already set" << std::endl;
#endif
        return false;
      }
    }

    jobject get(JNIEnv *jenv) const {
#if defined(DEBUG_DIRECTOR_OWNED)
      std::cout << "JObjectWrapper::get(";
      if (jthis_)
        std::cout << jthis_;
      else
        std::cout << "null";
      std::cout << ") -> return new local ref" << std::endl;
#endif
      return (jthis_ ? jenv->NewLocalRef(jthis_) : jthis_);
    }

    void release(JNIEnv *jenv) {
#if defined(DEBUG_DIRECTOR_OWNED)
      std::cout << "JObjectWrapper::release(" << jthis_ << "): " << (weak_global_ ? "weak global ref" : "global ref") << std::endl;
#endif
      if (jthis_) {
        if (weak_global_) {
          if (jenv->IsSameObject(jthis_, NULL) == JNI_FALSE)
            jenv->DeleteWeakGlobalRef((jweak)jthis_);
        } else
          jenv->DeleteGlobalRef(jthis_);
      }

      jthis_ = NULL;
      weak_global_ = true;
    }

    /* Only call peek if you know what you are doing wrt to weak/global references */
    jobject peek() {
      return jthis_;
    }

    /* Java proxy releases ownership of C++ object, C++ object is now
       responsible for destruction (creates NewGlobalRef to pin Java proxy) */
    void java_change_ownership(JNIEnv *jenv, jobject jself, bool take_or_release) {
      if (take_or_release) {  /* Java takes ownership of C++ object's lifetime. */
        if (!weak_global_) {
          jenv->DeleteGlobalRef(jthis_);
          jthis_ = jenv->NewWeakGlobalRef(jself);
          weak_global_ = true;
        }
      } else {
	/* Java releases ownership of C++ object's lifetime */
        if (weak_global_) {
          jenv->DeleteWeakGlobalRef((jweak)jthis_);
          jthis_ = jenv->NewGlobalRef(jself);
          weak_global_ = false;
        }
      }
    }

  private:
    /* pointer to Java object */
    jobject jthis_;
    /* Local or global reference flag */
    bool weak_global_;
  };

  /* Local JNI reference deleter */
  class LocalRefGuard {
    JNIEnv *jenv_;
    jobject jobj_;

    // non-copyable
    LocalRefGuard(const LocalRefGuard &);
    LocalRefGuard &operator=(const LocalRefGuard &);
  public:
    LocalRefGuard(JNIEnv *jenv, jobject jobj): jenv_(jenv), jobj_(jobj) {}
    ~LocalRefGuard() {
      if (jobj_)
        jenv_->DeleteLocalRef(jobj_);
    }
  };

  /* director base class */
  class Director {
    /* pointer to Java virtual machine */
    JavaVM *swig_jvm_;

  protected:
#if defined (_MSC_VER) && (_MSC_VER<1300)
    class JNIEnvWrapper;
    friend class JNIEnvWrapper;
#endif
    /* Utility class for managing the JNI environment */
    class JNIEnvWrapper {
      const Director *director_;
      JNIEnv *jenv_;
      int env_status;
    public:
      JNIEnvWrapper(const Director *director) : director_(director), jenv_(0), env_status(0) {
#if defined(__ANDROID__)
        JNIEnv **jenv = &jenv_;
#else
        void **jenv = (void **)&jenv_;
#endif
        env_status = director_->swig_jvm_->GetEnv((void **)&jenv_, JNI_VERSION_1_2);
        JavaVMAttachArgs args;
        args.version = JNI_VERSION_1_2;
        args.group = NULL;
        args.name = NULL;
#if defined(SWIG_JAVA_USE_THREAD_NAME)
        char thread_name[64];  // MAX_TASK_COMM_LEN=16 is hard-coded in the Linux kernel and MacOS has MAXTHREADNAMESIZE=64.
        if (Swig::GetThreadName(thread_name, sizeof(thread_name)) == 0) {
          args.name = thread_name;
#if defined(DEBUG_DIRECTOR_THREAD_NAME)
          std::cout << "JNIEnvWrapper: thread name: " << thread_name << std::endl;
        } else {
          std::cout << "JNIEnvWrapper: Couldn't set Java thread name" << std::endl;
#endif
        }
#endif
#if defined(SWIG_JAVA_ATTACH_CURRENT_THREAD_AS_DAEMON)
        // Attach a daemon thread to the JVM. Useful when the JVM should not wait for
        // the thread to exit upon shutdown. Only for jdk-1.4 and later.
        director_->swig_jvm_->AttachCurrentThreadAsDaemon(jenv, &args);
#else
        director_->swig_jvm_->AttachCurrentThread(jenv, &args);
#endif
      }
      ~JNIEnvWrapper() {
#if !defined(SWIG_JAVA_NO_DETACH_CURRENT_THREAD)
        // Some JVMs, eg jdk-1.4.2 and lower on Solaris have a bug and crash with the DetachCurrentThread call.
        // However, without this call, the JVM hangs on exit when the thread was not created by the JVM and creates a memory leak.
        if (env_status == JNI_EDETACHED)
          director_->swig_jvm_->DetachCurrentThread();
#endif
      }
      JNIEnv *getJNIEnv() const {
        return jenv_;
      }
    };

    /* Java object wrapper */
    JObjectWrapper swig_self_;

    /* Disconnect director from Java object */
    void swig_disconnect_director_self(const char *disconn_method) {
      JNIEnvWrapper jnienv(this) ;
      JNIEnv *jenv = jnienv.getJNIEnv() ;
      jobject jobj = swig_self_.get(jenv);
      LocalRefGuard ref_deleter(jenv, jobj);
#if defined(DEBUG_DIRECTOR_OWNED)
      std::cout << "Swig::Director::disconnect_director_self(" << jobj << ")" << std::endl;
#endif
      if (jobj && jenv->IsSameObject(jobj, NULL) == JNI_FALSE) {
        jmethodID disconn_meth = jenv->GetMethodID(jenv->GetObjectClass(jobj), disconn_method, "()V");
        if (disconn_meth) {
#if defined(DEBUG_DIRECTOR_OWNED)
          std::cout << "Swig::Director::disconnect_director_self upcall to " << disconn_method << std::endl;
#endif
          jenv->CallVoidMethod(jobj, disconn_meth);
        }
      }
    }

  public:
    Director(JNIEnv *jenv) : swig_jvm_((JavaVM *) NULL), swig_self_() {
      /* Acquire the Java VM pointer */
      jenv->GetJavaVM(&swig_jvm_);
    }

    virtual ~Director() {
      JNIEnvWrapper jnienv(this) ;
      JNIEnv *jenv = jnienv.getJNIEnv() ;
      swig_self_.release(jenv);
    }

    bool swig_set_self(JNIEnv *jenv, jobject jself, bool mem_own, bool weak_global) {
      return swig_self_.set(jenv, jself, mem_own, weak_global);
    }

    jobject swig_get_self(JNIEnv *jenv) const {
      return swig_self_.get(jenv);
    }

    // Change C++ object's ownership, relative to Java
    void swig_java_change_ownership(JNIEnv *jenv, jobject jself, bool take_or_release) {
      swig_self_.java_change_ownership(jenv, jself, take_or_release);
    }
  };

  // Zero initialized bool array
  template<size_t N> class BoolArray {
    bool array_[N];
  public:
    BoolArray() {
      memset(array_, 0, sizeof(array_));
    }
    bool& operator[](size_t n) {
      return array_[n];
    }
    bool operator[](size_t n) const {
      return array_[n];
    }
  };

  // Utility classes and functions for exception handling.

  // Simple holder for a Java string during exception handling, providing access to a c-style string
  class JavaString {
  public:
    JavaString(JNIEnv *jenv, jstring jstr) : jenv_(jenv), jstr_(jstr), cstr_(0) {
      if (jenv_ && jstr_)
	cstr_ = (const char *) jenv_->GetStringUTFChars(jstr_, NULL);
    }

    ~JavaString() {
      if (jenv_ && jstr_ && cstr_)
	jenv_->ReleaseStringUTFChars(jstr_, cstr_);
    }

    const char *c_str(const char *null_string = "null JavaString") const {
      return cstr_ ? cstr_ : null_string;
    }

  private:
    // non-copyable
    JavaString(const JavaString &);
    JavaString &operator=(const JavaString &);

    JNIEnv *jenv_;
    jstring jstr_;
    const char *cstr_;
  };

  // Helper class to extract the exception message from a Java throwable
  class JavaExceptionMessage {
  public:
    JavaExceptionMessage(JNIEnv *jenv, jthrowable throwable) : message_(jenv, exceptionMessageFromThrowable(jenv, throwable)) {
    }

    // Return a C string of the exception message in the jthrowable passed in the constructor
    // If no message is available, null_string is return instead
    const char *message(const char *null_string = "Could not get exception message in JavaExceptionMessage") const {
      return message_.c_str(null_string);
    }

  private:
    // non-copyable
    JavaExceptionMessage(const JavaExceptionMessage &);
    JavaExceptionMessage &operator=(const JavaExceptionMessage &);

    // Get exception message by calling Java method Throwable.getMessage()
    static jstring exceptionMessageFromThrowable(JNIEnv *jenv, jthrowable throwable) {
      jstring jmsg = NULL;
      if (jenv && throwable) {
	jenv->ExceptionClear(); // Cannot invoke methods with any pending exceptions
	jclass throwclz = jenv->GetObjectClass(throwable);
	if (throwclz) {
	  // All Throwable classes have a getMessage() method, so call it to extract the exception message
	  jmethodID getMessageMethodID = jenv->GetMethodID(throwclz, "getMessage", "()Ljava/lang/String;");
	  if (getMessageMethodID)
	    jmsg = (jstring)jenv->CallObjectMethod(throwable, getMessageMethodID);
	}
	if (jmsg == NULL && jenv->ExceptionCheck())
	  jenv->ExceptionClear();
      }
      return jmsg;
    }

    JavaString message_;
  };

  // C++ Exception class for handling Java exceptions thrown during a director method Java upcall
  class DirectorException : public std::exception {
  public:

    // Construct exception from a Java throwable
    DirectorException(JNIEnv *jenv, jthrowable throwable) : jenv_(jenv), throwable_(throwable), classname_(0), msg_(0) {

      // Call Java method Object.getClass().getName() to obtain the throwable's class name (delimited by '/')
      if (jenv && throwable) {
	jenv->ExceptionClear(); // Cannot invoke methods with any pending exceptions
	jclass throwclz = jenv->GetObjectClass(throwable);
	if (throwclz) {
	  jclass clzclz = jenv->GetObjectClass(throwclz);
	  if (clzclz) {
	    jmethodID getNameMethodID = jenv->GetMethodID(clzclz, "getName", "()Ljava/lang/String;");
	    if (getNameMethodID) {
	      jstring jstr_classname = (jstring)(jenv->CallObjectMethod(throwclz, getNameMethodID));
              // Copy strings, since there is no guarantee that jenv will be active when handled
              if (jstr_classname) {
                JavaString jsclassname(jenv, jstr_classname);
                const char *classname = jsclassname.c_str(0);
                if (classname)
                  classname_ = copypath(classname);
              }
	    }
	  }
	}
      }

      JavaExceptionMessage exceptionmsg(jenv, throwable);
      msg_ = copystr(exceptionmsg.message(0));
    }

    // More general constructor for handling as a java.lang.RuntimeException
    DirectorException(const char *msg) : jenv_(0), throwable_(0), classname_(0), msg_(msg ? copystr(msg) : 0) {
    }

    ~DirectorException() throw() {
      delete[] classname_;
      delete[] msg_;
    }

    const char *what() const throw() {
      return msg_ ? msg_ : "Unspecified DirectorException message";
    }

    // Reconstruct and raise/throw the Java Exception that caused the DirectorException
    // Note that any error in the JNI exception handling results in a Java RuntimeException
    void throwException(JNIEnv *jenv) const {
      if (jenv) {
        if (jenv == jenv_ && throwable_) {
          // Throw original exception if not already pending
          jthrowable throwable = jenv->ExceptionOccurred();
          if (throwable && jenv->IsSameObject(throwable, throwable_) == JNI_FALSE) {
            jenv->ExceptionClear();
            throwable = 0;
          }
          if (!throwable)
            jenv->Throw(throwable_);
        } else {
          // Try and reconstruct original exception, but original stacktrace is not reconstructed
          jenv->ExceptionClear();

          jmethodID ctorMethodID = 0;
          jclass throwableclass = 0;
          if (classname_) {
            throwableclass = jenv->FindClass(classname_);
            if (throwableclass)
              ctorMethodID = jenv->GetMethodID(throwableclass, "<init>", "(Ljava/lang/String;)V");
          }

          if (ctorMethodID) {
            jenv->ThrowNew(throwableclass, what());
          } else {
            SWIG_JavaThrowException(jenv, SWIG_JavaRuntimeException, what());
          }
        }
      }
    }

    // Deprecated - use throwException
    void raiseJavaException(JNIEnv *jenv) const {
      throwException(jenv);
    }

    // Create and throw the DirectorException
    static void raise(JNIEnv *jenv, jthrowable throwable) {
      throw DirectorException(jenv, throwable);
    }

  private:
    static char *copypath(const char *srcmsg) {
      char *target = copystr(srcmsg);
      for (char *c=target; *c; ++c) {
        if ('.' == *c)
          *c = '/';
      }
      return target;
    }

    static char *copystr(const char *srcmsg) {
      char *target = 0;
      if (srcmsg) {
	size_t msglen = strlen(srcmsg) + 1;
	target = new char[msglen];
	strncpy(target, srcmsg, msglen);
      }
      return target;
    }

    JNIEnv *jenv_;
    jthrowable throwable_;
    const char *classname_;
    const char *msg_;
  };

  // Helper method to determine if a Java throwable matches a particular Java class type
  // Note side effect of clearing any pending exceptions
  SWIGINTERN bool ExceptionMatches(JNIEnv *jenv, jthrowable throwable, const char *classname) {
    bool matches = false;

    if (throwable && jenv && classname) {
      // Exceptions need to be cleared for correct behavior.
      // The caller of ExceptionMatches should restore pending exceptions if desired -
      // the caller already has the throwable.
      jenv->ExceptionClear();

      jclass clz = jenv->FindClass(classname);
      if (clz) {
	jclass classclz = jenv->GetObjectClass(clz);
	jmethodID isInstanceMethodID = jenv->GetMethodID(classclz, "isInstance", "(Ljava/lang/Object;)Z");
	if (isInstanceMethodID) {
	  matches = jenv->CallBooleanMethod(clz, isInstanceMethodID, throwable) != 0;
	}
      }

#if defined(DEBUG_DIRECTOR_EXCEPTION)
      if (jenv->ExceptionCheck()) {
        // Typically occurs when an invalid classname argument is passed resulting in a ClassNotFoundException
        JavaExceptionMessage exc(jenv, jenv->ExceptionOccurred());
        std::cout << "Error: ExceptionMatches: class '" << classname << "' : " << exc.message() << std::endl;
      }
#endif
    }
    return matches;
  }
}

namespace Swig {
  namespace {
    jclass jclass_dclientJNI = NULL;
    jmethodID director_method_ids[7];
  }
}

	#include "../Def.h"
	#include "../IDClient.h"
	#include "../IDClientDelegate.h"



/* ---------------------------------------------------
 * C++ director class methods
 * --------------------------------------------------- */

#include "dclient_wrap.h"

SwigDirector_IDClientDelegate::SwigDirector_IDClientDelegate(JNIEnv *jenv) : IDClientDelegate(), Swig::Director(jenv) {
}

SwigDirector_IDClientDelegate::~SwigDirector_IDClientDelegate() {
  swig_disconnect_director_self("swigDirectorDisconnect");
}


int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_NOTIFY_HANDLE_FILE_INFO *pData, int nDataSize) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  jlong jpData = 0 ;
  jint jnDataSize  ;
  
  if (!swig_override[0]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    *((DCLIENT_NOTIFY_HANDLE_FILE_INFO **)&jpData) = (DCLIENT_NOTIFY_HANDLE_FILE_INFO *) pData; 
    jnDataSize = (jint) nDataSize;
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[0], swigjobj, jnMsgID, juParam1, juParam2, jpData, jnDataSize);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_SIP_INFO *pSipInfo, int nDataSize) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  jlong jpSipInfo = 0 ;
  jint jnDataSize  ;
  
  if (!swig_override[1]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    *((DCLIENT_SIP_INFO **)&jpSipInfo) = (DCLIENT_SIP_INFO *) pSipInfo; 
    jnDataSize = (jint) nDataSize;
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[1], swigjobj, jnMsgID, juParam1, juParam2, jpSipInfo, jnDataSize);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_OWNER_MSG *pTextMsg, int nDataSize) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  jlong jpTextMsg = 0 ;
  jint jnDataSize  ;
  
  if (!swig_override[2]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    *((DCLIENT_OWNER_MSG **)&jpTextMsg) = (DCLIENT_OWNER_MSG *) pTextMsg; 
    jnDataSize = (jint) nDataSize;
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[2], swigjobj, jnMsgID, juParam1, juParam2, jpTextMsg, jnDataSize);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, char const *pData) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  jstring jpData = 0 ;
  
  if (!swig_override[3]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    jpData = 0;
    if (pData) {
      jpData = jenv->NewStringUTF((const char *)pData);
      if (!jpData) return c_result;
    }
    Swig::LocalRefGuard pData_refguard(jenv, jpData);
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[3], swigjobj, jnMsgID, juParam1, juParam2, jpData);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, int nData) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  jint jnData  ;
  
  if (!swig_override[4]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    jnData = (jint) nData;
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[4], swigjobj, jnMsgID, juParam1, juParam2, jnData);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

int SwigDirector_IDClientDelegate::OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2) {
  int c_result = SwigValueInit< int >() ;
  jint jresult = 0 ;
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jint jnMsgID  ;
  jlong juParam1  ;
  jlong juParam2  ;
  
  if (!swig_override[5]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OnMessage.");
    return c_result;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jnMsgID = (jint) nMsgID;
    juParam1 = (jlong) uParam1;
    juParam2 = (jlong) uParam2;
    jresult = (jint) jenv->CallStaticIntMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[5], swigjobj, jnMsgID, juParam1, juParam2);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
    c_result = (int)jresult; 
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OnMessage ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
  return c_result;
}

void SwigDirector_IDClientDelegate::OuputLog(char const *pstrLog) {
  JNIEnvWrapper swigjnienv(this) ;
  JNIEnv * jenv = swigjnienv.getJNIEnv() ;
  jobject swigjobj = (jobject) NULL ;
  jstring jpstrLog = 0 ;
  
  if (!swig_override[6]) {
    SWIG_JavaThrowException(JNIEnvWrapper(this).getJNIEnv(), SWIG_JavaDirectorPureVirtual, "Attempted to invoke pure virtual method IDClientDelegate::OuputLog.");
    return;
  }
  swigjobj = swig_get_self(jenv);
  if (swigjobj && jenv->IsSameObject(swigjobj, NULL) == JNI_FALSE) {
    jpstrLog = 0;
    if (pstrLog) {
      jpstrLog = jenv->NewStringUTF((const char *)pstrLog);
      if (!jpstrLog) return ;
    }
    Swig::LocalRefGuard pstrLog_refguard(jenv, jpstrLog);
    jenv->CallStaticVoidMethod(Swig::jclass_dclientJNI, Swig::director_method_ids[6], swigjobj, jpstrLog);
    jthrowable swigerror = jenv->ExceptionOccurred();
    if (swigerror) {
      Swig::DirectorException::raise(jenv, swigerror);
    }
    
  } else {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "null upcall object in IDClientDelegate::OuputLog ");
  }
  if (swigjobj) jenv->DeleteLocalRef(swigjobj);
}

void SwigDirector_IDClientDelegate::swig_connect_director(JNIEnv *jenv, jobject jself, jclass jcls, bool swig_mem_own, bool weak_global) {
  static struct {
    const char *mname;
    const char *mdesc;
    jmethodID base_methid;
  } methods[] = {
    {
      "OnMessage", "(IJJLcom/akuvox/mobile/libcommon/wrapper/dclient/jni/DCLIENT_NOTIFY_HANDLE_FILE_INFO;I)I", NULL 
    },
    {
      "OnMessage", "(IJJLcom/akuvox/mobile/libcommon/wrapper/dclient/jni/DCLIENT_SIP_INFO;I)I", NULL 
    },
    {
      "OnMessage", "(IJJLcom/akuvox/mobile/libcommon/wrapper/dclient/jni/DCLIENT_OWNER_MSG;I)I", NULL 
    },
    {
      "OnMessage", "(IJJLjava/lang/String;)I", NULL 
    },
    {
      "OnMessage", "(IJJI)I", NULL 
    },
    {
      "OnMessage", "(IJJ)I", NULL 
    },
    {
      "OuputLog", "(Ljava/lang/String;)V", NULL 
    }
  };
  
  static jclass baseclass = 0 ;
  
  if (swig_set_self(jenv, jself, swig_mem_own, weak_global)) {
    if (!baseclass) {
      baseclass = jenv->FindClass("com/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate");
      if (!baseclass) return;
      baseclass = (jclass) jenv->NewGlobalRef(baseclass);
    }
    bool derived = (jenv->IsSameObject(baseclass, jcls) ? false : true);
    for (int i = 0; i < 7; ++i) {
      if (!methods[i].base_methid) {
        methods[i].base_methid = jenv->GetMethodID(baseclass, methods[i].mname, methods[i].mdesc);
        if (!methods[i].base_methid) return;
      }
      swig_override[i] = false;
      if (derived) {
        jmethodID methid = jenv->GetMethodID(jcls, methods[i].mname, methods[i].mdesc);
        swig_override[i] = (methid != methods[i].base_methid);
        jenv->ExceptionClear();
      }
    }
  }
}



#ifdef __cplusplus
extern "C" {
#endif

SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SEQUENCE_1NUM_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)((16));
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IP_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)((16));
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1VALUE_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)((64));
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1MAC_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)((20));
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1MD5_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)((36));
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1URL_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)(256);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1TIME_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)(24);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1CONFIG_1CHANGED_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_CONFIG_CHANGED;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1CONTACT_1CHANGED_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_CONTACT_CHANGED;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1CONNECT_1STATUS_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_CONNECT_STATUS;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1BOOTUP_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_BOOTUP;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1SIP_1INFO_1CHANGED_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_SIP_INFO_CHANGED;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1DEVICE_1LOCATION_1CHANGED_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_DEVICE_LOCATION_CHANGED;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1IPC_1NOTIFY_1RECEIVE_1OWNER_1MESSAGE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  DCLIENT_IPC_MSG_TYPE result;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_IPC_MSG_TYPE)DCLIENT_IPC_NOTIFY_RECEIVE_OWNER_MESSAGE;
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_new_1DCLIENT_1DEVICE_1INFO(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  DCLIENT_DEVICE_INFO *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_DEVICE_INFO *)new DCLIENT_DEVICE_INFO();
  *(DCLIENT_DEVICE_INFO **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szModelName_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szModelName, (const char *)arg2, (64)-1);
      arg1->szModelName[(64)-1] = 0;
    } else {
      arg1->szModelName[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szModelName_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szModelName);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szIPAddr_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szIPAddr, (const char *)arg2, (64)-1);
      arg1->szIPAddr[(64)-1] = 0;
    } else {
      arg1->szIPAddr[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szIPAddr_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szIPAddr);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szMask_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szMask, (const char *)arg2, (64)-1);
      arg1->szMask[(64)-1] = 0;
    } else {
      arg1->szMask[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szMask_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szMask);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szGateWay_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szGateWay, (const char *)arg2, (64)-1);
      arg1->szGateWay[(64)-1] = 0;
    } else {
      arg1->szGateWay[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szGateWay_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szGateWay);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szPrimaryDNS_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szPrimaryDNS, (const char *)arg2, (64)-1);
      arg1->szPrimaryDNS[(64)-1] = 0;
    } else {
      arg1->szPrimaryDNS[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szPrimaryDNS_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szPrimaryDNS);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szSecondaryDNS_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szSecondaryDNS, (const char *)arg2, (64)-1);
      arg1->szSecondaryDNS[(64)-1] = 0;
    } else {
      arg1->szSecondaryDNS[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szSecondaryDNS_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szSecondaryDNS);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szMAC_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szMAC, (const char *)arg2, (20)-1);
      arg1->szMAC[(20)-1] = 0;
    } else {
      arg1->szMAC[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1DEVICE_1INFO_1szMAC_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szMAC);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1DCLIENT_1DEVICE_1INFO(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  DCLIENT_DEVICE_INFO *arg1 = (DCLIENT_DEVICE_INFO *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(DCLIENT_DEVICE_INFO **)&jarg1; 
  delete arg1;
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_new_1DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *)new DCLIENT_NOTIFY_HANDLE_FILE_INFO_T();
  *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO_1szFilePath_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *arg1 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szFilePath, (const char *)arg2, 256-1);
      arg1->szFilePath[256-1] = 0;
    } else {
      arg1->szFilePath[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO_1szFilePath_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *arg1 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jarg1; 
  result = (char *)(char *) ((arg1)->szFilePath);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO_1szMD5_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *arg1 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szMD5, (const char *)arg2, (36)-1);
      arg1->szMD5[(36)-1] = 0;
    } else {
      arg1->szMD5[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO_1szMD5_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *arg1 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jarg1; 
  result = (char *)(char *) ((arg1)->szMD5);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1DCLIENT_1NOTIFY_1HANDLE_1FILE_1INFO(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *arg1 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO_T *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO_T **)&jarg1; 
  delete arg1;
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_new_1DCLIENT_1SIP_1INFO(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  DCLIENT_SIP_INFO *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_SIP_INFO *)new DCLIENT_SIP_INFO();
  *(DCLIENT_SIP_INFO **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1nServerPort_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2) {
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  int arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  arg2 = (int)jarg2; 
  if (arg1) (arg1)->nServerPort = arg2;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1nServerPort_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jint jresult = 0 ;
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  result = (int) ((arg1)->nServerPort);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szServer_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szServer, (const char *)arg2, 256-1);
      arg1->szServer[256-1] = 0;
    } else {
      arg1->szServer[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szServer_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szServer);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szUserName_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szUserName, (const char *)arg2, (64)-1);
      arg1->szUserName[(64)-1] = 0;
    } else {
      arg1->szUserName[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szUserName_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szUserName);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szPassword_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->szPassword, (const char *)arg2, (64)-1);
      arg1->szPassword[(64)-1] = 0;
    } else {
      arg1->szPassword[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1SIP_1INFO_1szPassword_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  result = (char *)(char *) ((arg1)->szPassword);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1DCLIENT_1SIP_1INFO(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  DCLIENT_SIP_INFO *arg1 = (DCLIENT_SIP_INFO *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(DCLIENT_SIP_INFO **)&jarg1; 
  delete arg1;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1TITLE_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)(128);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1CONTENT_1SIZE_1get(JNIEnv *jenv, jclass jcls) {
  jint jresult = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  result = (int)(1024);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_new_1DCLIENT_1OWNER_1MSG(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  DCLIENT_OWNER_MSG_T *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (DCLIENT_OWNER_MSG_T *)new DCLIENT_OWNER_MSG_T();
  *(DCLIENT_OWNER_MSG_T **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1title_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->title, (const char *)arg2, 128-1);
      arg1->title[128-1] = 0;
    } else {
      arg1->title[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1title_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  result = (char *)(char *) ((arg1)->title);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1content_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->content, (const char *)arg2, 1024-1);
      arg1->content[1024-1] = 0;
    } else {
      arg1->content[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1content_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  result = (char *)(char *) ((arg1)->content);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1time_1set(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *arg2 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  {
    if(arg2) {
      strncpy((char*)arg1->time, (const char *)arg2, 24-1);
      arg1->time[24-1] = 0;
    } else {
      arg1->time[0] = 0;
    }
  }
  
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jstring JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_DCLIENT_1OWNER_1MSG_1time_1get(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jstring jresult = 0 ;
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  char *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  result = (char *)(char *) ((arg1)->time);
  if (result) jresult = jenv->NewStringUTF((const char *)result);
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1DCLIENT_1OWNER_1MSG(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  DCLIENT_OWNER_MSG_T *arg1 = (DCLIENT_OWNER_MSG_T *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(DCLIENT_OWNER_MSG_T **)&jarg1; 
  delete arg1;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1IDClient(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  IDClient *arg1 = (IDClient *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(IDClient **)&jarg1; 
  delete arg1;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClient_1Init(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2, jstring jarg3, jlong jarg4, jobject jarg4_) {
  jint jresult = 0 ;
  IDClient *arg1 = (IDClient *) 0 ;
  char *arg2 = (char *) 0 ;
  char *arg3 = (char *) 0 ;
  IDClientDelegate *arg4 = (IDClientDelegate *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  (void)jarg4_;
  arg1 = *(IDClient **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return 0;
  }
  arg3 = 0;
  if (jarg3) {
    arg3 = (char *)jenv->GetStringUTFChars(jarg3, 0);
    if (!arg3) return 0;
  }
  arg4 = *(IDClientDelegate **)&jarg4; 
  result = (int)(arg1)->Init((char const *)arg2,(char const *)arg3,arg4);
  jresult = (jint)result; 
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
  if (arg3) jenv->ReleaseStringUTFChars(jarg3, (const char *)arg3);
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClient_1UnInit(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jint jresult = 0 ;
  IDClient *arg1 = (IDClient *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClient **)&jarg1; 
  result = (int)(arg1)->UnInit();
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClient_1SetDeviceInfo(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jlong jarg2, jobject jarg2_) {
  jint jresult = 0 ;
  IDClient *arg1 = (IDClient *) 0 ;
  DCLIENT_DEVICE_INFO *arg2 = 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  (void)jarg2_;
  arg1 = *(IDClient **)&jarg1; 
  arg2 = *(DCLIENT_DEVICE_INFO **)&jarg2;
  if (!arg2) {
    SWIG_JavaThrowException(jenv, SWIG_JavaNullPointerException, "DCLIENT_DEVICE_INFO const & reference is null");
    return 0;
  } 
  result = (int)(arg1)->SetDeviceInfo((DCLIENT_DEVICE_INFO const &)*arg2);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClient_1Run(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_) {
  jint jresult = 0 ;
  IDClient *arg1 = (IDClient *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClient **)&jarg1; 
  result = (int)(arg1)->Run();
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClient_1RequestOpenDoor(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2, jstring jarg3) {
  jint jresult = 0 ;
  IDClient *arg1 = (IDClient *) 0 ;
  char *arg2 = (char *) 0 ;
  char *arg3 = (char *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClient **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return 0;
  }
  arg3 = 0;
  if (jarg3) {
    arg3 = (char *)jenv->GetStringUTFChars(jarg3, 0);
    if (!arg3) return 0;
  }
  result = (int)(arg1)->RequestOpenDoor((char const *)arg2,(char const *)arg3);
  jresult = (jint)result; 
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
  if (arg3) jenv->ReleaseStringUTFChars(jarg3, (const char *)arg3);
  return jresult;
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_GetIDClientInstance(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  IDClient *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (IDClient *)GetIDClientInstance();
  *(IDClient **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_delete_1IDClientDelegate(JNIEnv *jenv, jclass jcls, jlong jarg1) {
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  
  (void)jenv;
  (void)jcls;
  arg1 = *(IDClientDelegate **)&jarg1; 
  delete arg1;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_10(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4, jlong jarg5, jobject jarg5_, jint jarg6) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  DCLIENT_NOTIFY_HANDLE_FILE_INFO *arg5 = (DCLIENT_NOTIFY_HANDLE_FILE_INFO *) 0 ;
  int arg6 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  (void)jarg5_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  arg5 = *(DCLIENT_NOTIFY_HANDLE_FILE_INFO **)&jarg5; 
  arg6 = (int)jarg6; 
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4,arg5,arg6);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_11(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4, jlong jarg5, jobject jarg5_, jint jarg6) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  DCLIENT_SIP_INFO *arg5 = (DCLIENT_SIP_INFO *) 0 ;
  int arg6 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  (void)jarg5_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  arg5 = *(DCLIENT_SIP_INFO **)&jarg5; 
  arg6 = (int)jarg6; 
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4,arg5,arg6);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_12(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4, jlong jarg5, jobject jarg5_, jint jarg6) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  DCLIENT_OWNER_MSG *arg5 = (DCLIENT_OWNER_MSG *) 0 ;
  int arg6 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  (void)jarg5_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  arg5 = *(DCLIENT_OWNER_MSG **)&jarg5; 
  arg6 = (int)jarg6; 
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4,arg5,arg6);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_13(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4, jstring jarg5) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  char *arg5 = (char *) 0 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  arg5 = 0;
  if (jarg5) {
    arg5 = (char *)jenv->GetStringUTFChars(jarg5, 0);
    if (!arg5) return 0;
  }
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4,(char const *)arg5);
  jresult = (jint)result; 
  if (arg5) jenv->ReleaseStringUTFChars(jarg5, (const char *)arg5);
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_14(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4, jint jarg5) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  int arg5 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  arg5 = (int)jarg5; 
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4,arg5);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT jint JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OnMessage_1_1SWIG_15(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jint jarg2, jlong jarg3, jlong jarg4) {
  jint jresult = 0 ;
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  int result;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (unsigned int)jarg3; 
  arg4 = (unsigned int)jarg4; 
  result = (int)(arg1)->OnMessage(arg2,arg3,arg4);
  jresult = (jint)result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1OuputLog(JNIEnv *jenv, jclass jcls, jlong jarg1, jobject jarg1_, jstring jarg2) {
  IDClientDelegate *arg1 = (IDClientDelegate *) 0 ;
  char *arg2 = (char *) 0 ;
  
  (void)jenv;
  (void)jcls;
  (void)jarg1_;
  arg1 = *(IDClientDelegate **)&jarg1; 
  arg2 = 0;
  if (jarg2) {
    arg2 = (char *)jenv->GetStringUTFChars(jarg2, 0);
    if (!arg2) return ;
  }
  (arg1)->OuputLog((char const *)arg2);
  if (arg2) jenv->ReleaseStringUTFChars(jarg2, (const char *)arg2);
}


SWIGEXPORT jlong JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_new_1IDClientDelegate(JNIEnv *jenv, jclass jcls) {
  jlong jresult = 0 ;
  IDClientDelegate *result = 0 ;
  
  (void)jenv;
  (void)jcls;
  result = (IDClientDelegate *)new SwigDirector_IDClientDelegate(jenv);
  *(IDClientDelegate **)&jresult = result; 
  return jresult;
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1director_1connect(JNIEnv *jenv, jclass jcls, jobject jself, jlong objarg, jboolean jswig_mem_own, jboolean jweak_global) {
  IDClientDelegate *obj = *((IDClientDelegate **)&objarg);
  (void)jcls;
  SwigDirector_IDClientDelegate *director = static_cast<SwigDirector_IDClientDelegate *>(obj);
  director->swig_connect_director(jenv, jself, jenv->GetObjectClass(jself), (jswig_mem_own == JNI_TRUE), (jweak_global == JNI_TRUE));
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_IDClientDelegate_1change_1ownership(JNIEnv *jenv, jclass jcls, jobject jself, jlong objarg, jboolean jtake_or_release) {
  IDClientDelegate *obj = *((IDClientDelegate **)&objarg);
  SwigDirector_IDClientDelegate *director = dynamic_cast<SwigDirector_IDClientDelegate *>(obj);
  (void)jcls;
  if (director) {
    director->swig_java_change_ownership(jenv, jself, jtake_or_release ? true : false);
  }
}


SWIGEXPORT void JNICALL Java_com_akuvox_mobile_libcommon_wrapper_dclient_jni_dclientJNI_swig_1module_1init(JNIEnv *jenv, jclass jcls) {
  int i;
  
  static struct {
    const char *method;
    const char *signature;
  } methods[7] = {
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_0", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJJI)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_1", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJJI)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_2", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJJI)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_3", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJLjava/lang/String;)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_4", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJI)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OnMessage__SWIG_5", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;IJJ)I" 
    },
    {
      "SwigDirector_IDClientDelegate_OuputLog", "(Lcom/akuvox/mobile/libcommon/wrapper/dclient/jni/IDClientDelegate;Ljava/lang/String;)V" 
    }
  };
  Swig::jclass_dclientJNI = (jclass) jenv->NewGlobalRef(jcls);
  if (!Swig::jclass_dclientJNI) return;
  for (i = 0; i < (int) (sizeof(methods)/sizeof(methods[0])); ++i) {
    Swig::director_method_ids[i] = jenv->GetStaticMethodID(jcls, methods[i].method, methods[i].signature);
    if (!Swig::director_method_ids[i]) return;
  }
}


#ifdef __cplusplus
}
#endif

