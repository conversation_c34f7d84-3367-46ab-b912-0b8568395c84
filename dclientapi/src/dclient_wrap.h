/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

#ifndef SWIG_dclient_WRAP_H_
#define SWIG_dclient_WRAP_H_

class SwigDirector_IDClientDelegate : public IDClientDelegate, public Swig::Director {

public:
    void swig_connect_director(JNIEnv *jenv, jobject jself, jclass jcls, bool swig_mem_own, bool weak_global);
    SwigDirector_IDClientDelegate(JNIEnv *jenv);
    virtual ~SwigDirector_IDClientDelegate();
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_NOTIFY_HANDLE_FILE_INFO *pData, int nDataSize);
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_SIP_INFO *pSipInfo, int nDataSize);
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, DCLIENT_OWNER_MSG *pTextMsg, int nDataSize);
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, char const *pData);
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2, int nData);
    virtual int OnMessage(int nMsgID, unsigned int uParam1, unsigned int uParam2);
    virtual void OuputLog(char const *pstrLog);
public:
    bool swig_overrides(int n) {
      return (n < 7 ? swig_override[n] : false);
    }
protected:
    Swig::BoolArray<7> swig_override;
};


#endif
