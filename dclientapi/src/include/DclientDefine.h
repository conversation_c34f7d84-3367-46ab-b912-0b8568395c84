#ifndef __DCLIENT_DEFINE_H__
#define __DCLIENT_DEFINE_H__
#pragma once


/*Timer*/
#define TIMER_ID_BASE		0
#define DCLIENT_TIMER_VAL_BASE		1000

#define TIMER_ID_STOP_PCAP	1
#define TIMER_ID_DELETE_DEVLOG_FILE 2
#define TIMER_ID_DELETE_DEVCONFIG_FILE 3
#define TIMER_ID_DELETE_PCAP_FILE	4

#define TIMER_VAL_SECOND	1


//SOCKET相关
#define SOCKET_MULTICAST_PORT		8500
#define SOCKET_TCP_LISTEN_PORT		8501
#define SOCKET_MULTICAST_ADDR		"238.8.8.1"

//枚举定义
typedef enum
{
	DCLIENT_SUB_SERVER_MODE_NORMAL = 0,
	DCLIENT_SUB_SERVER_MODE_LOCAL,
}DCLIENT_SUB_SERVER_MODE;

typedef enum
{
	DOORSETTING_CONNECT_SERVER_MODE_NONE,
	DOORSETTING_CONNECT_SERVER_MODE_SDMC,
	DOORSETTING_CONNECT_SERVER_MODE_CLOUD,
}DOORSETTING_CONNECT_SERVER_MODE;


#define RL_FREE(A) do {\
	if(A != NULL){\
		free(A);\
		A = NULL;\
	}\
}while(0)

#endif
