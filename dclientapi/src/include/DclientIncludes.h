#ifndef __DCLIENT_INCLUDES_H__
#define __DCLIENT_INCLUDES_H__
#pragma once

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>

#include <rl_revision.h>
#include <rl_msg.h>
#include <rl_log.h>

#include <controller.h>
#include "DclientMsg.h"
#include "revision.h"


#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_LINUX) || (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_ACCESSCONTROL)
#include "rlconfig.h"
#include <rlipc.h>
#else
#include <ipc.h>
#endif
#endif



