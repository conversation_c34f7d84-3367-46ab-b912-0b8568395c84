#ifndef __DCLIENT_MSG_H__
#define __DCLIENT_MSG_H__
#pragma once

#include <rl_revision.h>
#include <rl_msg.h>
#include "SDMCMsgCommon.h"

#define MSG_TYPE_MASK		0xFFFF0000
#define DCLIENT_MSG_TIMER	0x00010000
#define DCLIENT_MSG_MULTICAST		0x00020000
#define DCLIENT_MSG_TCP				0x00030000
#define DCLIENT_MSG_CTRL	0x00040000

enum
{
	MSG_CTRL_DOWNLOAD_PRIVATEKEY_DONE = DCLIENT_MSG_CTRL + 1,
	MSG_CTRL_DOWNLOAD_RFID_DONE,
	MSG_CTRL_DOWNLOAD_ADDRESS_DONE,
	MSG_CTRL_DOWNLOAD_FIRMWARE_DONE,
	MSG_CTRL_DOWNLOAD_AD_DONE,
	MSG_CTRL_DOWNLOAD_CONFIG_DONE,
	MSG_CTRL_SEND_ALARM,
	MSG_CTRL_SEND_MSG,
	MSG_CTRL_SEND_ACCESS_INFO,
	MSG_CTRL_SEND_CHECK_TMP_KEY,
	MSG_CTRL_DOWNLOAD_ADMODULE_DONE,
	MSG_CTRL_DOWNLOAD_COMMUNITY_PHONEBOOK_DONE,
	MSG_CTRL_CREATE_BIND_CODE,
	MSG_CTRL_DELETE_BIND_CODE,
	MSG_CTRL_GET_BINDCODE_LIST,
	MSG_CTRL_SEND_ALARM_DEAL,
	MSG_CTRL_SEND_DISVOCER,	
	MSG_CTRL_DCLIENT_NETWORK_CHANGED,
	MSG_CTRL_UPGRADE_FIRMWARE,	
	MSG_CTRL_REPORT_ARMING,
	MSG_CTRL_PUSH_NOANSWER_FWD_NUMBER,
	MSG_CTRL_MOTION_ALERT,
	MSG_CTRL_REPORT_ACTIVITY,
	MSG_CRTL_REQUEST_ARMING,
	MSG_CTRL_REPORT_ARMING_P2P,
	MSG_CTRL_DOWNLOAD_AKCS_CONTACT_DONE,
	MSG_CTRL_CHECK_DTMF,
	MSG_CTRL_REPORT_DEVICE_CODE,
	MSG_CTRL_PERSONAL_REGISTER,
	MSG_CTRL_PERSONAL_LOGIN,
	MSG_CTRL_ADD_SLAVE_ACCOUNT,
	MSG_CTRL_GET_SLAVE_ACCOUNT_LIST,
	MSG_CTRL_DELETE_SLAVE_ACCOUNT,
	MSG_CTRL_BIND_DEVICE_BY_DEVICECODE,
	MSG_CTRL_EMAIL_EXIST,
	MSG_CTRL_REPORT_NETWORK_INFO,
	MSG_CTRL_AUTO_DISCOVER,
	MSG_CTRL_GET_MOTION_AND_ROBINCALL_INFO,
	MSG_CTRL_SET_PERSONAL_ACCOUNT_MOTION,
	MSG_CTRL_SET_PERSONAL_ACCOUNT_ROBINCALL,
	MSG_CTRL_DOWNLOAD_FACEID_DONE,
	MSG_CTRL_SET_DTMF,
	MSG_CTRL_DOWNLOAD_D_FACEPIC_DONE,
	MSG_CTRL_REQUEST_CONFIG,
	MSG_CTRL_CONTROL4_RESPONSE_COMMON,
	MSG_CTRL_REPORT_CALL_CAPTURE,
	MSG_CTRL_REPORT_TRIGGER,
	MSG_CTRL_MANAGE_BROADCAST_MSG,
	MSG_CTRL_DOWNLOAD_FP_DONE,
	MSG_CTRL_REPORT_HEALTH,
	MSG_CTRL_RESPONSE_SENSOR_TRIGGER,
	MSG_CTRL_UPLOAD_VIDEO_NOTIFY,
	MSG_CTRL_REPORT_ALL_TRIGGER_STATUS,
	MSG_CTRL_REQUEST_ALL_TRIGGER_STATUS,
	MSG_CTRL_SEND_REPORT_DOOR_STATUS,
	MSG_CTRL_SEND_REPORT_GAS,
	MSG_CTRL_SEND_REPORT_VISITOR_INFO,
	MSG_CTRL_SEND_REPORT_VISITOR_AUTH_INFO,
	MSG_CTRL_DOWNLOAD_FACEDATA_DONE,
	MSG_CTRL_REQUEST_OSS_STS,
	MSG_CTRL_REMOTE_CONTROL_OPENDOOR_RESPONSE,
	MSG_CTRL_REQUEST_OPENDOOR,
	MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_DONE,//沈阳嘉里的ID，局域网传输，jpg文件不加密
	MSG_CTRL_MAINTENANCE_ALARM_REPORT,
	MSG_CTRL_DOWNLOAD_TZ_DONE,
	MSG_CTRL_DOWNLOAD_SYCN_FACE_PIC_AES_DONE,//云的人脸同步,公网传输jpg文件进行加密
	MSG_CTRL_DOWNLOAD_ACINFO_DONE,//门禁文件下载
	MSG_CTRL_DOWNLOAD_ACMETA_DONE,//门禁元数据文件下载
	MSG_CTRL_REQUEST_ACINFO,//请求门禁联系人信息
	MSG_CTRL_SEND_DELIVERY_MSG, //快递信息
	MSG_CTRL_UP_PERSONEL_DATA_WAITTING, //通知ACMS正在准备人脸数据
	MSG_CTRL_UP_FINGERPRINT_WAITTING, //通知ACMS正在准备指纹数据
	MSG_CTRL_FILE_HANDLE_MD5_ACK, //通知写MD5
	MSG_CTRL_SYNC_ACTIVITY,//通知批量上传开门记录
	MSG_CTRL_DOWNLOAD_SCHEDULE_DONE,
	MSG_CTRL_SEND_WAKE_WORD,
	MSG_CTRL_REPORT_RELAY_STATUS,
	MSG_CTRL_FLOW_OUT_OF_LIMIT,
	MSG_CTRL_REPORT_CALLLOG,
	MSG_CTRL_BACKUP_CONFIG_ACK,
	MSG_CTRL_DOWNLOAD_BACKUP_CONFIG_RECOVERY_DONE,
	MSG_CTRL_REQUEST_RTSP_MONITOR,
	MSG_CTRL_RTSP_MONITOR_STOP,
	MSG_CTRL_REQUEST_END_USER_REG,
	MSG_CTRL_REPORT_KIT_DEVICES,
	MSG_CTRL_REQUEST_KIT_DEVICES,
	MSG_CTRL_MODIFY_DEVICE_LOCATION,
	MSG_CTRL_ETHERNET_CHANGE,
	MSG_CTRL_UPLOAD_CAPTURE_NOTIFY,
	MSG_CTRL_REBOOT_DCLIENT,
};

typedef enum
{
	TRANSPORT_TYPE_TCP = 0,
	TRANSPORT_TYPE_UDP,
}TRANSPORT_TYPE;

typedef enum
{
	FILE_HANDLE_MD5_ACK_NONE = 0,
	ACCESS_CONTROL_META_ACK,
	ACCESS_CONTROL_INFO_ACK,
	SCHEDULE_INFO_ACK,
	CLEAR_FINGER_DATA_ACK,
	CLEAR_FACE_DATA_ACK,
	RECV_AD_ACK,
	RECV_PRIVATEKEY_ACK,
	RECV_RFKEY_ACK,
	RECV_ADDRESS_ACK,
	RECV_COMMUNITY_ACK,
	DEVICE_LIST_INFO_ACK,
	RECV_FACEID_XML_ACK,
	RECV_DOWNLOAD_FACEPIC_XML_ACK,
	SYNC_FACE_PIC_ACK,
	RECV_FP_TGZ_ACK,	
}FILE_HANDLE_MD5_ACK_TYPE;

#define MAX_UPGRADE_FILE_SIZE			(512*1024*1024)

typedef struct CLOUD_SERVER_INFO_T
{
#define CLOUDSERVER_ADDR_SIZE		64
#define CLOUDSERVER_TOKEN_SIZE		128
#define CLOUDSERVER_MESSAGE_SIZE	128
	INT nResult;
	CHAR szMessage[CLOUDSERVER_MESSAGE_SIZE];
	CHAR szGatewayAddr[CLOUDSERVER_ADDR_SIZE];		/*IP & Port*/
	CHAR szWebSrv[CLOUDSERVER_ADDR_SIZE];	
	CHAR szAccessSrv[CLOUDSERVER_ADDR_SIZE];
	CHAR szVrtspSrv[CLOUDSERVER_ADDR_SIZE];
	CHAR szPBXSrv[CLOUDSERVER_ADDR_SIZE];
	CHAR szFtpSrv[CLOUDSERVER_ADDR_SIZE];
	CHAR szToken[CLOUDSERVER_TOKEN_SIZE];
}CLOUD_SERVER_INFO;

typedef struct DOWNLOAD_SERVER_INFO_T
{
	CHAR szUrl[URL_SIZE];
	CHAR szMD5[MD5_SIZE];
}DOWNLOAD_SERVER_INFO;

#endif



