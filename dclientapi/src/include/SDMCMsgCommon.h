#ifndef __SDMC_MSG_COMMON_H__
#define __SDMC_MSG_COMMON_H__
#pragma once
#include "revision.h"

/*********************************************************************
*
* 这个文件定义的是WINDOWS端和LINUX端共用的宏定义和结构体,开发时一方定义即可
*
*********************************************************************/

#if defined(_WIN32)

#else

typedef char CHAR;
typedef CHAR TCHAR;
typedef unsigned int UINT;

#define _T(str)	str

#endif  // _WIN32

#ifndef IP_SIZE
#define IP_SIZE							16
#endif

#ifndef MAC_SIZE
#define MAC_SIZE						20
#endif

#ifndef VALUE_SIZE
#define VALUE_SIZE						64
#endif

#ifndef INT_SIZE
#define INT_SIZE						12
#endif

#define PROTOCAL_SIZE					16
#define DEVICE_ID_SIZE					24
#define DEVICE_TYPE_SIZE				24
#define DEVICE_STATUS_SIZE				16
#define DEVICE_SWVER_SIZE				16
#define DEVICE_HWVER_SIZE				32
#ifndef DEVICE_FWVER_SIZE
#define DEVICE_FWVER_SIZE				64
#endif
#define DCLIENT_USER_NAME_SIZE			24
#define KEY_TYPE_SIZE                   8
#define KEY_SIZE                        16
#define MSG_ID_SIZE                     8
#define MSG_CRC_SIZE                    8
#define ACK_RESULT_SIZE                 8
#define ACK_INFO_SIZE                   64
#define PASSWORD_SIZE					64

#define STRING_VALUE_SIZE				1024
#define SOCKET_DCLIENT_TYPE_SIZE		64
#define DCLIENT_BUF_MAX_SIZE			1024

#ifndef MD5_SIZE
#define MD5_SIZE						36
#endif

#ifndef URL_SIZE
#define URL_SIZE						256
#endif

#ifndef URL_SIZE_MAX
#define URL_SIZE_MAX					1024
#endif

#ifndef DATETIME_SIZE
#define DATETIME_SIZE					24
#endif

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
#define PROTOCAL_NAME_DEFAULT			_T("2.0")
#else
#define PROTOCAL_NAME_DEFAULT			_T("1.0")
#endif

#define PROTOCAL_SHOWTIME_DEFAULT			_T("1:00-5:00")
#define PROTOCAL_REMOTEIP_DEFAULT			_T("**************")

#define DNS_PARSE_PERIOD_TIMEOUT_DEFAULT	3600

/*收到MULTICAST或TCP消息后通过该结构体转到主线程处理*/
typedef struct SOCKET_MSG_T
{
#define MAX_SOCKET_FRAME_SIZE		4096
#define SOCKET_MSG_MAC_SIZE		6
	TCHAR szRemoteAddr[IP_SIZE];
	UCHAR byRemoteMac[SOCKET_MSG_MAC_SIZE];
	UINT nPort;
	UINT nSocketFd;
	UINT nSize;
	UINT nTransport;
	UCHAR byData[MAX_SOCKET_FRAME_SIZE];
}SOCKET_MSG;

#define SOCKET_MSG_VERSION_OFFSET	11

#define SOCKET_MSG_MAGIC_MSB		0xBC
#define SOCKET_MSG_MAGIC_LSB		0xDE
#define SOCKET_MSG_ID_MASK			0x07FF
#define SOCKET_MSG_VERSION_MASK		0x3800

#if RL_GLOBAL_SUPPORT_AES_ENCRYPT_DECRYPT
#define SOCKET_MSG_VERSION_02		(2<<SOCKET_MSG_VERSION_OFFSET)
#else
#define SOCKET_MSG_VERSION_02		(1<<SOCKET_MSG_VERSION_OFFSET)
#endif


#define MSG_TO_DEVICE_REQUEST_CONNECTION			0x0001
#define MSG_TO_DEVICE_REQUEST_STATUS				0x0002
#define MSG_TO_DEVICE_REQUEST_CONFIG				0x0003
#define MSG_TO_DEVICE_UPDATE_CONFIG					0x0004
#define MSG_TO_DEVICE_FILE_START					0x0005 	//弃用
#define MSG_TO_DEVICE_FILE_DATA						0x0006	//弃用
#define MSG_TO_DEVICE_FILE_END						0x0007	//弃用
#define MSG_TO_DEVICE_UPGRADE_START					0x0008	//弃用
#define MSG_TO_DEVICE_REQUEST_FILE					0x0009	//弃用
#define MSG_TO_DEVICE_REMOTE_CONTROL				0x000A
#define MSG_TO_DEVICE_ACK							0x000B
#define MSG_TO_DEVICE_SEND_DISCOVER					0x000C
#define MSG_TO_DEVICE_ACK_DISCOVER					0x000D
#define MSG_TO_DEVICE_PUSH_AD						0x000E
#define MSG_TO_DEVICE_SEND_OWNER_MESSAGE			0x000F
#define MSG_TO_DEVICE_KEY_SEND						0x0010
#define MSG_TO_DEVICE_UPGRADE_SEND					0x0011
#define MSG_TO_DEVICE_AD_SEND						0x0012
#define MSG_TO_DEVICE_REQUEST_CONFIG_UDP			0x0013
#define MSG_TO_DEVICE_UPDATE_CONFIG_UDP				0x0014
#define MSG_TO_DEVICE_ALARM_SEND					0x0015
#define MSG_TO_DEVICE_SEND_TEXT_MESSAGE				0x0016
#define MSG_TO_DEVICE_UPDATE_HEARTBEAT_PERIOD		0x0017

#define MSG_TO_DEVICE_CHECK_TMP_KEY_ACK				0x0018   //云平台校验临时秘钥的响应
#define MSG_TO_DEVICE_CREATE_BIND_CODE_ACK		    0x0019   //云平台创建绑定码的响应
#define MSG_TO_DEVICE_DELETE_BIND_CODE_ACK		    0x001A   //云平台解除绑定码的响应
#define MSG_TO_DEVICE_BIND_CODE_LIST_ACK		    0x001B   //云平台下发所有绑定列表
#define MSG_TO_DEVICE_NOTIFY_BIND_CODE_CHANGE		0x001C   //云平台下发绑定状态改变的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED			0x001D   //云平台下发alarm告警的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_DEAL		        0x001E   //云平台下发告警已经处理完毕的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_ACK		        0x001F   //云平台响应设备的告警
#define MSG_TO_DEVICE_START_RTSP					0x0023   //云平台下发开始RTSP监控
#define MSG_TO_DEVICE_STOP_RTSP						0x0024   //云平台下发停止RTSP监控

#define MSG_TO_DEVICE_SEND_DEVICE_LIST              0x0025   //云平台下发同一联动单元内的设备列表,用于个人终端用户设备
#define MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE       0x0026   //云平台下发同一联动单元内的设备列表发生变化的通知,用于个人终端用户设备
#define MSG_TO_DEVICE_APP_REPORT_STATUS_ACK         0x0027   //云平台响应app(android/ios)的状态上报消息
#define MSG_TO_DEVICE_APP_LOGOUT_SIP				0x0028   //平台下发设备\app注销sip的信令
#define MSG_TO_DEVICE_REQUEST_ARMING				0x0029	 //平台下发获取ARMING

#define MSG_TO_DEVICE_PUSH_NOANSWER_FWD_NUMBER		0x0030
#define MSG_TO_DEVICE_REPORT_NOANSWER_FWD_NUMBER	0x0031
#define MSG_TO_APP_NOTIFY_MOTION_OCCURED		    0x0032   //平台通知APP有移动侦测事件
#define MSG_TO_DEVICE_REQUEST_ARMING_2				0x0033	 //平台下发获取ARMING,与0x0029等同,因为云平台与SDMC定义冲突所以此处有2个消息
#define MSG_TO_APP_REPORT_ARMING					0x0034   //平台发送给APP关于设备的ARMING状态
#define MSG_TO_DEVICE_KEEP_RTSP						0x0035	 //平台发送给设置保持RTSP连接
#define MSG_TO_DEVICE_CONTACT_URL			        0x0038 //发送获取联系人列表的url
#define MSG_TO_DEVICE_CHECK_DTMF_ACK                0x0039  //返回校验dtmf按键
#define MSG_TO_DEVICE_DEVICE_CODE                   0x003A	 //平台返回设备码让用户绑定或注册主账号
#define MSG_TO_DEVICE_CLEAR_DEVICE_CODE             0x003B	 //清空设备码
#define MSG_TO_DEVICE_HEARBEAT_ACK                  0x003D  // 心跳回复
#define MSG_TO_DEVICE_SERVER_HEARTBEAT				0x003E //服务器发送确认心跳
#define MSG_TO_DEVICE_NOTIFY_VISITOR_AUTH           0x003F  //下发通过访客授权

#define MSG_TO_DEVICE_REQUEST_ARMING_P2P			0x0040	 //DISCOVER下设备与设备的通讯
#define MSG_TO_DEVICE_SCAN							0x0041	 //SDMC 发送scan指令
#define MSG_TO_DEVICE_FACE_DATA_FORWARD             0x0042  //人脸模型转发
#define MSG_TO_DEVICE_SEND_TEMP_KEY            		0x0043  //TempKeyCode下发给X916

#define MSG_TO_DEVICE_DOOR_MOTION_ALERT  			0x0050 //云平台转发motion消息给室内机

#define MSG_TO_DEVICE_MANAGE_ALARM					0x0051 //转发alarm消息给管理机(Mac加密)

#define MSG_TO_DEVICE_COMMON_FORWARD				0x0060

#define MSG_TO_DEVICE_DOWNLOAD_FACE_PIC				0x0070
#define MSG_TO_DEVICE_UPLOAD_FACE_PIC				0x0071
#define MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER		0x0072
#define MSG_TO_DEVICE_SEND_OSS_STS	    			0x0073 //下发相应oss sts令牌给设备
#define MSG_TO_DEVICE_REMOTE_ACCESS_WEB				0x0074 //远程访问设备网页
#define MSG_TO_DEVICE_OPENDOOR_ACK					0x0075
#define MSG_TO_DEVICE_REQUEST_PERSONEL_DATA			0x0076	//通知门禁设备上传人脸用户信息
#define MSG_TO_DEVICE_SYNC_PERSONEL_DATA			0x0077	//通知门禁设备同步人脸用户信息
#define MSG_TO_DEVICE_REQUEST_FINGER_PRINT			0x0078	//通知R29上传指纹信息
#define MSG_TO_DEVICE_SYNC_FINGER_PRINT				0x0079	//通知R29同步指纹信息
#define MSG_TO_DEVICE_NOTIFY_ATTENDANCE_SERVICE		0x0080	//通知设备指定哪台是考勤服务器
#define MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY		0x0081	//通知设备relay常开
#define MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY		0x0082	//通知设备relay常关
#define MSG_TO_APP_CHANGE_RELAY_STATUS				0x0083	//通知App改变Relay的状态
#define MSG_TO_DEVICE_BACKUP_CONFIG					0x0084	//通知设备备份配置文件
#define MSG_TO_DEVICE_BACKUP_CONFIG_RECOVERY		0x0085	//通知设备恢复备份的配置
#define MSG_TO_DEVICE_REG_END_USER					0x0086	//通过二维码让设备补充EndUser信息
#define MSG_TO_DEVICE_REQUEST_IS_KIT				0x0087	//下发给设备是否kit方案
#define MSG_TO_DEVICE_REPORT_KIT_DEVICES  			0x0088	//云需要把当前家庭下的设备信息告知室内机


#define MSG_TO_DEVICE_MAINTENANCE_GETLOG			0x201
#define MSG_TO_DEVICE_MAINTENANCE_START_PCAP 		0x202
#define MSG_TO_DEVICE_MAINTENANCE_STOP_PCAP			0x203
#define MSG_TO_DEVICE_MAINTENANCE_GET_DEV_CONFIG  	0x205
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS		0x206
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY	0x207
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESS_SERVER	0x208
#define MSG_TO_DEVICE_CLI_COMMAND	 				0x0209  //控制终端消息信令
#define MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE 	0x020A  //更新服务器地址

#define MSG_TO_DEVICE_REGISTER_FACE					0x0210  //注册人脸信息
#define MSG_TO_DEVICE_MODIFY_FACE					0x0211  //修改人脸信息
#define MSG_TO_DEVICE_DELETE_FACE					0x0212  //删除人脸信息
#define MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN			0x0213  //登录gsface获取人脸列表信息

#define MSG_FROM_DEVICE_CHECK_KEY					0x0100
#define MSG_FROM_DEVICE_ACK							0x0101
#define MSG_FROM_DEVICE_REPORT_STATUS				0x0102
#define MSG_FROM_DEVICE_REPORT_CONFIG				0x0103
#define MSG_FROM_DEVICE_BOOTUP						0x0104
#define MSG_FROM_DEVICE_FILE_START					0x0105
#define MSG_FROM_DEVICE_FILE_DATA					0x0106
#define MSG_FROM_DEVICE_FILE_END					0x0107
#define MSG_FROM_DEVICE_ALARM						0x0108
#define MSG_FROM_DEVICE_TEXT_MSG					0x0109
#define MSG_FROM_DEVICE_ACCESS_INFO					0x010A
#define MSG_FROM_DEVICE_HEART_BEAT					0x010B
#define MSG_FROM_DEVICE_REPORT_TRIGGER				0x010C

#define MSG_FROM_DEVICE_CHECK_TMP_KEY		        0x010F  //梯口机校验临时秘钥
#define MSG_FROM_DEVICE_CREATE_BIND_CODE		    0x0110  //室内机请求生成绑定码
#define MSG_FROM_DEVICE_DELETE_BIND_CODE		    0x0111  //室内机请求解绑绑定码
#define MSG_FROM_DEVICE_GET_BIND_CODE_LIST		    0x0112  //室内机请求所有绑定列表
#define MSG_FROM_DEVICE_POST_BIND_CODE     	        0x0113  //APP主动向平台推送绑定码
#define MSG_FROM_DEVICE_PUT_ALARM_DEAL              0x0114  //设备或者app向平台推送告警处理的消息

#define MSG_FROM_ANDROID_REPORT_STATUS				0x0116  //安卓向平台上报状态
#define MSG_FROM_IOS_REPORT_STATUS				    0x0117  //IOS向平台上报状态
#define MSG_FROM_DEVICE_REQUEST_DEVICE_LIST	        0x0118  //设备平台请求统一联动单元内的设备列表,用于个人终端用户

#define MSG_FROM_DEVICE_SEND_DISCOVER				0x0120 //在没有云平台和SDMC的情况下走discover进行探测
#define MSG_FROM_DEVICE_ACK_DISCOVER				0x0121 
#define MSG_FROM_DEVICE_REPORT_ARMING				0x0122	//设备上报ARMING状态给平台
#define MSG_FROM_DEVICE_MOTION_ALERT	            0x0123  //设备上报移动侦测消息给平台
#define MSG_FROM_DEVICE_REPORT_ACTIVITY				0x0124  //设备上报动作时间给平台
#define MSG_FROM_APP_REQUEST_CAPTURE				0x0125  //APP发送截屏命令给平台
#define MSG_FROM_APP_SET_MOTION_ALERT				0x0126  //APP发送开始/关闭移动侦测的命令给平台
#define MSG_FROM_APP_REQUEST_ARMING					0x0127  //APP发送布防撤防的命令给平台
#define MSG_FROM_DEVICE_CHECK_DTMF                  0x0129  //校验dtmf按键
#define MSG_FROM_DEVICE_CLI_COMMAND_RESP  			0x012B  //cli 返回
#define MSG_FROM_DEVICE_REPORT_CALL_CAPTURE         0x012C  //通话截图消息
#define MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG        0x012D  //管理机广播消息(Mac加密)
#define MSG_FROM_DEVICE_ACK_HEARTBEAT				0x012E //设备回复服务器心跳

#define MSG_FROM_DEVICE_REPORT_DEVICE_CODE			0x0130	//设备上报device Code
#define MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER		0x0131

#define MSG_FROM_DEVICE_REPORT_DOOR_STATUS			0x0140   //DOORPHONE REPORT DOOR_STATUS
#define MSG_FROM_DEVICE_REPORT_GAS					0x0141   //INDOOR MONITOR REPORT GAS
#define MSG_FROM_DEVICE_REPORT_HEALTH				0x0142   //REPORT HEALTH STATUS

#define MSG_FROM_DEVICE_COMMON_FORWARD				0x0160	//设备上报通用
#define MSG_FROM_DEVICE_UPLOAD_VIDEO_NOTIFY			0x0161	//设备上传视频
#define MSG_FROM_DEVICE_REPORT_VISITOR_INFO			0x0162  //设备上报访客信息
#define MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG 0x0163  //上报访客授权信息
#define MSG_FROM_DEVICE_REQUEST_OSS_STS	  			0x0164	//设备请求oss sts令牌
#define MSG_FROM_DEVICE_AKCS_ACK					0x0165  //设备对接云的ACK
#define MSG_FROM_DEVICE_REQUEST_OPENDOOR 			0x0166 //室内机请求开门
#define MSG_FROM_DEVICE_REQUEST_ACINFO				0x0167 //请求获取门禁联系人
#define MSG_FROM_DEVICE_SEND_DELIVERY_MSG			0x0168 //快递消息
#define MSG_FROM_DEVICE_SYNC_ACTIVITY				0x0169 //批量上传开门记录
#define MSG_FROM_DEVICE_REPORT_RELAY_STATUS			0x0170	//设备上报relay状态
#define MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT 			0x0171	//新增额度、百分比 参数；代表总共40G 超过80% 告警
#define MSG_FROM_DEVICE_REPORT_CALLLOG				0x0176	//上报通话记录，主叫上传
#define MSG_FROM_DEVICE_BACKUP_CONFIG_ACK			0x0177	//设备备份配置后，发消息上报
#define MSG_FROM_DEVICE_REQUEST_RTSP_MONITOR		0x0179	//通知RTSP监控
#define MSG_FROM_DEVICE_RTSP_MONITOR_STOP			0x0180	//通知停止RTSP监控
#define MSG_FROM_DEVICE_REQUEST_END_USER_REG		0x0181	//设备请求注册二维码
#define MSG_FROM_DEVICE_REPORT_KIT_DEVICES			0x0182	//KIT室内机将设备的固件号和MAC地址传输给云
#define MSG_FROM_DEVICE_ADD_KIT_DEVICES				0x0183	//KIT手动添加设备上报给云
#define MSG_FROM_DEVICE_REQUEST_KIT_DEVICES  		0x0184	//设备请求当前家庭下的设备信息
#define MSG_FROM_DEVICE_MODIFY_LOCATION  			0x0185	//室内机可修改设备的Location信息
#define MSG_FROM_DEVICE_UPLOAD_CAPTURE_NOTIFY		0x018A	//设备图片上传

#define MSG_DEVICE_TO_DEVICE_REPORT_NETWORK_INFO	0x0300	//设备上报网络信息给设备
#define MSG_DEVICE_TO_DEVICE_REQUEST_CONFIG			0x0301	//设备请求设备上报配置
#define MSG_DEVICE_TO_DEVICE_REQUEST_ALL_TRIGGER	0x0302	// 请求触发状态
#define MSG_DEVICE_TO_DEVICE_REPORT_ALL_TRIGGER		0x0303	//上报触发状态

#define MSG_FROM_APP_REQUEST_CHANGE_RELAY 			0x0330	//请求改变Relay状态


#define MSG_CONTROL4_REMOTE_ACTION					0x0401  //Control 4模块控制设备的id
#define MSG_CONTROL4_SEND_CONTACT_INFO				0x0402  //Control 4发送联系人信息
#define MSG_CONTROL4_REMOTE_ACTION_ACK				0x0410

#define SOCKET_MSG_TYPE_NAME_BOOTUP				"Bootup"
#define SOCKET_MSG_TYPE_NAME_REQ_CONN			"RequestConnection"
#define SOCKET_MSG_TYPE_NAME_REQ_STATUS			"RequestStatus"
#define SOCKET_MSG_TYPE_NAME_REPORT_STATUS		"ReportStatus"
#define SOCKET_MSG_TYPE_NAME_REBOOT				"Reboot"
#define SOCKET_MSG_TYPE_NAME_OPENDOOR			"OpenDoor"
#define SOCKET_MSG_TYPE_NAME_RESET_TO_FACTORY	"ResetToFactory"
#define SOCKET_MSG_TYPE_NAME_REQ_CONFIG			"RequestConfig"
#define SOCKET_MSG_TYPE_NAME_REPORT_CONFIG		"ReportConfig"
#define SOCKET_MSG_TYPE_NAME_UPDATE_CONFIG		"UpdateConfig"
#define SOCKET_MSG_TYPE_NAME_UPGRADE_START		"UpgradeStart"
#define SOCKET_MSG_TYPE_NAME_FILE_END			"FileEnd"
#define SOCKET_MSG_TYPE_NAME_CHECK_KEY			"CheckKey"
#define SOCKET_MSG_TYPE_NAME_ACK				"Ack"
#define SOCKET_MSG_TYPE_NAME_MAKECALL			"MakeCall"
#define SOCKET_MSG_TYPE_NAME_HANGUP				"HangUp"
#define SOCKET_MSG_TYPE_NAME_PUSH_AD			"PushAd"
#define SOCKET_MSG_TYPE_NAME_ALARM				"Alarm"
#define SOCKET_MSG_TYPE_NAME_TEXT_MESSAGE		"TextMessage"
#define SOCKET_MSG_TYPE_NAME_ACCESS_INFO		"AccessInfo"
#define SOCKET_MSG_TYPE_NAME_CHECK_TMP_KEY		"CheckTmpKey"
#define SOCKET_MSG_TYPE_NAME_GET_BIND_CODE		"GetBindCode"
#define SOCKET_MSG_TYPE_NAME_UN_BIND_CODE		"UnBindCode"
#define SOCKET_MSG_TYPE_NAME_GET_BIND_LIST		"GetBindList"
#define SOCKET_MSG_TYPE_NAME_DEAL_ALARM			"DealAlarm"
#define SOCKET_MSG_TYPE_NAME_DISCOVER			"Discover"
#define SOCKET_MSG_TYPE_NAME_DISCOVER_ACK		"DiscoverAck"
#define SOCKET_MSG_TYPE_NAME_REQ_DEVICE_LIST	"RequestDevList"
#define SOCKET_MSG_TYPE_NAME_REPORT_ARMING		"ReportArming"
#define SOCKET_MSG_TYPE_NAME_PUSH_NOANSWER_FORWARD_NUMBER	"PushNoAnswerFwdNumber"
#define SOCKET_MSG_TYPE_NAME_REPORT_NOANSWER_FORWARD_NUMBER  "ReportNoAnswerFwdNumber"
#define SOCKET_MSG_TYPE_NAME_MOTION_ALERT		"MotionAlert"
#define SOCKET_MSG_TYPE_NAME_REPORT_ACTIVITY	"ReportActivity"
#define SOCKET_MSG_TYPE_NAME_REQUEST_ARMING		"RequestArming"
#define SOCKET_MSG_TYPE_NAME_SEND_DTMF_SET		"SendDtmfSet"
#define SOCKET_MSG_TYPE_NAME_REPORT_TRIGGER		"ReportTrigger"
#define SOCKET_MSG_TYPE_NAME_BROADCAST_MSG		"BroadCastMsg"
#define SOCKET_MSG_TYPE_NAME_REPORT_HEALTH		"ReportHealth"
#define SOCKET_MSG_TYPE_NAME_RESPONSE_SENSOR_TRIGGER	"ResponseSensorTrigger"
#define SOCKET_MSG_TYPE_NAME_UPLOAD_VIDEO_NOTIFY	"UploadVideoNotify"
#define SOCKET_MSG_TYPE_NAME_REQUEST_ALL_TRIGGER	"RequestAllTrigger"
#define SOCKET_MSG_TYPE_NAME_REPORT_ALL_TRIGGER		"ReportAllTrigger"
#define SOCKET_MSG_TYPE_NAME_REPORT_DOORSTATUS		"ReportDoorStatus"
#define SOCKET_MSG_TYPE_NAME_REPORT_GAS			"ReportGAS"
#define SOCKET_MSG_TYPE_NAME_REPORT_VISITOR_INFO	"ReportVisitorInfo"
#define SOCKET_MSG_TYPE_NAME_REPORT_VISITOR_AUTH	"ReportVisitorAuth"

typedef struct SOCKET_MSG_NORMAL_T
{
#define SOCKET_MSG_MAGIC_SIZE			2
#define SOCKET_MSG_DATA_SIZE			4096
#define SOCKET_MSG_NORMAL_HEADER_SIZE	10

	UCHAR byMagic[SOCKET_MSG_MAGIC_SIZE];
	USHORT nCrc;
	USHORT nMsgID;
	USHORT nHeadSize;
	USHORT nDataSize;
	UCHAR byData[SOCKET_MSG_DATA_SIZE-SOCKET_MSG_NORMAL_HEADER_SIZE];
}SOCKET_MSG_NORMAL;

typedef struct SOCKET_MSG_FILEDATA_T
{
#define SOCKET_MSG_FILEDATA_HEADER_SIZE		16
	UCHAR byMagic[SOCKET_MSG_MAGIC_SIZE];
	USHORT nCrc;
	USHORT nMsgID;
	USHORT nHeadSize;
	USHORT nDataSize;
	USHORT nReserved;
	UINT nDataOffset;
	UCHAR byData[SOCKET_MSG_DATA_SIZE-SOCKET_MSG_FILEDATA_HEADER_SIZE];
}SOCKET_MSG_FILEDATA;

typedef struct SOCKET_MSG_BOOTUP_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szIPAddr[IP_SIZE];
	UINT nPort;
}SOCKET_MSG_BOOTUP;

typedef struct SOCKET_MSG_REQ_STATUS_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szSrvName[VALUE_SIZE];
}SOCKET_MSG_REQ_STATUS;

typedef struct SOCKET_MSG_REQ_CONN_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szIPAddr[IP_SIZE];
	UINT nPort;
	UINT nFactoryMode;
	BOOL bForceConnect;
	UINT nHeartBeatPeriod;
}SOCKET_MSG_REQ_CONN;

typedef struct SOCKET_MSG_REPORT_STATUS_T
{
#define AUTH_CODE_SIZE		32
	UINT nDclientVer;
	UINT nArming;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szDeviceID[DEVICE_ID_SIZE];
	TCHAR szExtension[INT_SIZE];
	TCHAR szDownloadServer[VALUE_SIZE];
	TCHAR szUploadServer[VALUE_SIZE];
	TCHAR szType[INT_SIZE];
	TCHAR szIPAddr[IP_SIZE];
	TCHAR szSubnetMask[IP_SIZE];
	TCHAR szGateway[IP_SIZE];
	TCHAR szPrimaryDNS[IP_SIZE];
	TCHAR szSecondaryDNS[IP_SIZE];
	TCHAR szMAC[MAC_SIZE];
	TCHAR szStatus[DEVICE_STATUS_SIZE];
	TCHAR szSWVer[DEVICE_SWVER_SIZE];
	TCHAR szHWVer[DEVICE_HWVER_SIZE];
	TCHAR szPrivatekeyMD5[MD5_SIZE];
	TCHAR szRfidMD5[MD5_SIZE];
	TCHAR szAddressMD5[MD5_SIZE];
	TCHAR szConfigMD5[MD5_SIZE];
	TCHAR szAdModuleMD5[MD5_SIZE];
	TCHAR szContactMD5[MD5_SIZE];
	TCHAR szCommunityPhonebookMD5[MD5_SIZE];
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC	
	TCHAR szFaceidMD5[MD5_SIZE];
#endif
#if RL_SUPPORT_AUTH_CODE
	TCHAR szAuthCode[AUTH_CODE_SIZE];
#endif
	TCHAR szFPMD5[MD5_SIZE];
	TCHAR szTzMD5[MD5_SIZE];
	TCHAR szTzDataMD5[MD5_SIZE];
	TCHAR szFaceSyncMD5[MD5_SIZE];
	TCHAR szACMetaMD5[MD5_SIZE];
	TCHAR szACInfoMD5[MD5_SIZE];
	TCHAR szScheduleMD5[MD5_SIZE];
	TCHAR szRelayStatus[VALUE_SIZE];
	TCHAR szModelName[VALUE_SIZE];
}SOCKET_MSG_REPORT_STATUS;

typedef struct SOCKET_MSG_OWNER_MESSAGE_T
{
#define OWNERMSG_TYPE_SIZE	24
#define OWNERMSG_CONTENT_SIZE	512
#define OWNERMSG_USER_SIZE	24
#define OWNERMSG_TITLE_SIZE	48
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szType[OWNERMSG_TYPE_SIZE];
	TCHAR szTitle[OWNERMSG_TITLE_SIZE];
	TCHAR szContent[OWNERMSG_CONTENT_SIZE];
	TCHAR szTime[DATETIME_SIZE];
	TCHAR szUser[OWNERMSG_USER_SIZE];
}SOCKET_MSG_OWNER_MESSAGE;

typedef struct SOCKET_MSG_CHECK_KEY_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szDeviceID[DEVICE_ID_SIZE];
	TCHAR szKeyType[KEY_TYPE_SIZE];
	TCHAR szKeyValue[KEY_SIZE];
}SOCKET_MSG_CHECK_KEY;

typedef struct SOCKET_MSG_ACK_T
{
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szMsgID[MSG_ID_SIZE];
	TCHAR szMsgCRC[MSG_CRC_SIZE];
	TCHAR szResult[ACK_RESULT_SIZE];
	TCHAR szInfo[ACK_INFO_SIZE];
}SOCKET_MSG_ACK;

typedef struct SOCKET_MSG_AKCS_ACK_T
{	
#define SOCKET_MSG_AKCS_ACK_TYPE_SIZE	32
#define SOCKET_MSG_AKCS_ACK_TRACEID_SIZE	32
	INT nMsgID;
	TCHAR szType[SOCKET_MSG_AKCS_ACK_TYPE_SIZE];
	TCHAR szTraceID[SOCKET_MSG_AKCS_ACK_TRACEID_SIZE];
	TCHAR szResult[ACK_RESULT_SIZE];
	TCHAR szInfo[ACK_INFO_SIZE];
}SOCKET_MSG_AKCS_ACK;

typedef struct SOCKET_MSG_REMOTE_CONTROL_T
{
#define REMOTE_CONTROL_ITEM_NUM			8
#define REMOTE_CONTROL_ITEM_SIZE		32
#define REMOTE_CONTROL_TYPE_SIZE		32
#define REMOTE_CONTROL_ACCOUNT_SIZE		16
#define REMOTE_CONTROL_MODE_SIZE		8

	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szType[REMOTE_CONTROL_TYPE_SIZE];
	TCHAR szItem[REMOTE_CONTROL_ITEM_NUM][REMOTE_CONTROL_ITEM_SIZE];
	TCHAR szFrom[REMOTE_CONTROL_ACCOUNT_SIZE];
	TCHAR szTo[REMOTE_CONTROL_ACCOUNT_SIZE];
	TCHAR szMode[REMOTE_CONTROL_MODE_SIZE];
}SOCKET_MSG_REMOTE_CONTROL;

typedef struct CONFIG_MODULE_T
{
#define CONFIG_MODULE_ITEM_NUM			32
#define CONFIG_MODULE_ITEM_SIZE			96
	TCHAR szItem[CONFIG_MODULE_ITEM_NUM][CONFIG_MODULE_ITEM_SIZE];
}CONFIG_MODULE;

typedef struct CONFIG_MODULE_FROM_DEVICE_T
{
#define CONFIG_MODULE_FROM_DEVICE_ITEM_NUM			10
#define CONFIG_MODULE_FROM_DEVICE_ITEM_SIZE			256
	TCHAR szItem[CONFIG_MODULE_FROM_DEVICE_ITEM_NUM][CONFIG_MODULE_FROM_DEVICE_ITEM_SIZE];
}CONFIG_MODULE_FROM_DEVICE;

typedef struct SOCKET_MSG_CONFIG_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	CONFIG_MODULE module;
}SOCKET_MSG_CONFIG;

typedef struct SOCKET_MSG_CONFIG_FROM_DEVICE_T
{
	CHAR szFromIP[IP_SIZE];
	CHAR szToIP[IP_SIZE];
	CONFIG_MODULE_FROM_DEVICE module;
}SOCKET_MSG_CONFIG_FROM_DEVICE;


typedef struct SOCKET_MSG_UPGRADE_START_T
{
#define FILE_PATH_SIZE					128
#define FILE_MD5_SIZE					32
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFilePath[FILE_PATH_SIZE];
	TCHAR szFileMd5[FILE_MD5_SIZE];
	UINT nFileSize;
}SOCKET_MSG_UPGRADE_START;

typedef struct AD_MSG_ITEM_T
{
#define AD_MSG_TYPE_SIZE	32
	UINT nDuration;
	UINT nCount;
	TCHAR szType[AD_MSG_TYPE_SIZE];
	TCHAR szUrl[URL_SIZE];
}AD_MSG_ITEM;

typedef struct SOCKET_MSG_ADSEND_MESSAGE_T
{
#define AD_ITEM_MAX		8
	int nCount;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szTime[DATETIME_SIZE];
	AD_MSG_ITEM items[AD_ITEM_MAX];
}SOCKET_MSG_AD_SEND;


typedef struct SOCKET_MSG_FILE_END_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFileMd5[FILE_MD5_SIZE];
	UINT nFileSize;
}SOCKET_MSG_FILE_END;

typedef struct SOCKET_MSG_KEY_SEND_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szPrivatekeyUrl[URL_SIZE];
	TCHAR szPrivatekeyMD5[MD5_SIZE];
	TCHAR szRfidUrl[URL_SIZE];
	TCHAR szRfidMD5[MD5_SIZE];
	TCHAR szConfigUrl[URL_SIZE];
	TCHAR szConfigMD5[MD5_SIZE];
	TCHAR szAddrUrl[URL_SIZE];
	TCHAR szAddrMD5[MD5_SIZE];
	TCHAR szAdModuleUrl[URL_SIZE];
	TCHAR szAdModuleMD5[MD5_SIZE];
	TCHAR szCommunityPhonebookUrl[URL_SIZE];
	TCHAR szCommunityPhonebookMD5[MD5_SIZE];
	TCHAR szContactMD5[MD5_SIZE];
	TCHAR szContactUrl[URL_SIZE];
#if RL_SUPPORT_DOWNUPLOAD_FACE_PIC	
	TCHAR szFaceIDUrl[URL_SIZE];
	TCHAR szFaceIDMD5[MD5_SIZE];
#endif
	TCHAR szFPUrl[URL_SIZE];
	TCHAR szFPMD5[MD5_SIZE];
	TCHAR szTzUrl[URL_SIZE];//for 宓屽叆寮?
	TCHAR szTzMD5[MD5_SIZE];
	TCHAR szTzDataUrl[URL_SIZE];//for android
	TCHAR szTzDataMD5[MD5_SIZE];
	TCHAR szFaceSyncUrl[URL_SIZE];
	TCHAR szFaceSyncMD5[MD5_SIZE];
	TCHAR szACInfoUrl[URL_SIZE];
	TCHAR szACInfoMD5[MD5_SIZE];
	TCHAR szACMetaUrl[URL_SIZE];
	TCHAR szACMetaMD5[MD5_SIZE];
	TCHAR szScheduleUrl[URL_SIZE];
	TCHAR szScheduleMD5[MD5_SIZE];
}SOCKET_MSG_KEY_SEND;

typedef struct SOCKET_MSG_UPGRADE_SEND_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFirmwareVer[DEVICE_SWVER_SIZE];
	TCHAR szFirmwareUrl[URL_SIZE_MAX];
}SOCKET_MSG_UPGRADE_SEND;

typedef struct SOCKET_MSG_ALARM_T
{
#define ALARM_TYPE_SIZE			256
	UINT nSequenceNum;
	UINT id;
	UINT nAlarmCode;
	UINT unAlarmLocation;
	UINT unAlarmZone;
	UINT unAlarmCustomize;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szType[ALARM_TYPE_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szFromName[DCLIENT_USER_NAME_SIZE];
	TCHAR szToName[DCLIENT_USER_NAME_SIZE];
	TCHAR szTime[DATETIME_SIZE];
}SOCKET_MSG_ALARM;

typedef struct SOCKET_MSG_ALARM_SEND_T
{
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szType[ALARM_TYPE_SIZE];
	TCHAR szAddress[DEVICE_ID_SIZE];
	CHAR szTime[DATETIME_SIZE];
	CHAR szMAC[MAC_SIZE];
	UINT nExtension;
	UINT nDeviceType;
	UINT id;
	UINT nDuration;
	UINT nAlarmCode;
	UINT unAlarmLocation;
	UINT unAlarmZone;
	UINT unAlarmCustomize;
}SOCKET_MSG_ALARM_SEND;

typedef struct SOCKET_MSG_TEXT_MESSAGE_T
{
#define TEXTMSG_TYPE_SIZE		24
#define TEXTMSG_CONTENT_SIZE	1200
#define TEXTMSG_USER_SIZE		24
#define TEXTMSG_TITLE_SIZE		360
	UINT nSequenceNum;
	UINT nMsgID;
	UINT nMultiFlag;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szType[TEXTMSG_TYPE_SIZE];
	TCHAR szTitle[TEXTMSG_TITLE_SIZE];
	TCHAR szContent[TEXTMSG_CONTENT_SIZE];
	TCHAR szTime[DATETIME_SIZE];
	TCHAR szFrom[TEXTMSG_USER_SIZE];
	TCHAR szTo[TEXTMSG_USER_SIZE];
	TCHAR szFromName[TEXTMSG_USER_SIZE];
	TCHAR szToName[TEXTMSG_USER_SIZE];
}SOCKET_MSG_TEXT_MESSAGE;

typedef struct SOCKET_MSG_ACCESS_INFO_T
{
#define ACCESS_INFO_CODE_SIZE		24
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szCode[ACCESS_INFO_CODE_SIZE];
}SOCKET_MSG_ACCESS_INFO;

typedef struct SOCKET_MSG_HEARTBEAT_PERIOD_T
{
#define HEARTBEAT_PERIOD_SIZE		24
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szExpire[HEARTBEAT_PERIOD_SIZE];
}SOCKET_MSG_HEARTBEAT_PERIOD;


#if RL_GLOBAL_SUPPORT_TMP_KEY
typedef struct SOCKET_MSG_CHECK_TMP_KEY_T
{
#define CHECK_TMP_KEY_CODE_SIZE		24
#define MSG_SEQ_CODE_SIZE			24
#define DCLIENT_CHECK_TMPKEY_RELAY_SIZE 24
#define SOCKET_TMP_KEY_COMMON_VALUE_SIZE	24
#define DCLIENT_PER_ID_SIZE			32
	TCHAR szProtocal[PROTOCAL_SIZE];
	UINT nResult; 
	TCHAR szRelay[DCLIENT_CHECK_TMPKEY_RELAY_SIZE];
	TCHAR szSecurityRelay[DCLIENT_CHECK_TMPKEY_RELAY_SIZE];
	TCHAR szCheckTmpKeyCode[CHECK_TMP_KEY_CODE_SIZE];
	TCHAR szMsgSeqCode[MSG_SEQ_CODE_SIZE];
	TCHAR szUnitAPT[SOCKET_TMP_KEY_COMMON_VALUE_SIZE];
	TCHAR szPerID[DCLIENT_PER_ID_SIZE];
}SOCKET_MSG_CHECK_TMP_KEY;
#endif

#if RL_GLOBAL_SUPPORT_VRTSP
typedef struct SOCKET_MSG_START_RTSP_T
{
	UINT nExpire;
	UINT nSSRC;
	TCHAR szProtocal[PROTOCAL_SIZE];
	UINT nRemotePort;
	TCHAR szRemoteIP[IP_SIZE];
}SOCKET_MSG_START_RTSP;

typedef struct SOCKET_MSG_STOP_RTSP_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	UINT nRemotePort;
	TCHAR szRemoteIP[IP_SIZE];
}SOCKET_MSG_STOP_RTSP;

typedef struct SOCKET_MSG_KEEP_RTSP_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	UINT nExpire;
}SOCKET_MSG_KEEP_RTSP;
#endif

typedef struct SOCKET_MSG_ALARM_DEAL_T
{
#define ALARM_DEAL_USER_SIZE	24
#define ALARM_DEAL_RESULT_SIZE	512
#define ALARM_DEAL_ID_SIZE		16
	UINT id;
	UINT nAlarmCode;
	UINT unAlarmLocation;
	UINT unAlarmZone;
	UINT unAlarmCustomize;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szUser[ALARM_DEAL_USER_SIZE];
	TCHAR szResult[ALARM_DEAL_RESULT_SIZE];
	TCHAR szType[ALARM_TYPE_SIZE];
	TCHAR szTime[DATETIME_SIZE];
}SOCKET_MSG_ALARM_DEAL;

typedef struct SOCKET_DLCIENT_BINDCODE_INFO_T
{
#define DCLIENT_DEVICE_CODE_SIZE	20	
#define DCLIENT_BIND_CODE_SIZE					20
	INT nStatus;
	TCHAR szBindCode[DCLIENT_BIND_CODE_SIZE];
	TCHAR szTime[DATETIME_SIZE];
	TCHAR szDeviceCode[DCLIENT_DEVICE_CODE_SIZE];
}SOCKET_DLCIENT_BINDCODE_INFO;

typedef struct SOCKET_MSG_BIND_CODE_LIST_T
{	
#define BIND_CODE_LIST_COUNT_MAX		10
	TCHAR szProtocal[PROTOCAL_SIZE];
	UINT nResult;
	UINT nBindCodeCount;
	UINT nSequenceNum;
	SOCKET_DLCIENT_BINDCODE_INFO bindCodeInfo[BIND_CODE_LIST_COUNT_MAX];
}SOCKET_MSG_BIND_CODE_LIST;

typedef struct SOCKET_MSG_BIND_CODE_CREATE_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	INT nResult;
	UINT nSequenceNum;
	CHAR szBindCode[DCLIENT_BIND_CODE_SIZE];
}SOCKET_MSG_BIND_CODE_CREATE;

typedef struct SOCKET_MSG_DISCOVER_SEND_T
{		
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];	
	TCHAR szType[INT_SIZE];
	TCHAR szDeviceID[DEVICE_ID_SIZE];
	TCHAR szExtension[INT_SIZE];
	TCHAR szFlag[INT_SIZE];
	TCHAR szDiscoverMethod[INT_SIZE];
}SOCKET_MSG_DISCOVER_SEND;

typedef struct SOCKET_MSG_DISCOVER_ACK_T
{	
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];	
	TCHAR szType[INT_SIZE];
	TCHAR szDeviceID[DEVICE_ID_SIZE];
	TCHAR szExtension[INT_SIZE];
	TCHAR szIPAddr[IP_SIZE];
	TCHAR szSWVer[DEVICE_SWVER_SIZE];
	TCHAR szDeviceName[VALUE_SIZE];
	TCHAR szLocation[VALUE_SIZE];
	TCHAR szRTSP[URL_SIZE];
	TCHAR szFlag[INT_SIZE];
	TCHAR szDiscoverMethod[INT_SIZE];
	TCHAR szDeviceCode[INT_SIZE];
	TCHAR szMac[MAC_SIZE];
	TCHAR szModel[VALUE_SIZE];
}SOCKET_MSG_DISCOVER_ACK;

typedef struct SOCKET_MSG_REQUEST_DEVICE_LIST_T
{	
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];	
}SOCKET_MSG_REQUEST_DEVICE_LIST;

typedef struct SOCKET_MSG_REQUEST_ARMING_T
{
#define ARMING_ACTION_SIZE		12
	UINT nSequenceNum;
	UINT nMode;
	TCHAR szAction[ARMING_ACTION_SIZE];
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szFromIP[DCLIENT_USER_NAME_SIZE];
	TCHAR szToIP[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_REQUEST_ARMING;

typedef struct SOCKET_MSG_REPORT_ARMING_T
{
	UINT nSequenceNum;
	UINT nMode;
	UINT nSync;
	UINT nActionType;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szFromIP[DCLIENT_USER_NAME_SIZE];
	TCHAR szToIP[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_REPORT_ARMING;

typedef struct SOCKET_MSG_PUSH_FORWARD_NUMBER_T
{
#define FORWARD_NUMBER_ACTION_SIZE		12
#define FORWARD_NUMBER_GROUP_SIZE		128
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szAction[FORWARD_NUMBER_ACTION_SIZE];
	TCHAR szGroup0[FORWARD_NUMBER_GROUP_SIZE];
	TCHAR szGroup1[FORWARD_NUMBER_GROUP_SIZE];
	TCHAR szGroup2[FORWARD_NUMBER_GROUP_SIZE];
}SOCKET_MSG_PUSH_FORWARD_NUMBER;

typedef struct SOCKET_MSG_REPORT_FORWARD_NUMBER_T
{
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szGroup0[FORWARD_NUMBER_GROUP_SIZE];
	TCHAR szGroup1[FORWARD_NUMBER_GROUP_SIZE];
	TCHAR szGroup2[FORWARD_NUMBER_GROUP_SIZE];
}SOCKET_MSG_REPORT_FORWARD_NUMBER;

typedef struct SOCKET_MSG_MOTION_ALERT_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szPicName[URL_SIZE];
}SOCKET_MSG_MOTION_ALERT;

typedef struct SOCKET_MSG_DOOR_MOTION_ALERT_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szMAC[MAC_SIZE];
}SOCKET_MSG_DOOR_MOTION_ALERT;


typedef struct SOCKET_MSG_CONTACT_URL_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szContactUrl[URL_SIZE];
}SOCKET_MSG_CONTACT_URL;

typedef struct SOCKET_MSG_RECONNECT_RPS_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szRPSServer[URL_SIZE];
}SOCKET_MSG_RECONNECT_RPS;

typedef struct SOCKET_MSG_RECONNECT_GATEWAY_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szGateWayServer[URL_SIZE];
}SOCKET_MSG_RECONNECT_GATEWAY;

typedef struct SOCKET_MSG_RECONNECT_ACCESS_SERVER_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szAccessServer[URL_SIZE];
}SOCKET_MSG_RECONNECT_ACCESS_SERVER;

typedef struct SOCKET_MSG_CHECK_DTMF_T
{
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szRemoteSip[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_CHECK_DTMF;

typedef struct SOCKET_MSG_CHECK_DTMF_ACK_T
{
	UINT nSequenceNum;
	UINT nResult;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szRemoteSip[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_CHECK_DTMF_ACK;

typedef struct SOCKET_MSG_DEVICE_CODE_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szDeviceCode[DCLIENT_DEVICE_CODE_SIZE];
}SOCKET_MSG_DEVICE_CODE;

typedef struct SOCKET_MSG_SEND_DTMF_SET_T
{
#define DCLIENT_DTMF_SIZE	10
	UINT nSequenceNum;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	TCHAR szDTMF[DCLIENT_DTMF_SIZE];	
}SOCKET_MSG_SEND_DTMF_SET;

typedef struct SOCKET_MSG_COMMON_TRANSFER_T
{
#define COMMON_TRANSFER_BUF_SIZE 1024
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szMsgType[VALUE_SIZE];
	TCHAR szFrom[DCLIENT_USER_NAME_SIZE];
	TCHAR szTo[DCLIENT_USER_NAME_SIZE];
	UCHAR szCommonBuf[COMMON_TRANSFER_BUF_SIZE];
}SOCKET_MSG_COMMON_TRANSFER;

typedef struct DISCOVER_DEVICE_ADDR_T
{
#define DISCOVER_DEVICE_NAME_SIZE			32
	UINT nType;
	UINT nExtension;
	CHAR szDeviceID[DEVICE_ID_SIZE];
	CHAR szName[DISCOVER_DEVICE_NAME_SIZE];
	CHAR szIP[IP_SIZE];
	CHAR szMac[MAC_SIZE];
	BOOL bUpdated;
	struct DISCOVER_DEVICE_ADDR_T *next;
}DISCOVER_DEVICE_ADDR;

typedef struct SOCKET_MSG_MAINTENANCE_GETLOG_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szServerUrl[URL_SIZE];
	TCHAR szUserName[DCLIENT_USER_NAME_SIZE];
	TCHAR szPasswd[PASSWORD_SIZE];
	TCHAR szFileName[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_MAINTENANCE_GETLOG;

typedef struct SOCKET_MSG_MAINTENANCE_START_PCAP_T
{
	UINT  nDuration;
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szServerUrl[URL_SIZE];
	TCHAR szUserName[DCLIENT_USER_NAME_SIZE];
	TCHAR szPasswd[PASSWORD_SIZE];
	TCHAR szFileName[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_MAINTENANCE_START_PCAP;

typedef struct SOCKET_MSG_MAINTENANCE_STOP_PCAP_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szServerUrl[URL_SIZE];
	TCHAR szUserName[DCLIENT_USER_NAME_SIZE];
	TCHAR szPasswd[PASSWORD_SIZE];
	TCHAR szFileName[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_MAINTENANCE_STOP_PCAP;

typedef struct SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szServerUrl[URL_SIZE];
	TCHAR szUserName[DCLIENT_USER_NAME_SIZE];
	TCHAR szPasswd[PASSWORD_SIZE];
	TCHAR szFileName[DCLIENT_USER_NAME_SIZE];
}SOCKET_MSG_MAINTENANCE_GET_DEVCONFIG;

typedef struct SOCKET_MSG_DOWNUPLOAD_FACE_PIC_T
{
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szUrl[URL_SIZE];
	TCHAR szMD5[MD5_SIZE];
}SOCKET_MSG_DOWNUPLOAD_FACE_PIC;

typedef struct SOCKET_MSG_CLI_COMMAND_T
{
#define CLI_COMMAND_MAX_SIZE 256
	TCHAR szProtocal[PROTOCAL_SIZE];
	TCHAR szCommand[CLI_COMMAND_MAX_SIZE];
}SOCKET_MSG_CLI_COMMAND;

typedef struct SOCKET_MSG_CLI_COMMAND_RESP_T
{
#define CLI_COMMAND_RESP_BUF_SIZE 2048
	TCHAR szSeq[INT_SIZE];
	TCHAR szRespBuf[CLI_COMMAND_RESP_BUF_SIZE];
}SOCKET_MSG_CLI_COMMAND_RESP;

typedef struct SOCKET_MSG_MANAGE_ALARM_MSG_T
{
#define SOCKET_DCLIENT_MANAGE_ALARM_MSG_SIZE	1024
#define SOCKET_DCLIENT_MANAGE_ALARM_APT_SIZE	64
	INT nID;
	UINT unAlarmCode;
	UINT unAlarmLocation;
	UINT unAlarmZone;
	UINT unAlarmCustomize;
	TCHAR szType[SOCKET_DCLIENT_TYPE_SIZE];
	TCHAR szAlarmMsg[SOCKET_DCLIENT_MANAGE_ALARM_MSG_SIZE];
	TCHAR szFromName[DCLIENT_USER_NAME_SIZE];
	TCHAR szApt[SOCKET_DCLIENT_MANAGE_ALARM_APT_SIZE];
	TCHAR szTime[DATETIME_SIZE];
	TCHAR szMAC[MAC_SIZE];
}SOCKET_MSG_MANAGE_ALARM_MSG;

typedef struct SOCKET_MSG_MAINTENANCE_SERVER_CHANGE_T
{
	CHAR szSrvType[VALUE_SIZE];
}SOCKET_MSG_MAINTENANCE_SERVER_CHANGE;

typedef struct SOCKET_MSG_REQUEST_ALL_TRIGGER_T
{
	TCHAR szFromIP[IP_SIZE];
	TCHAR szToIP[IP_SIZE];
}SOCKET_MSG_REQUEST_ALL_TRIGGER;

typedef struct SOCKET_MSG_REPORT_ALL_TRIGGER_T
{
	TCHAR szAlarmChException[VALUE_SIZE];
	TCHAR szFromIP[IP_SIZE];
	TCHAR szToIP[IP_SIZE];
}SOCKET_MSG_REPORT_ALL_TRIGGER;

typedef struct SOCKET_MSG_GSFACE_HTTPAPI_T
{
	TCHAR szURL[URL_SIZE];
}SOCKET_MSG_GSFACE_HTTPAPI;

typedef struct SOCKET_MSG_BACKUP_CONFIG_RECOVERY_T
{
	TCHAR szURL[URL_SIZE];
	TCHAR szMD5[MD5_SIZE];
}SOCKET_MSG_BACKUP_CONFIG_RECOVERY;

#endif
