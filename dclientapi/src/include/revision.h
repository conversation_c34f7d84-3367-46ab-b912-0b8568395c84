#ifndef __REVISION_H__
#define __REVISION_H__

#include "rl_revision.h"


#define	DCLIENT_PLATFORM_TYPE_INDOOR_LINUX	1
#define	DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID	2
#define	DCLIENT_PLATFORM_TYPE_DOOR_LINUX_V3	3
#define	DCLIENT_PLATFORM_TYPE_DOOR_LINUX_GM8138	4
#define	DCLIENT_PLATFORM_TYPE_DOOR_ANDROID	5
#define DCLIENT_PLATFORM_TYPE_DOOR_LINUX_T30	6
#define	DCLIENT_PLATFORM_TYPE_ACCESSCONTROL	7
#define DCLIENT_PLATFORM_TYPE_ACCESSCONTROL_V3	8
#define DCLIENT_PLATFORM_TYPE_DOOR_LINUX_RV1109	9
#define DCLIENT_PLATFORM_TYPE_DOOR_LINUX_SV82X	10


#define DCLIENT_PLATFORM_TYPE	DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID

#define DCLIENT_VERSION					6457
#define MOD_VERSION  "1.0.0.2" 
#define MOD_NAME    "dclientsdk"

/*********************************************
*  New version : 1.0.0.2
*  Old version : 1.0.0.1
*  User 	  : jeffrey
*  Author	  : jeffrey
*  Date 	  : 2023/11/21
*  Reason	  : 
*  Modified   : 新增发送Alarm的接口
*  Affected   :
*********************************************/
/*********************************************
*  New version : 1.0.0.1
*  Old version : 1.0.0.1
*  User 	  : jeffrey
*  Author	  : yujin.kang
*  Date 	  : 2023/XX/XX
*  Reason	  : 
*  Modified   : dclientsdk第一版本（补充的记录）
*  Affected   :
*********************************************/

#if (DCLIENT_PLATFORM_TYPE == DCLIENT_PLATFORM_TYPE_INDOOR_ANDROID)
#define	RL_SUPPORT_SDMC_AD							1
#define RL_SUPPORT_SDMC_REMOTE_PHONEBOOK			1
#define RL_SUPPORT_PUSH_NOANSWER_FWD_NUMBER			0
#define RL_SUPPORT_SEND_PUSH_NOANSWER_FWD_NUMBER	1
#define RL_SUPPORT_CLOUD_DEV_INFO					1
#define RL_SUPPORT_ARMING							1
#define RL_SUPPORT_REPORT_ACTIVITY					0
#define RL_SUPPORT_VRTSP_INDOOR						0
#define RL_SUPPORT_ARMING_P2P						1
#define RL_SUPPORT_DEVICE_HTTP_REGISTER				1  /*设备注册账号功能，使用RESTFUL接口，室内机打开即可*/
#define RL_SUPPORT_DEVICE_MAINTENANCE				1  /*云平台的运维接口，一般都要打开*/
#define RL_SUPPORT_DTMF_SET							0  /*在室内机上设置门口机/梯口机的DTMF，DISCREET提的*/
#define RL_SUPPORT_DOWNUPLOAD_FACE_PIC				0  /*人脸识别相关，只有支持人脸识别的设备需要打开*/
#define RL_SUPPORT_ROBINCALL_SETTING_BY_INDOOR		0  /*在室内机上设置门口机/梯口机的RobinCall功能, DISCREET提的*/
#define RL_SUPPORT_RECV_MOTION_ALERT				1  /*室内机接收和处理MOTION ALERT功能，只有室内机需要开启*/
#define RL_SUPPORT_REPORT_CONFIG_TO_DEVICE			0  /*室内机获取级联设备的配置，为discreet开发的，只需要discreet开启*/
#define RL_SUPPORT_AUTH_CODE						0  /*支持设备鉴权码，所有设备都打开*/
#define RL_SUPPORT_DISCOVER_AUTO_SWITCH				1  /*自组网的信息响应根据连接模式进行开关，默认为1，discreet默认为0*/
#define RL_SUPPORT_FP_DOWNLOAD						0  /*指纹下载协议，目前只需要R2X开启，其他的关闭*/
#define RL_SUPPORT_CONTROL4							0  /*Control4功能*/
#define RL_SUPPORT_MULTICAST_SEND_ASSIGN_LAN		1	/*广播限制有线网卡发送数据*/
#define RL_SUPPORT_ETHERNET_TRANSPORT				0 /*support ethernet transport, please confirm the version of pcaplib-0.5.2 is higher than 1.0.1.0, which changed at July,2019*/
#endif

#endif


